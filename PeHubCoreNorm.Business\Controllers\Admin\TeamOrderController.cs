using Microsoft.AspNetCore.Mvc;

namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 团检订单管理控制器（管理端）
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class TeamOrderController : BaseControllerAuthorize
{
    private readonly IOrderService _orderService;

    public TeamOrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 添加团检订单
    /// </summary>
    /// <param name="input">团检订单输入</param>
    /// <returns></returns>
    [HttpPost("AddTeamOrder")]
    [ActionPermission(ActionType.Button, "添加团检订单", "团检订单管理")]
    public async Task<bool> AddTeamOrder([FromBody] TeamOrderInput input)
    {
        return await _orderService.AddTeamOrder(input);
    }

    /// <summary>
    /// 批量添加团检订单
    /// </summary>
    /// <param name="input">批量团检订单输入</param>
    /// <returns></returns>
    [HttpPost("AddBatchTeamOrder")]
    [ActionPermission(ActionType.Button, "批量添加团检订单", "团检订单管理")]
    public async Task<BatchTeamOrderResult> AddBatchTeamOrder([FromBody] BatchTeamOrderInput input)
    {
        return await _orderService.AddBatchTeamOrder(input);
    }

    /// <summary>
    /// 编辑团检订单
    /// </summary>
    /// <param name="input">团检订单编辑输入</param>
    /// <returns></returns>
    [HttpPost("EditTeamOrder")]
    [ActionPermission(ActionType.Button, "编辑团检订单", "团检订单管理")]
    public async Task<bool> EditTeamOrder([FromBody] TeamOrderEditInput input)
    {
        return await _orderService.EditTeamOrder(input);
    }

    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input">取消团检订单输入</param>
    /// <returns></returns>
    [HttpPost("CancelTeamOrder")]
    [ActionPermission(ActionType.Button, "取消团检订单", "团检订单管理")]
    public async Task<bool> CancelTeamOrder([FromBody] CancelTeamOrderInput input)
    {
        return await _orderService.CancelTeamOrder(input);
    }

    /// <summary>
    /// 获取团检订单分页列表
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns></returns>
    [HttpPost("GetTeamOrderPageList")]
    [ActionPermission(ActionType.Query, "查询团检订单列表", "团检订单管理")]
    public async Task<SqlSugarPagedList<TeamOrderBriefOutput>> GetTeamOrderPageList([FromBody] TeamOrderQueryInput input)
    {
        return await _orderService.GetTeamOrderPageList(input);
    }

    /// <summary>
    /// 根据ID获取团检订单详情
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    [HttpGet("GetTeamOrderById")]
    [ActionPermission(ActionType.Query, "获取团检订单详情", "团检订单管理")]
    public async Task<TeamOrderOutput> GetTeamOrderById([FromQuery] string orderId)
    {
        return await _orderService.GetTeamOrderById(orderId);
    }

    /// <summary>
    /// 获取团检订单统计信息
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">单位体检次数</param>
    /// <returns></returns>
    [HttpGet("GetTeamOrderStatistics")]
    [ActionPermission(ActionType.Query, "获取团检订单统计", "团检订单管理")]
    public async Task<TeamOrderStatisticsOutput> GetTeamOrderStatistics([FromQuery] string companyCode, [FromQuery] int? companyTimes = null)
    {
        return await _orderService.GetTeamOrderStatistics(companyCode, companyTimes);
    }

    /// <summary>
    /// 批量取消团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <param name="cancelReason">取消原因</param>
    /// <returns></returns>
    [HttpPost("BatchCancelTeamOrder")]
    [ActionPermission(ActionType.Button, "批量取消团检订单", "团检订单管理")]
    public async Task<BatchTeamOrderResult> BatchCancelTeamOrder([FromBody] List<string> orderIds, [FromQuery] string cancelReason = "管理员批量取消")
    {
        return await _orderService.BatchCancelTeamOrder(orderIds, cancelReason);
    }

    /// <summary>
    /// 导入团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <returns></returns>
    [HttpPost("ImportTeamOrders")]
    [ActionPermission(ActionType.Button, "导入团检订单", "团检订单管理")]
    public async Task<BatchTeamOrderResult> ImportTeamOrders([FromBody] List<string> orderIds)
    {
        return await _orderService.ImportTeamOrders(orderIds);
    }

    /// <summary>
    /// 删除团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <returns></returns>
    [HttpPost("DeleteTeamOrders")]
    [ActionPermission(ActionType.Button, "删除团检订单", "团检订单管理")]
    public async Task<bool> DeleteTeamOrders([FromBody] List<string> orderIds)
    {
        try
        {
            // 只能删除已取消或已退费的订单
            var orders = await Context.Queryable<TeamOrder>()
                .Where(x => orderIds.Contains(x.Id))
                .ToListAsync();

            var canDeleteOrders = orders.Where(x => x.Status == OrderStatus.已取消 || x.Status == OrderStatus.已退费).ToList();
            
            if (canDeleteOrders.Count != orderIds.Count)
            {
                Unify.SetError("只能删除已取消或已退费的订单");
                return false;
            }

            Context.Ado.BeginTran();

            // 删除订单详情
            await Context.Deleteable<OrderDetail>()
                .Where(x => orderIds.Contains(x.OrderId))
                .ExecuteCommandAsync();

            // 删除订单
            await Context.Deleteable<TeamOrder>()
                .Where(x => orderIds.Contains(x.Id))
                .ExecuteCommandAsync();

            Context.Ado.CommitTran();
            return true;
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            Unify.SetError("删除团检订单失败：" + ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 导出团检订单
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns></returns>
    [HttpPost("ExportTeamOrders")]
    [ActionPermission(ActionType.Query, "导出团检订单", "团检订单管理")]
    public async Task<IActionResult> ExportTeamOrders([FromBody] TeamOrderQueryInput input)
    {
        try
        {
            // 设置大页面大小以获取所有数据
            input.pageSize = 10000;
            input.pageNum = 1;

            var result = await _orderService.GetTeamOrderPageList(input);
            
            // TODO: 实现Excel导出逻辑
            // 这里可以使用EPPlus或其他Excel库来生成Excel文件
            
            return Ok(new { message = "导出功能待实现", count = result.TotalCount });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "导出失败：" + ex.Message });
        }
    }

    /// <summary>
    /// 获取团检订单状态统计
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns></returns>
    [HttpGet("GetStatusStatistics")]
    [ActionPermission(ActionType.Query, "获取状态统计", "团检订单管理")]
    public async Task<Dictionary<string, int>> GetStatusStatistics([FromQuery] string companyCode = null, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var query = Context.Queryable<TeamOrder>()
            .WhereIF(!string.IsNullOrEmpty(companyCode), x => x.CompanyCode == companyCode)
            .WhereIF(startDate.HasValue, x => x.CreateTime >= startDate.Value)
            .WhereIF(endDate.HasValue, x => x.CreateTime <= endDate.Value);

        var orders = await query.ToListAsync();

        return new Dictionary<string, int>
        {
            ["已预约"] = orders.Count(x => x.Status == OrderStatus.已预约),
            ["已支付"] = orders.Count(x => x.Status == OrderStatus.已支付),
            ["已导入"] = orders.Count(x => x.Status == OrderStatus.已导入),
            ["已完成"] = orders.Count(x => x.Status == OrderStatus.已完成),
            ["已取消"] = orders.Count(x => x.Status == OrderStatus.已取消),
            ["已退费"] = orders.Count(x => x.Status == OrderStatus.已退费),
            ["总计"] = orders.Count
        };
    }
}
