﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单数据
/// </summary>
public class OrderList
{
    /// <summary>
    /// 订单Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string MemberId { get; set; }

    /// <summary>
    /// 体检人
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public string CardType { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 性别(1:男 2:女)
    /// </summary>
    public string Sex { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string MarryStatus { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 体检类型
    /// </summary>
    public string PeClsCode { get; set; }

    /// <summary>
    /// 体检时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal TotalPrice { get; set; }
}
