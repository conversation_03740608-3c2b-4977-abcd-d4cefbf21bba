namespace PeHubCoreNorm.RBAC;

/// <summary>
/// </summary>
[ApiExplorerSettings(GroupName = "RBAC")]
[Route("sys/auth")]
public class AuthController : BaseControllerAuthorize
{
    private readonly IAuthService _authService;

    /// <summary>
    /// </summary>
    /// <param name="authService"></param>
    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    /// <summary>
    ///     登陆
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("login")]
    [ActionPermission(ActionType.Button, "LoginIn", "公用部分")] //LoginIn用于日志分类
    public async Task<dynamic> Login(LoginInput input)
    {
        return await _authService.Login(input);
    }

    /// <summary>
    ///     登出
    /// </summary>
    /// <returns></returns>
    [HttpPost("loginOut")]
    [ActionPermission(ActionType.Button, "LoginOut", "公用部分")] //LoginOut用于日志分类
    public async Task LoginOut(LoginOutInput input)
    {
        await _authService.LoginOut(input);
    }

    /// <summary>
    ///     获取图片验证码
    /// </summary>
    /// <returns></returns>
    [HttpGet("getPicCaptcha")]
    [AllowAnonymous]
    [IgnoreLog]
    public async Task<dynamic> GetPicCaptcha()
    {
        return await _authService.GetCaptchaInfo();
    }

    /// <summary>
    ///     获取用户信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("getLoginUser")]
    [ActionPermission(ActionType.Query, "登录时获取用户信息", "公用部分")]
    public async Task<dynamic> GetLoginUser()
    {
        return await _authService.GetLoginUser();
    }
}