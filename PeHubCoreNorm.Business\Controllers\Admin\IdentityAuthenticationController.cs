using Microsoft.AspNetCore.Mvc;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using PeHubCoreNorm.Business.Services.IdentityAuthentication;

namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 身份认证控制器（管理端）
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class IdentityAuthenticationController : BaseControllerAuthorize
{
    private readonly IIdentityAuthenticationService _identityAuthenticationService;

    public IdentityAuthenticationController(IIdentityAuthenticationService identityAuthenticationService)
    {
        _identityAuthenticationService = identityAuthenticationService;
    }

    /// <summary>
    /// 身份认证（管理端）
    /// </summary>
    /// <param name="input">认证输入参数</param>
    /// <returns>认证结果</returns>
    [HttpPost("AuthenticateIdentity")]
    [ActionPermission(ActionType.Query, "身份认证", "身份认证管理")]
    public async Task<IdentityAuthenticationOutput> AuthenticateIdentity([FromBody] IdentityAuthenticationInput input)
    {
        return await _identityAuthenticationService.AuthenticateIdentity(input);
    }

    /// <summary>
    /// 发送短信验证码（管理端）
    /// </summary>
    /// <param name="input">发送短信输入参数</param>
    /// <returns>发送结果</returns>
    [HttpPost("SendSmsCode")]
    [ActionPermission(ActionType.Button, "发送短信验证码", "身份认证管理")]
    public async Task<SendSmsCodeOutput> SendSmsCode([FromBody] SendSmsCodeInput input)
    {
        return await _identityAuthenticationService.SendSmsCode(input);
    }

    /// <summary>
    /// 验证短信验证码（管理端）
    /// </summary>
    /// <param name="tel">电话号码</param>
    /// <param name="code">验证码</param>
    /// <returns>验证结果</returns>
    [HttpGet("VerifySmsCode")]
    [ActionPermission(ActionType.Query, "验证短信验证码", "身份认证管理")]
    public async Task<bool> VerifySmsCode([FromQuery] string tel, [FromQuery] string code)
    {
        return await _identityAuthenticationService.VerifySmsCode(tel, code);
    }

    /// <summary>
    /// 根据证件号码和电话查找已激活的人员（管理端）
    /// </summary>
    /// <param name="idNumber">证件号码</param>
    /// <param name="tel">电话号码</param>
    /// <param name="companyCode">单位编码（可选）</param>
    /// <param name="batchNumber">体检次数（可选）</param>
    /// <returns>人员信息列表</returns>
    [HttpGet("FindActivePersonnel")]
    [ActionPermission(ActionType.Query, "查找已激活人员", "身份认证管理")]
    public async Task<List<PersonnelInfo>> FindActivePersonnel(
        [FromQuery] string idNumber, 
        [FromQuery] string tel, 
        [FromQuery] string companyCode = null, 
        [FromQuery] int? batchNumber = null)
    {
        return await _identityAuthenticationService.FindActivePersonnel(idNumber, tel, companyCode, batchNumber);
    }

    /// <summary>
    /// 验证认证令牌（管理端）
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <returns>人员信息</returns>
    [HttpGet("ValidateAuthToken")]
    [ActionPermission(ActionType.Query, "验证认证令牌", "身份认证管理")]
    public async Task<PersonnelInfo> ValidateAuthToken([FromQuery] string token)
    {
        return await _identityAuthenticationService.ValidateAuthToken(token);
    }
}
