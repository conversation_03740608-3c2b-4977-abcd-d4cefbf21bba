﻿namespace PeHubCoreNorm.RBAC;

public class MenuOutput
{
    public string TypeName { get; set; }

    public string MethodName { get; set; }

    public string MethodPath { get; set; }

    public string MethodCode { get; set; }

    public ActionType AuthType { get; set; }
}

public class MenuApisOut
{
    public string typeName { get; set; }

    public List<ApisOut> ApisOutList { get; set; }
}

public class ApisOut
{
    public string MethodName { get; set; }
    public string MethodPath { get; set; }
}

/// <summary>
///     路由元信息
/// </summary>
public class menu_meta
{
    public menu_meta()
    {
        showLink = true;
    }

    /// <summary>
    ///     菜单名称
    /// </summary>
    public string title { get; set; }

    /// <summary>
    ///     菜单图标
    /// </summary>
    public string icon { get; set; }

    /// <summary>
    ///     菜单名称右侧的额外图标
    /// </summary>
    public string extraIcon { get; set; }

    /// <summary>
    ///     是否显示该菜单
    /// </summary>
    public bool showLink { get; set; }

    /// <summary>
    /// 是否显示父级菜单
    /// </summary>
    //public Boolean showParent { get; set; }

    /// <summary>
    ///     页面级别权限设置
    /// </summary>
    public List<string> roles { get; set; }

    /// <summary>
    ///     按钮级别权限设置
    /// </summary>
    public List<string> auths { get; set; }

    /// <summary>
    /// 是否显示父级菜单
    /// </summary>
    //public Boolean keepAlive { get; set; }

    /// <summary>
    ///     父级排序
    /// </summary>
    public int rank { get; set; }
}

/// <summary>
///     子路由配置项
/// </summary>
public class menu_children
{
	/// <summary>
	///     路由路径
	/// </summary>
	public string path { get; set; }

	/// <summary>
	///     路由名称（必须唯一并且和当前路由component字段对应的页面里用defineOptions包起来的name保持一致）
	/// </summary>
	public string name { get; set; }

	/// <summary>
	///     路由重定向
	/// </summary>
	public string redirect { get; set; }

	/// <summary>
	///     路由元信息
	/// </summary>
	public menu_meta meta { get; set; }
}

public class MenuRoutes
{
	/// <summary>
	///     路由路径
	/// </summary>
	public string path { get; set; }

	/// <summary>
	///     路由名称（必须保持唯一）
	/// </summary>
	public string name { get; set; }

	/// <summary>
	///     路由元信息，通俗来说就是路由上的额外信息
	/// </summary>
	public menu_meta meta { get; set; }

	/// <summary>
	///     子路由配置项
	/// </summary>
	public List<menu_children> children { get; set; }
}