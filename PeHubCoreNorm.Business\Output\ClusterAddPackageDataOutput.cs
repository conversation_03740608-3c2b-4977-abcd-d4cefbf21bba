﻿using NLog.LayoutRenderers;

namespace PeHubCoreNorm.Business.Output;

/// <summary>
/// 套餐加项包返参
/// </summary>
public class ClusterAddPackageDataOutput
{
    /// <summary>
    /// 加项包编码
    /// </summary>
    public string AddPackageCode { get; set; }

    /// <summary>
    /// 加项包名称
    /// </summary>
    public string AddPackageName { get; set; }

    /// <summary>
    /// 可选项目数量
    /// </summary>
    public int OptionalQuantity { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string Introduce { get; set; }

    /// <summary>
    /// 加项包组合数据
    /// </summary>
    public AddPkgCombsData[] AddPkgCombsData { get; set; }
}

/// <summary>
/// 加项包组合数据
/// </summary>
public class AddPkgCombsData
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 组合描述
    /// </summary>
    public string Description { get; set; }
}


