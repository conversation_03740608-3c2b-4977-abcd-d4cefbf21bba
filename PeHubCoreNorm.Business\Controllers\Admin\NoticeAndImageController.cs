﻿using Microsoft.AspNetCore.Authorization;
namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 体检须知和轮播图管理
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class NoticeAndImageController : BaseControllerAuthorize
{
    private readonly INoticeAndImageService _noticeAndImageService;

    public NoticeAndImageController(INoticeAndImageService noticeAndImageService)
    {
        _noticeAndImageService = noticeAndImageService;
    }

    #region 轮播图

    /// <summary>
    /// 获取轮播图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetCarouselImages")]
    [ActionPermission(ActionType.Query, "获取轮播图")]
    public async Task<SqlSugarPagedList<CarouselImage>> GetCarouselImages([FromBody] CarouselImageInput input)
    {
        return await _noticeAndImageService.GetCarouselImages(input);
    }

    /// <summary>
    /// 更新轮播图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateCarouselImages")]
    [ActionPermission(ActionType.Button, "更新轮播图")]
    public async Task<bool> UpdateCarouselImages([FromBody] CarouselImage input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateCarouselImages(input);
        });
    }

    /// <summary>
    /// 更新轮播图状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateCarouselImageStatus")]
    [ActionPermission(ActionType.Button, "更新轮播图状态")]
    public async Task<bool> UpdateCarouselImageStatus([FromBody] CarouselImage input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateCarouselImageStatus(input);
        });
    }

    /// <summary>
    /// 新增轮播图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("AddCarouselImage")]
    [ActionPermission(ActionType.Button, "新增轮播图")]
    public async Task<bool> AddCarouselImage([FromBody] CarouselImage input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.AddCarouselImages(input);
        });
    }

    /// <summary>
    /// 删除轮播图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("DeleteCarouselImage")]
    [ActionPermission(ActionType.Button, "删除轮播图")]
    public async Task<bool> DeleteCarouselImage([FromBody] BaseIdsInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.DeleteCarouselImage(input.Ids);
        });
    }



    #endregion


    #region 体检须知

    /// <summary>
    /// 根据Id获取体检须知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNotices")]
    [ActionPermission(ActionType.Query, "获取体检须知")]
    public async Task<Notice> GetNotices([FromBody] NoticeInput input)
    {
        return await _noticeAndImageService.GetNoticeById(input.id);
    }

    /// <summary>
    /// 获取体检须知(根据TypeCode，Status）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetSqlNotices")]
    [ActionPermission(ActionType.Query, "获取体检须知")]
    public async Task<SqlSugarPagedList<Notice>> GetSqlNotices([FromBody] NoticeInput input)
    {
        return await _noticeAndImageService.GetSqlNotices(input);
    }

    /// <summary>
    /// 获取全部体检须知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetAllNotices")]
    [ActionPermission(ActionType.Query, "获取全部体检须知")]
    public async Task<List<Notice>> GetAllNotices([FromBody] NoticeInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.GetAllNotices();
        });
    }

    [HttpPost("UpdateNoticeStatus")]
    [ActionPermission(ActionType.Button, "根据id修改体检须知的状态")]
    public async Task<bool> UpdateNoticeStatus([FromBody] UpdateStatusInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateNoticeStatus(input);
        });
    }

    /// <summary>
    /// 根据Id更新条件须知内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateNoticeContentById")]
    [ActionPermission(ActionType.Button, "根据Id更新条件须知内容")]
    public async Task<bool> UpdateNoticeContentById([FromBody] UpdateNoticeInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateNoticeContentById(input);
        });
    }

    /// <summary>
    /// 新增体检须知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("AddNotices")]
    [ActionPermission(ActionType.Button, "体检须知")]
    public async Task<bool> AddNotices([FromBody] Notice input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.AddNotices(input);
        });
    }

    #endregion

    #region 资讯

    /// <summary>
    /// 新增资讯
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("AddRealTimeInfos")]
    [ActionPermission(ActionType.Button, "新增资讯")]
    public async Task<bool> AddRealTimeInfos([FromBody] RealTimeInfo input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.AddRealTimeInfos(input);
        });
    }

    /// <summary>
    /// 获取资讯（筛选、分页）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetTimeInfos")]
    [ActionPermission(ActionType.Button, "获取资讯")]
    public async Task<SqlSugarPagedList<RealTimeInfo>>GetTimeInfos([FromBody] RealTimeInfoInput input)
    {
        return await _noticeAndImageService.GetTimeInfos(input);
    }

    /// <summary>
    /// 根据id修改资讯的状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateRealTimeInfoStatus")]
    [ActionPermission(ActionType.Button, "根据id修改资讯的状态")]
    public async Task<bool> UpdateRealTimeInfoStatus([FromBody] UpdateStatusInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateRealTimeInfoStatus(input);
        });
    }

    /// <summary>
    /// 根据id更新资讯的内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateRealTimeInfoContentById")]
    [ActionPermission(ActionType.Button, "根据id更新资讯的内容")]
    public async Task<bool> UpdateRealTimeInfoContentById([FromBody] UpdateContentInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateRealTimeInfoContentById(input);
        });
    }

    /// <summary>
    /// 更新资讯（Title、Status、DiagramUrl、TypeCode）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("UpdateRealTimeInfos")]
    [ActionPermission(ActionType.Button, "根据id更新资讯（Title、Status、DiagramUrl、TypeCode）")]
    public async Task<bool> UpdateRealTimeInfos([FromBody] UpdateRealTimeInfosInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.UpdateRealTimeInfos(input);
        });
    }


    /// <summary>
    /// 删除资讯
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("DeleteRealTimeInfo")]
    [ActionPermission(ActionType.Button, "删除资讯")]
    public async Task<bool> DeleteRealTimeInfo([FromBody] BaseIdsInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _noticeAndImageService.DeleteRealTimeInfo(input.Ids);
        });
    }

    /// <summary>
    /// 根据id获取资讯内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetRealTimeContent")]
    [ActionPermission(ActionType.Query, "根据id获取资讯内容")]
    public async Task<dynamic> GetRealTimeContent([FromBody] ShareDataInput input)
    {
        return await _noticeAndImageService.GetNewRealTimeContent(input.kw);
    }



    #endregion
}
