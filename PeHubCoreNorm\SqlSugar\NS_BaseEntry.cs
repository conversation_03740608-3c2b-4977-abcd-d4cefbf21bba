﻿using Masuit.Tools.Core.Validator;
using SqlSugar;

namespace PeHubCoreNorm;

/// <summary>
///     框架实体基类
/// </summary>
public class NS_BaseEntry : BaseEntity
{
    /// <summary>
    /// 总容量
    /// </summary>
    [SugarColumn(ColumnName = "TotalCapacity", ColumnDescription = "总容量")]
    [MinValue(0)]
    public virtual int TotalCapacity { get; set; }

    /// <summary>
    /// 已用容量
    /// </summary>
    [SugarColumn(ColumnName = "UsedCapacity", ColumnDescription = "已用容量")]
    [MinValue(0)]
    public virtual int UsedCapacity { get; set; } = 0;

    /// <summary>
    /// 可用容量
    /// </summary>
    [SugarColumn(ColumnName = "AvailableCapacity", ColumnDescription = "可用容量")]
    [MinValue(0)]
    public virtual int AvailableCapacity { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    [SugarColumn(ColumnName = "Date", ColumnDescription = "日期")]
    public virtual DateTime Date { get; set; }

    /// <summary>
    /// 星期（1-7）
    /// </summary>
    [SugarColumn(ColumnName = "Week", ColumnDescription = "星期（1-7）")]
    public virtual int Week { get; set; }

    /// <summary>
    /// 是否休假
    /// </summary>
    [SugarColumn(ColumnName = "IsVacation", ColumnDescription = "是否休假")]
    public virtual string IsVacation { get; set; }

    /// <summary>
    /// 关联的时间段ID
    /// </summary>
    [SugarColumn(ColumnName = "TimeSlotID", ColumnDescription = "关联的时间段ID")]
    public virtual string TimeSlotID { get; set; }
}