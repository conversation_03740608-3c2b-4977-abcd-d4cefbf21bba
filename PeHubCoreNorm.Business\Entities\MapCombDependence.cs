﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合依赖关系(b依赖于 肝功1，肝功2，选b则带出肝功1，肝功2)
/// </summary>
[SugarTable(TableName = "MapCombDependence", TableDescription = "组合依赖关系表")]
public class MapCombDependence : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombName", ColumnDescription = "组合名称", Length = 200)]
    public string CombName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "DependOnCombCode", ColumnDescription = "依赖于什么组合编码", Length = 10)]
    public string DependOnCombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "DependOnCombName", ColumnDescription = "依赖于什么组合名称", Length = 200)]
    public string DependOnCombName { get; set; }
}