﻿using Microsoft.AspNetCore.Authorization;
namespace PeHubCoreNorm.Business.AppWeb;

[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/NoticeAndImages")]
[AllowAnonymous]
public class NoticeAndImagesController : BaseController
{
    private readonly INoticeAndImageService _noticeAndImageService;
    public NoticeAndImagesController(INoticeAndImageService noticeAndImageService)
    {
        _noticeAndImageService = noticeAndImageService;
    }
    #region 轮播图和体检须知

    /// <summary>
    /// 获取首页轮播图和资讯
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetIndexInfos")]
    [ActionPermission(ActionType.Query, "获取首页轮播图和资讯")]
    public async Task<dynamic> GetIndexInfos([FromBody] IndexInfoInput input)
    {
        var model = new
        {
            carouselImages = await _noticeAndImageService.GetCarouselImagesByPage(input.imagePage),
            realTimeInfos= await _noticeAndImageService.GetNewRealTimeInfos(input.typeCode, input.num)
        };
        return model;
    }




    /// <summary>
    /// 获取轮播图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetCarouselImagesByPage")]
    [ActionPermission(ActionType.Query, "获取轮播图")]
    public async Task<dynamic> GetCarouselImagesByPage([FromBody] CarouselImageByPageInput input)
    {
        return await _noticeAndImageService.GetCarouselImagesByPage(input.imagePage);
    }

    /// <summary>
    /// 获取体检须知(根据TypeCode）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNoticesByTypeCode")]
    [ActionPermission(ActionType.Query, "根据TypeCode获取体检须知")]
    public async Task<Object?> GetNoticesByTypeCode([FromBody] ShareDataInput input)
    {
        var model= await _noticeAndImageService.GetNoticesByTypeCode(input.code);
        return model != null ? new
        {
            Content = model.Content,
            Id = model.Id
        } : model;
    }

    #endregion

    #region 资讯
    /// <summary>
    /// 获取num个最新资讯信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNewRealTimeInfos")]
    [ActionPermission(ActionType.Query, "获取num个最新资讯信息")]
    public async Task<dynamic> GetNewRealTimeInfos([FromBody] RealTimeInfosHomeInput input)
    {
        return await _noticeAndImageService.GetNewRealTimeInfos(input.typeCode,input.num);
    }

    /// <summary>
    /// 根据id获取资讯内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNewRealTimeContent")]
    [ActionPermission(ActionType.Query, "根据id获取资讯内容")]
    public async Task<dynamic> GetNewRealTimeContent([FromBody] ShareDataInput input)
    {
        return await _noticeAndImageService.GetNewRealTimeContent(input.kw);
    }

    #endregion
}