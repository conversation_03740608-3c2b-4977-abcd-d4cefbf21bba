namespace PeHubCoreNorm.Business.Utils;

/// <summary>
/// 图片处理工具类
/// </summary>
public static class ImageUtils
{
    /// <summary>
    /// 验证并转换Base64字符串为字节数组
    /// </summary>
    /// <param name="base64String">Base64字符串</param>
    /// <returns>字节数组</returns>
    /// <exception cref="ArgumentException">格式错误时抛出异常</exception>
    public static byte[] ConvertBase64ToBytes(string base64String)
    {
        if (string.IsNullOrEmpty(base64String))
        {
            throw new ArgumentException("Base64字符串不能为空");
        }

        try
        {
            // 如果包含data:image前缀，需要去掉
            string cleanBase64 = base64String;
            if (base64String.Contains(","))
            {
                cleanBase64 = base64String.Split(',')[1];
            }

            return Convert.FromBase64String(cleanBase64);
        }
        catch (Exception ex)
        {
            throw new ArgumentException("无效的Base64字符串格式", ex);
        }
    }

    /// <summary>
    /// 根据字节数组判断图片MIME类型
    /// </summary>
    /// <param name="imageBytes">图片字节数组</param>
    /// <returns>MIME类型</returns>
    public static string GetImageMimeType(byte[] imageBytes)
    {
        if (imageBytes == null || imageBytes.Length < 4)
        {
            return "image/jpeg"; // 默认类型
        }

        // PNG文件头：89 50 4E 47
        if (imageBytes[0] == 0x89 && imageBytes[1] == 0x50 && 
            imageBytes[2] == 0x4E && imageBytes[3] == 0x47)
            return "image/png";

        // GIF文件头：47 49 46 38
        if (imageBytes[0] == 0x47 && imageBytes[1] == 0x49 && 
            imageBytes[2] == 0x46 && imageBytes[3] == 0x38)
            return "image/gif";

        // WebP文件头：52 49 46 46
        if (imageBytes[0] == 0x52 && imageBytes[1] == 0x49 && 
            imageBytes[2] == 0x46 && imageBytes[3] == 0x46)
            return "image/webp";

        // JPEG文件头：FF D8 FF
        if (imageBytes[0] == 0xFF && imageBytes[1] == 0xD8 && imageBytes[2] == 0xFF)
            return "image/jpeg";

        // BMP文件头：42 4D
        if (imageBytes[0] == 0x42 && imageBytes[1] == 0x4D)
            return "image/bmp";

        return "image/jpeg"; // 默认类型
    }

    /// <summary>
    /// 验证图片格式是否支持
    /// </summary>
    /// <param name="imageBytes">图片字节数组</param>
    /// <returns>是否支持</returns>
    public static bool IsSupportedImageFormat(byte[] imageBytes)
    {
        var mimeType = GetImageMimeType(imageBytes);
        return mimeType == "image/jpeg" || 
               mimeType == "image/png" || 
               mimeType == "image/gif" || 
               mimeType == "image/webp";
    }

    /// <summary>
    /// 验证图片大小是否在允许范围内
    /// </summary>
    /// <param name="imageBytes">图片字节数组</param>
    /// <param name="maxSizeInBytes">最大大小（字节）</param>
    /// <returns>是否在允许范围内</returns>
    public static bool IsValidImageSize(byte[] imageBytes, int maxSizeInBytes = 5 * 1024 * 1024) // 默认5MB
    {
        return imageBytes != null && imageBytes.Length > 0 && imageBytes.Length <= maxSizeInBytes;
    }

    /// <summary>
    /// 获取图片格式名称
    /// </summary>
    /// <param name="imageBytes">图片字节数组</param>
    /// <returns>格式名称</returns>
    public static string GetImageFormatName(byte[] imageBytes)
    {
        var mimeType = GetImageMimeType(imageBytes);
        return mimeType switch
        {
            "image/jpeg" => "JPEG",
            "image/png" => "PNG",
            "image/gif" => "GIF",
            "image/webp" => "WebP",
            "image/bmp" => "BMP",
            _ => "Unknown"
        };
    }
}
