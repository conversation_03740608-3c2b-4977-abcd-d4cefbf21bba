﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合返参
/// </summary>
public class CodeItemCombOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 独立放号
    /// </summary>
    public bool IsPriority { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    public string ClsCode { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    public string ClsName { get; set; }

    /// <summary>
    /// 组合描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string Attention { get; set; }
}
