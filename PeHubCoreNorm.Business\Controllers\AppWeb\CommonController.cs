﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Http;
using PeHubCoreNorm.RBAC;
using PeHubCoreNorm.System;
using PeHubCoreNorm.Utils.IOUtils;
using System.IO;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///   通用功能
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/common")]
[AllowAnonymous]
public class CommonController : BaseController
{
	private readonly ISysOrgService _orgService;

	private readonly IDictService _dictService;

	/// <summary>
	/// 构造
	/// </summary>
	/// <param name="orgService"></param>
	/// <param name="dictService"></param>
	public CommonController(ISysOrgService orgService, IDictService dictService)
	{
		_orgService = orgService;
		_dictService = dictService;
	}

	[HttpGet("test")]
	[ActionPermission(ActionType.Query, "测试")]
	public async Task<dynamic> test()
	{
		var data = SM2CryptoUtil.GetKey();
		return data;
	}

	/// <summary>
	///   医院机构详情
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpGet("getOrgDetail")]
	[ActionPermission(ActionType.Query, "医院机构详情", "通用功能")]
	public async Task<dynamic> getOrgDetail([FromQuery] BaseIdInput input)
	{
		return await _orgService.GetSysOrgById(input.Id);
	}


	/// <summary>
	///  获取业务字典树
	/// </summary>
	/// <returns></returns>
	[HttpGet("getDictTree")]
	[IgnoreLog]
	public async Task<dynamic> getDictTree([FromQuery] DictTreeInput input)
	{
		return await _dictService.Tree(input);
	}


	/// <summary>
	///   医院机构列表
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpGet("getOrgInfoListByOrgkey")]
	[ActionPermission(ActionType.Query, "医院机构列表", "通用功能")]
	public async Task<dynamic> getOrgInfoListByOrgkey([FromQuery] BaseIdInput input)
	{
		return await _orgService.GetOrgInfoListByOrgkey(input.Id);
	}

	 

} 