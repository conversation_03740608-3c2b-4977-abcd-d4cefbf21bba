﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Http;
using PeHubCoreNorm.RBAC;
using PeHubCoreNorm.Utils.IOUtils;
using System.IO;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///   通用功能
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/common")]
[AllowAnonymous]
public class CommonController : BaseController
{
	private readonly ISysOrgService _orgService;

	/// <summary>
	/// 构造
	/// </summary>
	/// <param name="orgService"></param>
	public CommonController(ISysOrgService orgService)
	{
		_orgService = orgService;
	}

	[HttpGet("test")]
	[ActionPermission(ActionType.Query, "测试")]
	public async Task<dynamic> test()
	{
		var data = SM2CryptoUtil.GetKey();
		return data;
	}

	/// <summary>
	///   医院机构详情
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpGet("getOrgDetail")]
	[ActionPermission(ActionType.Query, "医院机构详情", "通用功能")]
	public async Task<dynamic> getOrgDetail([FromQuery] BaseIdInput input)
	{
		return await _orgService.GetSysOrgById(input.Id);
	}


} 