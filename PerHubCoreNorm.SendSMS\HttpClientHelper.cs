﻿using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using PeHubCoreNorm;

#pragma warning disable CS8603 // 可能返回 null 引用。

namespace PerHubCoreNorm.SendSMS;

/// <summary>
///     HTTP帮助类
/// </summary>
public class HttpClientHelper : ITransient
{
    private readonly IHttpClientFactory _httpClientFactory;

    public HttpClientHelper(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    ///     发起GET异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> GetAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (var client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
                foreach (var key in headers.Keys)
                    client.DefaultRequestHeaders.Add(key, headers[key]);

            using (var response = await client.GetAsync(url))
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }

                return string.Empty;
            }
        }
    }


    /// <summary>
    ///     发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> PostAsync(string url, string body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (var client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
                foreach (var key in headers.Keys)
                    client.DefaultRequestHeaders.Add(key, headers[key]);

            var content = new StringContent(body, Encoding.UTF8, bodyMediaType);
            if (!string.IsNullOrWhiteSpace(responseContentType))
                content.Headers.ContentType = MediaTypeHeaderValue.Parse(responseContentType);

            using (var response = await client.PostAsync(url, content))
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }

                return string.Empty;
            }
        }
    }

    /// <summary>
    ///     发起POST异步请求
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> PutAsync(string url, string body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (var client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
                foreach (var key in headers.Keys)
                    client.DefaultRequestHeaders.Add(key, headers[key]);

            var content = new StringContent(body, Encoding.UTF8, bodyMediaType);
            if (!string.IsNullOrWhiteSpace(responseContentType))
                content.Headers.ContentType = MediaTypeHeaderValue.Parse(responseContentType);

            using (var response = await client.PutAsync(url, content))
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }

                return string.Empty;
            }
        }
    }

    /// <summary>
    ///     发起GET异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回string</returns>
    public async Task<string> DeleteAsync(string url, Dictionary<string, string> headers = null, int timeOut = 30)
    {
        var hostName = GetHostName(url);
        using (var client = _httpClientFactory.CreateClient(hostName))
        {
            client.Timeout = TimeSpan.FromSeconds(timeOut);
            if (headers?.Count > 0)
                foreach (var key in headers.Keys)
                    client.DefaultRequestHeaders.Add(key, headers[key]);

            using (var response = await client.DeleteAsync(url))
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    return responseString;
                }

                return string.Empty;
            }
        }
    }

    /// <summary>
    ///     发起GET异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> GetAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30)
        where T : new()
    {
        var responseString = await GetAsync(url, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
            return JsonSerializer.Deserialize<T>(responseString);
        return default;
    }


    /// <summary>
    ///     发起POST异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> PostAsync<T>(string url, string body,
        string bodyMediaType = "application/json",
        string responseContentType = "application/json",
        Dictionary<string, string> headers = null,
        int timeOut = 30) where T : new()
    {
        var responseString = await PostAsync(url, body, bodyMediaType, responseContentType, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
            return JsonSerializer.Deserialize<T>(responseString);
        return default;
    }

    /// <summary>
    ///     发起PUT异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="body">POST提交的内容</param>
    /// <param name="bodyMediaType">POST内容的媒体类型，如：application/xml、application/json</param>
    /// <param name="responseContentType">HTTP响应上的content-type内容头的值,如：application/xml、application/json、application/text、application/x-www-form-urlencoded等</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> PutAsync<T>(string url, string body,
        string bodyMediaType = null,
        string responseContentType = null,
        Dictionary<string, string> headers = null,
        int timeOut = 30) where T : new()
    {
        var responseString = await PutAsync(url, body, bodyMediaType, responseContentType, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
            return JsonSerializer.Deserialize<T>(responseString);
        return default;
    }

    /// <summary>
    ///     发起DELETE异步请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="headers">请求头信息</param>
    /// <param name="timeOut">请求超时时间，单位秒</param>
    /// <returns>返回T</returns>
    public async Task<T> DeleteAsync<T>(string url, Dictionary<string, string> headers = null, int timeOut = 30)
        where T : new()
    {
        var responseString = await DeleteAsync(url, headers, timeOut);
        if (!string.IsNullOrWhiteSpace(responseString))
            return JsonSerializer.Deserialize<T>(responseString);
        return default;
    }

    #region 私有函数

    /// <summary>
    ///     获取请求的主机名
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    private static string GetHostName(string url)
    {
        if (!string.IsNullOrWhiteSpace(url))
            return url.Replace("https://", "").Replace("http://", "").Split('/')[0];
        return "AnyHost";
    }

    #endregion
}