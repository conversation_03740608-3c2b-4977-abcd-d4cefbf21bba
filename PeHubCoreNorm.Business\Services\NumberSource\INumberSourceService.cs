﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 号源管理业务接口
/// </summary>
public interface INumberSourceService : ITransient, ITenantDBTransient
{

    #region 时段管理    

    /// <summary>
    /// 获取全部号源时段
    /// </summary>
    /// <returns></returns>
    Task<TimeSlotOutput[]> GetTimeSlot();

    /// <summary>
    /// 获取所有时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<TimeSlotEntryOutput[]> GetTimeSlotEntry();

    /// <summary>
    /// 添加时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> AddTimeSlotEntry(AddTimeSlotEntryInput input);

    /// <summary>
    /// 编辑时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> EditTimeSlotEntry(EditTimeSlotEntryInput input);

    /// <summary>
    /// 删除时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> DeleteTimeSlotEntry(DeleteTimeSlotEntryInput input);

    #endregion

    #region 号源类型关联时段

    /// <summary>
    /// 获取全部号源类型
    /// </summary>
    /// <returns></returns>
    Task<SourceTypeOutput[]> GetSourceType();

    /// <summary>
    /// 通过号源类型获取已关联时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<TimeSlotOutput[]> GetTimeSlotByType(TimeSlotInput input);

    /// <summary>
    /// 编辑号源类型对应时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> EditTimeSlotByType(EditTimeSlotByTypeInput input);

    /// <summary>
    /// 删除号源类型时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> DeleteTimeSlotByType(DeleteTimeSlotByTypeInput input);

    #endregion

    #region 号源管理
    /// <summary>
    /// 获取个检类型
    /// </summary>
    /// <returns></returns>
    Task<PersonNumberSourceOutput[]> GetPersonType();

    /// <summary>
    /// 获取号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<NumberSourceOutput[]> GetNumberSource(NumberSourceInput input);

    /// <summary>
    /// 获取某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<NumberSourceOutput[]> GetNumberSourceByDate(NumberSourceByDayInput input);

    /// <summary>
    /// 编辑号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> EditNumberSource(List<EditNumberSourceInput> input);

    /// <summary>
    /// 删除/清空号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> DeleteNumberSource(DeleteNumberSourceInput input);

    /// <summary>
    /// 设置号源休假
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> SetNumberSourceVacation(List<SetNumberSourceVacationInput> input);

    #endregion

    /// <summary>
    /// 更新个检号源
    /// </summary>
    /// <param name="beginTime"></param>
    /// <param name="sourceTypeID"></param>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    Task<bool> UpdatePersonNumberSource(DateTime beginTime, string sourceTypeID, string timeSlotID);

    /// <summary>
    /// 获取个检某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<NumberSourceOutput[]> GetPersonNumberSourceByDate(NumberSourceByDayInput input);
}