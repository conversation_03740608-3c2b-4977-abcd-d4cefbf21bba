﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合依赖关系参数
/// </summary>
public class MapCombDependenceInput
{
    
}

/// <summary>
/// 组合依赖关系明细参数
/// </summary>
public class CombDependenceDetailInput
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 依赖于组合编码
    /// </summary>
    public string DependOnCombCode { get; set; }

    /// <summary>
    /// 依赖于组合名称
    /// </summary>
    public string DependOnCombName { get; set; }
}
