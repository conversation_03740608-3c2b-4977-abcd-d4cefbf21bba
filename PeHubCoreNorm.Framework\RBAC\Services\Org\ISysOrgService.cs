﻿namespace PeHubCoreNorm.RBAC;

public interface ISysOrgService : ITransient
{
    
    Task<SqlSugarPagedList<SysOrg>> Page(OrgPageInput input);

	Task<SysOrg> GetSysOrgById(string Id);
	Task Add(OrgAddInput input);

    Task Edit(OrgEditInput input);

    Task Delete(BaseIdsInput input);
	Task<List<OrgOutPut>> GetOrgList(string userid,bool issupadmin);

	Task<string> UpdateAvatar(BaseFileInput input);

	Task<SysOrg> GetSysOrgByCode(string code, string arrcode = "");
}