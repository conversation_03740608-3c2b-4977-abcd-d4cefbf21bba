﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 扫码付订单表
    /// </summary>
    [SugarTable("Order_ScanPay", TableDescription = "扫码付订单表")]
    public class Order_ScanPay : BaseEntity
    {
        /// <summary>
        /// 体检订单编码
        /// </summary>
        [SugarColumn(ColumnName = "RegNo", ColumnDescription = "体检订单编码")]
        public virtual string RegNo { get; set; }

        /// <summary>
        /// 订单唯一编码（标识）
        /// </summary>
        [SugarColumn(ColumnName = "UniqueId", ColumnDescription = "订单唯一编码（标识）")]
        public virtual string UniqueId { get; set; }        

        /// <summary>
        /// 订单价格
        /// </summary>
        [SugarColumn(ColumnName = "Price", ColumnDescription = "订单价格")]
        public virtual decimal Price { get; set; }

        /// <summary>
        /// 该笔订单同步次数
        /// </summary>
        [SugarColumn(ColumnName = "SyncCount", ColumnDescription = "该笔订单同步次数")]
        public virtual int SyncCount { get; set; } = 0;

        /// <summary>
        /// 订单状态
        /// </summary>
        [SugarColumn(ColumnName = "Statu", ColumnDescription = "订单状态", Length = 20)]
        public virtual string Statu { get; set; }
    }
}
