﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <InvariantGlobalization>true</InvariantGlobalization>
        <Nullable>disable</Nullable>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="DotNetCore.CAP.InMemoryStorage" Version="8.0.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
        <PackageReference Include="Hangfire.Core" Version="1.8.20" />
        <PackageReference Include="Hangfire.SqlServer" Version="1.8.20" />
        <PackageReference Include="IPTools.China" Version="1.6.0" />
        <PackageReference Include="Lazy.Captcha.Core" Version="2.0.7" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Masuit.Tools.Core" Version="2025.3.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.1" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="9.0.6" />
        <PackageReference Include="NewLife.MQTT" Version="1.4.2024.101" />
        <PackageReference Include="NewLife.Redis" Version="5.6.2024.105" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
        <PackageReference Include="Npgsql" Version="9.0.3" />
        <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
        <PackageReference Include="RecyclableMemoryStream" Version="1.0.0" />
        <PackageReference Include="Savorboard.CAP.InMemoryMessageQueue" Version="8.0.0" />
        <PackageReference Include="Scrutor" Version="4.2.2" />
        <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Mvc" Version="1.4.0" />
        <PackageReference Include="SqlSugarCore" Version="*********" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
        <PackageReference Include="RazorEngineCore" Version="2023.11.2" />
        <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
        <PackageReference Include="UAParser" Version="3.1.47" />
    </ItemGroup>

</Project>
