﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 互斥组合分页查询
/// </summary>
public class MutexCombsInput
{
    /// <summary>
    /// 关键字
    /// </summary>
    public string SearchKey { get; set; }
}

/// <summary>
/// 互斥组合明细入参
/// </summary>
public class MutexCombDetailInput
{
    /// <summary>
    /// 互斥代码（同属一个互斥代码中的组合相斥）
    /// </summary>        
    public string MutexCode { get; set; }

    /// <summary>
    /// 互斥名称
    /// </summary>        
    public string MutexName { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    public string CombName { get; set; }
}