﻿namespace PeHubCoreNorm.RBAC;

public interface ISysMenuService : ITransient
{
    Task<List<SysMenu>> GetOwnMenus(string id);

    Task Add(MenuAddInput input);

    List<SysMenu> ConstructMenuTrees(List<SysMenu> resourceList, string parentId = PeHubCoreNormConst.Zero);

    Task<List<SysMenu>> GetListByCategory(string category);

    Task<List<SysMenu>> Tree(MenuTreeInput input);

    Task Edit(MenuEditInput input);

    Task Delete(BaseIdsInput input);

    Task ChangeModule(MenuChangeModuleInput input);

    Task ChangeApiList(MenuAuthInput input);


    List<MenuApisOut> GetAuthCodeAttributes();

    Task<List<MenuTreeSelector>> ResourceTreeSelector();
}