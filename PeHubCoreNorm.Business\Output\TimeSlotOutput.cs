﻿namespace PeHubCoreNorm.Business;

public class TimeSlotOutput
{
    /// <summary>
    /// 时段ID
    /// </summary>
    public string TimeSlotEntryID { get; set; }
    /// <summary>
    /// 时段全称
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    /// 上下午标识
    /// </summary>
    public string TimePeriod { get; set; }

    /// <summary>
    ///号源类型名称
    /// </summary>
    public string SourceTypeName { get; set; }

    /// <summary>
    /// 时段启用状态
    /// </summary>
    public string Statu { get; set; }
}

public class TimeSlotEntryOutput
{
    /// <summary>
    /// 时段ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 时段全称
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    /// 范围标识
    /// </summary>
    public string TimePeriod { get; set; }
}

public class SourceTypeOutput
{
    /// <summary>
    /// 号源类型编码
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// 号源类型名称
    /// </summary>
    public string SourceTypeName { get; set; }

    /// <summary>
    /// 当前类型号源存放的表
    /// </summary>
    public string SourceTypeTable { get; set; }

    /// <summary>
    /// 当前号源类型的父级ID
    /// </summary>
    public string SourceTypeParentId { get; set; }
}