﻿using Microsoft.AspNetCore.Authorization;
using PeHubCoreNorm.Business.Output;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///  套餐组合业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class ClusterCombController : BaseControllerAuthorize
{
    private readonly IBasicCodeService _basicCodeService;
    private readonly IClusterCombService _clusterCombService;

    public ClusterCombController(IBasicCodeService basicCodeService, IClusterCombService clusterCombService)
    {
        _basicCodeService = basicCodeService;
        _clusterCombService = clusterCombService;
    }

    /// <summary>
    /// 体检分类查询
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetCodePeCls")]
    [ActionPermission(ActionType.Query, "体检分类查询", "APP端用户业务")]
    public async Task<PeCls[]> GetCodePeCls()
    {
        return await _basicCodeService.GetCodePeCls(true);
    }

    /// <summary>
    /// 获取按项目分类分组的组合
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetCombsByItemClsGroup")]
    [ActionPermission(ActionType.Query, "获取按项目分类分组的组合", "APP端用户业务")]
    public async Task<ItemClsCombOutput[]> GetCombsByItemClsGroup()
    {
        return await _basicCodeService.GetCombsByItemClsGroup();
    }

    /// <summary>
    /// 获取套餐列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterList")]
    [ActionPermission(ActionType.Button, "GetClusterList", "APP端用户业务")]
    public async Task<CodeClusterOutput[]> GetClusterList([FromBody] ClusterQuery query)
    {
        return await _clusterCombService.GetClusterList(query);
    }

    /// <summary>
    /// 获取套餐包含的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterCombs")]
    [ActionPermission(ActionType.Query, "GetClusterCombs", "APP端用户业务")]
    public async Task<ClusterClsCombs[]> GetClusterCombs([FromBody] MapClusterCombInput query)
    {
        return await _clusterCombService.GetClusterCombs(query);
    }

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetMapClusterExtraComb")]
    [ActionPermission(ActionType.Query, "GetMapClusterExtraComb", "APP端用户业务")]
    public async Task<ClusterClsCombs[]> GetMapClusterExtraComb([FromBody] MapClusterExtraCombInput query)
    {
        return await _clusterCombService.GetMapClusterExtraComb(query);
    }

    /// <summary>
    /// 获取个检套餐加项包
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterAddPackage")]
    [ActionPermission(ActionType.Query, "GetClusterAddPackage", "APP端用户业务")]
    public async Task<ClusterAddPackageDataOutput[]> GetClusterAddPackage([FromBody] ClusterAddPackageInput query)
    {
        return await _clusterCombService.GetClusterAddPackage(query);
    }

    /// <summary>
    /// 获取项目的相关关系
    /// </summary>
    /// <param name="comb"></param>
    /// <returns></returns>
    [HttpPost("GetCombRelation")]
    [ActionPermission(ActionType.Query, "GetCombRelation", "APP端用户业务")]
    public async Task<ComRelationOutput> GetCombRelation([FromBody] CombRelation comb)
    {
        return await _clusterCombService.GetCombRelation(comb);
    }

    /// <summary>
    /// 计算项目加项
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    [HttpPost("CalCulateCombsAsync")]
    [AllowAnonymous]
    [ActionPermission(ActionType.Button, "CalCulateCombsAsync", "APP端用户业务")]
    public async Task<CalCulateCombs> CalCulateCombsAsync([FromBody] CalCulateCombs calcCombs)
    {
        return await _clusterCombService.CalCulateCombsAsync(calcCombs);
    }

    /// <summary>
    /// 检验项目关系是否通过
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    [HttpPost("VerifyCombIsPass")]
    [AllowAnonymous]
    [ActionPermission(ActionType.Button, "VerifyCombIsPass", "APP端用户业务")]
    public async Task<bool> VerifyCombIsPass([FromBody] List<ReturnCombs> calcCombs)
    {
        return await _clusterCombService.VerifyCombIsPass(calcCombs);
    }
}