﻿using Microsoft.AspNetCore.Authorization;
using PeHubCoreNorm.Business.Output;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///  套餐组合业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
[AllowAnonymous]
public class ClusterCombController : BaseControllerAuthorize
{
    private readonly IBasicCodeService _basicCodeService;
    private readonly IClusterCombService _clusterCombService;

    public ClusterCombController(IBasicCodeService basicCodeService, IClusterCombService clusterCombService)
    {
        _basicCodeService = basicCodeService;
        _clusterCombService = clusterCombService;
    }

    /// <summary>
    /// 体检分类查询
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetCodePeCls")]
    [ActionPermission(ActionType.Query, "体检分类查询", "基础业务")]
    public async Task<PeCls[]> GetCodePeCls()
    {
        return await _basicCodeService.GetCodePeCls(true);
    }

    /// <summary>
    /// 获取套餐列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterList")]
    [ActionPermission(ActionType.Button, "GetClusterList", "APP端用户业务")]
    public async Task<CodeClusterOutput[]> GetClusterList([FromBody] ClusterQuery query)
    {
        return await _clusterCombService.GetClusterList(query);
    }

    /// <summary>
    /// 获取套餐包含的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterCombs")]
    [ActionPermission(ActionType.Button, "GetClusterCombs", "APP端用户业务")]
    public async Task<ClusterClsCombs[]> GetClusterCombs([FromBody] MapClusterCombInput query)
    {
        return await _clusterCombService.GetClusterCombs(query);
    }

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetMapClusterExtraComb")]
    [ActionPermission(ActionType.Button, "GetMapClusterExtraComb", "APP端用户业务")]
    public async Task<ClusterClsCombs[]> GetMapClusterExtraComb([FromBody] MapClusterExtraCombInput query)
    {
        return await _clusterCombService.GetMapClusterExtraComb(query);
    }

    /// <summary>
    /// 获取个检套餐加项包
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetClusterAddPackage")]
    [ActionPermission(ActionType.Button, "GetClusterAddPackage", "APP端用户业务")]
    public async Task<ClusterAddPackageDataOutput[]> GetClusterAddPackage([FromBody] ClusterAddPackageInput query)
    {
        return await _clusterCombService.GetClusterAddPackage(query);
    }


}
