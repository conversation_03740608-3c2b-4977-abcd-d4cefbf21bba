﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <InvariantGlobalization>true</InvariantGlobalization>
        <Nullable>disable</Nullable>
        <LangVersion>latest</LangVersion>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>


    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\PeHubCoreNorm.Plugins\PeHubCoreNorm.Services.csproj" />
        <ProjectReference Include="..\PeHubCoreNorm\PeHubCoreNorm.csproj" />
    </ItemGroup>
</Project>
