﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 个检号源信息表
    /// </summary>
    [SugarTable("NS_IndividualSource", TableDescription = "个检号源信息表")]
    public class NS_IndividualSource : NS_BaseEntry
    {
        /// <summary>
        /// 个检类型（入职、健康证、驾驶证等）
        /// </summary>
        [SugarColumn(ColumnName = "SourceTypeID", ColumnDescription = "个检类型")]
        public virtual string SourceTypeID { get; set; }        
    }
}
