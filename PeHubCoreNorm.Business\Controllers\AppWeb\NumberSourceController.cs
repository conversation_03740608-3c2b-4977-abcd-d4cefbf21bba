﻿namespace PeHubCoreNorm.Business.Controllers.AppWeb;

/// <summary>
/// 号源业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class NumberSourceController : BaseControllerAuthorize
{
    private readonly INumberSourceService _numberSourceService;

    public NumberSourceController(INumberSourceService numberSourceService)
    {
        _numberSourceService = numberSourceService;
    }

    /// <summary>
    /// 获取全部号源类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("GetSourceType")]
    [ActionPermission(ActionType.Query, "获取全部号源类型")]
    public async Task<SourceTypeOutput[]> GetSourceType()
    {
        return await _numberSourceService.GetSourceType();
    }

    /// <summary>
    /// 号源查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNumberSource")]
    [ActionPermission(ActionType.Query, "号源查询")]
    public async Task<NumberSourceOutput[]> GetNumberSource([FromBody] NumberSourceInput input)
    {
        return await _numberSourceService.GetNumberSource(input);
    }

    /// <summary>
    /// 获取某一天的号源（分时段）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetNumberSourceByDate")]
    [ActionPermission(ActionType.Query, "获取某一天的号源")]
    public async Task<NumberSourceOutput[]> GetNumberSourceByDate([FromBody] NumberSourceByDayInput input)
    {
        return await _numberSourceService.GetNumberSourceByDate(input);
    }
}
