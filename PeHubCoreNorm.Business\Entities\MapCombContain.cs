﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合包含关系(肝功4 = 肝功1,肝功2,肝功3，则选了1,2,3就升级为肝功4)
/// </summary>
[SugarTable(TableName = "MapCombContain", TableDescription = "组合包含关系")]
public class MapCombContain : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombName", ColumnDescription = "组合名称", Length = 200)]
    public string CombName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ChildCombCode", ColumnDescription = "子组合编码", Length = 10)]
    public string ChildCombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ChildCombName", ColumnDescription = "子组合名称", Length = 200)]
    public string ChildCombName { get; set; }
}
