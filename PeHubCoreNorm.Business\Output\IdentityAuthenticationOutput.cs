namespace PeHubCoreNorm.Business.Output;

/// <summary>
/// 身份认证结果输出
/// </summary>
public class IdentityAuthenticationOutput
{
    /// <summary>
    /// 认证是否成功
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 认证消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 符合身份的人员信息列表
    /// </summary>
    public List<PersonnelInfo> PersonnelList { get; set; } = new List<PersonnelInfo>();

    /// <summary>
    /// 认证令牌（可选，用于后续操作）
    /// </summary>
    public string AuthToken { get; set; }
}

/// <summary>
/// 人员信息
/// </summary>
public class PersonnelInfo
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 员工姓名
    /// </summary>
    public string EmployeeName { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IdNumber { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public int IdNumberType { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string PackAgeCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string PackAgeName { get; set; }

    /// <summary>
    /// 体检次数
    /// </summary>
    public int BatchNumber { get; set; }

    /// <summary>
    /// 有效起始日期
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 有效终止日期
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 状态（0：未激活，1：已激活）
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText => Status == 1 ? "已激活" : "未激活";

    /// <summary>
    /// 是否在有效期内
    /// </summary>
    public bool IsValid => DateTime.Now >= StartTime && DateTime.Now <= EndTime;
}

/// <summary>
/// 发送短信验证码结果
/// </summary>
public class SendSmsCodeOutput
{
    /// <summary>
    /// 是否发送成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 验证码有效期（分钟）
    /// </summary>
    public int ValidMinutes { get; set; } = 5;
}
