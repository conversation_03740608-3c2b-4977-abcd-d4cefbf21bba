﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    [SugarTable("SourceTypes", TableDescription = "体检类型信息表")]
    public class NS_SourceTypes : BaseEntity
    {
        /// <summary>
        /// 号源类型名称
        /// </summary>
        [SugarColumn(ColumnName = "SourceTypeName", ColumnDescription = "体检类型名称", Length = 20)]
        public virtual string SourceTypeName { get; set; }

        /// <summary>
        /// 当前类型号源存放的表
        /// </summary>
        [SugarColumn(ColumnName = "SourceTypeTable", ColumnDescription = "当前类型号源存放的表名", Length = 50)]
        public virtual string SourceTypeTable { get; set; }

        /// <summary>
        /// 当前号源类型的父级ID
        /// </summary>
        [SugarColumn(ColumnName = "SourceTypeParentId", ColumnDescription = "当前号源类型的父级ID", Length = 50)]
        public virtual string SourceTypeParentId { get; set; }

    }
}
