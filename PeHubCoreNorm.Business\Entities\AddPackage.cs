﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 加项包信息
/// </summary>
[SugarTable(TableName = "AddPackage", TableDescription = "加项包信息")]
public class AddPackage : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "AddPackageCode", ColumnDescription = "加项包编码", Length = 20)]
    public string AddPackageCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "AddPackageName", ColumnDescription = "加项包名称", Length = 50)]
    public string AddPackageName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Gender", ColumnDescription = "性别(0:通用 1:男 2:女)", Length = 2)]
    public string Gender { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Introduce", ColumnDescription = "简介", Length = 200)]
    public string Introduce { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 8, DecimalDigits = 2)]
    public decimal Price { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsCompany", ColumnDescription = "单位标识", Length = 2)]
    public string IsCompany { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "OptionalQuantity", ColumnDescription = "可选项目数量")]
    public int OptionalQuantity { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态", Length = 5)]
    public string Status { get; set; }
}