using System.ComponentModel.DataAnnotations;

namespace PeHubCoreNorm.Business.Input;

/// <summary>
/// 单位人员查询输入
/// </summary>
public class UnitPersonnelQueryInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    [Required(ErrorMessage = "单位编码不能为空")]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    [Required(ErrorMessage = "批次号不能为空")]
    public int BatchNumber { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    [Required(ErrorMessage = "证件号码不能为空")]
    public string IdNumber { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    [Required(ErrorMessage = "手机号码不能为空")]
    public string Tel { get; set; }
}

/// <summary>
/// 单位人员分页查询输入
/// </summary>
public class UnitPersonnelPageQueryInput : BasePageInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public int? BatchNumber { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IdNumber { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 员工姓名
    /// </summary>
    public string EmployeeName { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string PackAgeCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}
