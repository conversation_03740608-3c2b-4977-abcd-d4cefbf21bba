﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    [SugarTable("TimeSlot", TableDescription = "号源时段表")]
    public class NS_TimeSlot : BaseEntity
    {
        /// <summary>
        /// 时段信息编码
        /// </summary>
        [SugarColumn(ColumnName = "TimeSlotEntryID", ColumnDescription = "时段信息编码")]
        public virtual string TimeSlotEntryID { get; set; }

        /// <summary>
        /// 体检类型编码
        /// </summary>
        [SugarColumn(ColumnName = "SourceTypeID", ColumnDescription = "体检类型编码")]
        public virtual string SourceTypeID { get; set; }

        /// <summary>
        /// 时段启用状态
        /// </summary>
        [SugarColumn(ColumnName = "Statu", ColumnDescription = "时段启用状态", Length = 20)]
        public virtual string Statu { get; set; } = "T";
    }
}
