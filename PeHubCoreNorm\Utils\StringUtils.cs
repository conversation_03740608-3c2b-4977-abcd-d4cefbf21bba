﻿namespace PeHubCoreNorm;

public static class StringUtils
{
    /// <summary>
    ///     首字母小写
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public static string FirstCharToLower(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        return input.First().ToString().ToLower() + input.Substring(1);
    }

    //首字母大写
    public static string FirstCharToUpper(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        return input.First().ToString().ToUpper() + input.Substring(1);
    }
}