﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 互斥组合返参
/// </summary>
public class MutexCombOutput
{
    /// <summary>
    /// 互斥代码
    /// </summary>
    public string MutexCode { get; set; }

    /// <summary>
    /// 互斥名称
    /// </summary>
    public string MutexName { get; set; }

    /// <summary>
    /// 互斥组合展示
    /// </summary>
    public string MutexCombs { get; set; }
}

/// <summary>
/// 互斥组合明细返参
/// </summary>
public class MutexCombDetailOutput
{
    /// <summary>
    /// 互斥代码（同属一个互斥代码中的组合相斥）
    /// </summary>        
    public string MutexCode { get; set; }

    /// <summary>
    /// 互斥名称
    /// </summary>        
    public string MutexName { get; set; }

    /// <summary>
    /// 组合代码
    /// </summary>        
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>        
    public string CombName { get; set; }
}