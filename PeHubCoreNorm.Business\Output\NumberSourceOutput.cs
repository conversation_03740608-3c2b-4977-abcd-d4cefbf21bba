﻿namespace PeHubCoreNorm.Business;

public class NumberSourceOutput
{
    /// <summary>
    /// 总容量
    /// </summary>
    public int TotalCapacity { get; set; }

    /// <summary>
    /// 已用容量
    /// </summary>
    public int UsedCapacity { get; set; }

    /// <summary>
    /// 可用容量
    /// </summary>
    public int AvailableCapacity { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 星期（1-7）
    /// </summary>
    public int Week { get; set; }

    /// <summary>
    /// 是否休假
    /// </summary>
    public string IsVacation { get; set; }

    /// <summary>
    /// 关联的时间段ID
    /// </summary>
    public string TimeSlotID { get; set; }

    /// <summary>
    /// 关联的时间段名称
    /// </summary>
    public string TimeSlotName { get; set; }
}

public class SetNumberSourceOutput
{
    public string IsSuccess { get; set; }
}

public class PersonNumberSourceOutput
{
    public string Id { get; set; }
    public string SourceTypeName { get; set; }
    public string ClsCode { get; set; }
}