﻿namespace PeHubCoreNorm;

public class PeHubCoreNormConst
{
    #region 其他配置

    /// <summary>
    ///     0
    /// </summary>
    public const string Zero = "0";

    /// <summary>
    ///     网站开启访问
    /// </summary>
    public const string SYS_WEB_STATUS = "SYS_WEB_STATUS";

    /// <summary>
    ///     网站关闭提示
    /// </summary>
    public const string SYS_WEB_CLOSE_PROMPT = "SYS_WEB_CLOSE_PROMPT";

    /// <summary>
    ///     启用
    /// </summary>
    public const string STATUS_ENABLE = "Y";

    /// <summary>
    ///     停用
    /// </summary>
    public const string STATUS_DISABLED = "N";

    /// <summary>
    ///     超级管理员code
    /// </summary>
    public const string SUPER_ADMIN_CODE = "superAdmin";


    #region 登录错误次数

    /// <summary>
    ///     登录错误次数缓存Key
    /// </summary>
    public const string CACHE_LOGIN_ERROR_COUNT = CacheConst.Cache_Prefix_Web + "LoginErrorCount:";

    #endregion 登录错误次数

    #endregion

    #region 登录策略

    /// <summary>
    ///     登录验证码开关
    /// </summary>
    public const string LOGIN_CAPTCHA_OPEN = "LOGIN_CAPTCHA_OPEN";

    /// <summary>
    ///     登录验证码开关
    /// </summary>
    public const string LOGIN_CAPTCHA_TYPE = "LOGIN_CAPTCHA_TYPE";

    /// <summary>
    ///     单用户登录开关
    /// </summary>
    public const string LOGIN_SINGLE_OPEN = "LOGIN_SINGLE_OPEN";

    /// <summary>
    ///     登录错误锁定时长
    /// </summary>
    public const string LOGIN_ERROR_LOCK = "LOGIN_ERROR_LOCK";

    /// <summary>
    ///     登录错误锁定时长
    /// </summary>
    public const string LOGIN_ERROR_RESET_TIME = "LOGIN_ERROR_RESET_TIME";

    /// <summary>
    ///     登录错误次数
    /// </summary>
    public const string LOGIN_ERROR_COUNT = "LOGIN_ERROR_COUNT";

    #endregion 登录策略

    #region 密码策略

    /// <summary>
    ///     默认用户密码
    /// </summary>
    public const string PWD_DEFAULT_PASSWORD = "PWD_DEFAULT_PASSWORD";

    /// <summary>
    ///     密码定期提醒更新
    /// </summary>
    public const string PWD_REMIND = "PWD_REMIND";

    /// <summary>
    ///     密码定期提醒更新时间
    /// </summary>
    public const string PWD_REMIND_DAY = "PWD_REMIND_DAY";

    /// <summary>
    ///     修改初始密码提醒
    /// </summary>
    public const string PWD_UPDATE_DEFAULT = "PWD_UPDATE_DEFAULT";

    /// <summary>
    ///     密码最小长度
    /// </summary>
    public const string PWD_MIN_LENGTH = "PWD_MIN_LENGTH";

    /// <summary>
    ///     包含数字
    /// </summary>
    public const string PWD_CONTAIN_NUM = "PWD_CONTAIN_NUM";

    /// <summary>
    ///     包含小写字母
    /// </summary>
    public const string PWD_CONTAIN_LOWER = "PWD_CONTAIN_LOWER";

    /// <summary>
    ///     包含大写字母
    /// </summary>
    public const string PWD_CONTAIN_UPPER = "PWD_CONTAIN_UPPER";

    /// <summary>
    ///     包含特殊字符
    /// </summary>
    public const string PWD_CONTAIN_CHARACTER = "PWD_CONTAIN_UPPER";

	#endregion 密码策略

	#region 上传文件的配置
	/// <summary>
	/// 上传文件类型名字
	/// </summary>
	public const string FILE_TYPE = "FILE_TYPE";
	#endregion

	#region 加密wid
	/// <summary>
	/// 上传文件类型名字
	/// </summary>
	public const string WID = "PE_WEB";
	#endregion
}