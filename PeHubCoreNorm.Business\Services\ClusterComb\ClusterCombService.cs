﻿using Dm.Config;
using PeHubCoreNorm.Business.Output;
using SqlSugar;
using System.Diagnostics.Metrics;
using System.Reflection;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐组合服务
/// </summary>
public class ClusterCombService : BizDbRepository<CodeCluster>, IClusterCombService
{
    public ClusterCombService()
    {

    }

    /// <summary>
    /// 获取套餐列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<CodeClusterOutput[]> GetClusterList(ClusterQuery query)
    {
        var ageFlag = query.LowerAgeLimit != null && query.UpperAgeLimit != null;
        if (ageFlag && query.LowerAgeLimit > query.UpperAgeLimit)
            return Unify.SetError("年龄区间有误");

        return await Context.Queryable<CodeCluster>()
            .WhereIF(!string.IsNullOrEmpty(query.PeCls), x => x.PeCls == query.PeCls)
            .WhereIF(!string.IsNullOrEmpty(query.ClusName), x => x.ClusName == query.ClusName)
            .WhereIF(ageFlag && query.LowerAgeLimit != 0 && query.UpperAgeLimit != 0, x => x.LowerAgeLimit >= query.LowerAgeLimit && x.UpperAgeLimit <= query.UpperAgeLimit)
            .WhereIF(!string.IsNullOrEmpty(query.Gender), x => x.Gender == query.Gender)
            .Where(x => x.IsEnabled == "Y")
            .Select<CodeClusterOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 获取套餐包含的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterClsCombs[]> GetClusterCombs(MapClusterCombInput query)
    {
        var queryable = await Context.Queryable<MapClusterComb>()
            .InnerJoin<CodeItemComb>((map, comb) => comb.CombCode == map.CombCode)
            .LeftJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
            .Where(map => map.ClusCode == query.ClusCode)
            .Select((map, comb, cls) => new
            {
                cls.ClsCode,
                cls.ClsName,
                comb.CombCode,
                comb.CombName,
                comb.Price,
                comb.Description
            })
            .ToArrayAsync();

        return queryable.GroupBy(x => new { x.ClsCode, x.ClsName })
            .Select(x => new ClusterClsCombs
            {
                ClsCode = x.Key.ClsCode ?? "",
                ClsName = x.Key.ClsName ?? "无分类",
                Combs = x.Select(y => new ClsCombs
                {
                    CombCode = y.CombCode,
                    CombName = y.CombName,
                    Price = y.Price,
                    Description = y.Description ?? "",
                }).ToArray()
            })
            .ToArray();
    }

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterClsCombs[]> GetMapClusterExtraComb(MapClusterExtraCombInput query)
    {
        var queryable = await Context.Queryable<MapClusterExtraComb>()
          .InnerJoin<CodeItemComb>((map, comb) => comb.CombCode == map.CombCode)
          .LeftJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
          .Where(map => map.ClusCode == query.ClusCode)
          .Select((map, comb, cls) => new
          {
              cls.ClsCode,
              cls.ClsName,
              comb.CombCode,
              comb.CombName,
              comb.Price,
              comb.Description
          })
          .ToArrayAsync();

        return queryable.GroupBy(x => new { x.ClsCode, x.ClsName })
            .Select(x => new ClusterClsCombs
            {
                ClsCode = x.Key.ClsCode ?? "",
                ClsName = x.Key.ClsName ?? "无分类",
                Combs = x.Select(y => new ClsCombs
                {
                    CombCode = y.CombCode,
                    CombName = y.CombName,
                    Price = y.Price,
                    Description = y.Description ?? "",
                }).ToArray()
            })
            .ToArray();
    }

    /// <summary>
    /// 获取个检套餐加项包
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterAddPackageDataOutput[]> GetClusterAddPackage(ClusterAddPackageInput query)
    {
        var queryable = await Context.Queryable<MapClusterAddPackage>()
            .InnerJoin<AddPackage>((cluster, pkg) => pkg.AddPackageCode == cluster.AddPackageCode)
            .InnerJoin<AddPackageDetail>((cluster, pkg, pkgDetail) => pkgDetail.AddPackageCode == pkg.AddPackageCode)
            .InnerJoin<CodeItemComb>((cluster, pkg, pkgDetail, comb) => comb.CombCode == pkgDetail.CombCode)
            .Where(cluster => cluster.ClusCode == query.ClusCode)
            .Select((cluster, pkg, pkgDetail, comb) => new
            {
                pkg.AddPackageCode,
                pkg.AddPackageName,
                pkg.OptionalQuantity,
                pkg.CompanyCode,
                pkg.Gender,
                pkg.Introduce,
                comb.CombCode,
                comb.CombName,
                comb.Price,
                comb.Description
            })
            .ToArrayAsync();

        return queryable.GroupBy(x => new
        {
            x.AddPackageCode,
            x.AddPackageName
        })
        .Select(x => new ClusterAddPackageDataOutput
        {
            AddPackageCode = x.Key.AddPackageCode,
            AddPackageName = x.Key.AddPackageName,
            OptionalQuantity = x.First().OptionalQuantity,
            CompanyCode = x.First().CompanyCode,
            Gender = x.First().Gender,
            Introduce = x.First().Introduce,
            AddPkgCombsData = x.Select(y => new AddPkgCombsData
            {
                CombCode = y.CombCode,
                CombName = y.CombName,
                Price = y.Price,
                Description = y.Description
            })
            .ToArray()
        }).ToArray();
    }
}
