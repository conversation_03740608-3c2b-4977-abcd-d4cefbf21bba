﻿using AngleSharp.Css;
using Castle.Core.Resource;
using Dm.Config;
using Masuit.Tools;
using PeHubCoreNorm.Business.Output;
using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐组合服务
/// </summary>
public class ClusterCombService : BizDbRepository<CodeCluster>, IClusterCombService
{
    public ClusterCombService()
    {

    }

    /// <summary>
    /// 获取套餐列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<CodeClusterOutput[]> GetClusterList(ClusterQuery query)
    {
        var ageFlag = query.LowerAgeLimit != null && query.UpperAgeLimit != null;
        if (ageFlag && query.LowerAgeLimit > query.UpperAgeLimit)
            return Unify.SetError("年龄区间有误");

        return await Context.Queryable<CodeCluster>()
            .WhereIF(!string.IsNullOrEmpty(query.PeCls), x => x.PeCls == query.PeCls)
            .WhereIF(!string.IsNullOrEmpty(query.ClusName), x => x.ClusName == query.ClusName)
            .WhereIF(ageFlag && query.LowerAgeLimit != 0 && query.UpperAgeLimit != 0, x => x.LowerAgeLimit >= query.LowerAgeLimit && x.UpperAgeLimit <= query.UpperAgeLimit)
            .WhereIF(!string.IsNullOrEmpty(query.Gender), x => x.Gender == query.Gender)
            .Where(x => x.IsEnabled == "Y")
            .Select<CodeClusterOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 获取套餐包含的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterClsCombs[]> GetClusterCombs(MapClusterCombInput query)
    {
        var queryable = await Context.Queryable<MapClusterComb>()
            .InnerJoin<CodeItemComb>((map, comb) => comb.CombCode == map.CombCode)
            .LeftJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
            .Where(map => map.ClusCode == query.ClusCode)
            .Select((map, comb, cls) => new
            {
                cls.ClsCode,
                cls.ClsName,
                comb.CombCode,
                comb.CombName,
                comb.Price,
                comb.Description
            })
            .ToArrayAsync();

        return queryable.GroupBy(x => new { x.ClsCode, x.ClsName })
            .Select(x => new ClusterClsCombs
            {
                ClsCode = x.Key.ClsCode ?? "",
                ClsName = x.Key.ClsName ?? "无分类",
                CombData = x.Select(y => new ClsCombs
                {
                    CombCode = y.CombCode,
                    CombName = y.CombName,
                    Price = y.Price,
                    Description = y.Description ?? "",
                }).ToArray()
            })
            .ToArray();
    }

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterClsCombs[]> GetMapClusterExtraComb(MapClusterExtraCombInput query)
    {
        var queryable = await Context.Queryable<MapClusterExtraComb>()
          .InnerJoin<CodeItemComb>((map, comb) => comb.CombCode == map.CombCode)
          .LeftJoin<CodeItemCls>((map, comb, cls) => comb.ClsCode == cls.ClsCode)
          .Where(map => map.ClusCode == query.ClusCode)
          .Select((map, comb, cls) => new
          {
              cls.ClsCode,
              cls.ClsName,
              comb.CombCode,
              comb.CombName,
              comb.Price,
              comb.Gender,
              comb.Description
          })
          .ToArrayAsync();

        return queryable.GroupBy(x => new { x.ClsCode, x.ClsName })
            .Select(x => new ClusterClsCombs
            {
                ClsCode = x.Key.ClsCode ?? "",
                ClsName = x.Key.ClsName ?? "无分类",
                CombData = x.Select(y => new ClsCombs
                {
                    CombCode = y.CombCode,
                    CombName = y.CombName,
                    Price = y.Price,
                    Gender = y.Gender,
                    Description = y.Description ?? "",
                }).ToArray()
            })
            .ToArray();
    }

    /// <summary>
    /// 获取个检套餐加项包
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<ClusterAddPackageDataOutput[]> GetClusterAddPackage(ClusterAddPackageInput query)
    {
        var queryable = await Context.Queryable<MapClusterAddPackage>()
            .InnerJoin<AddPackage>((cluster, pkg) => pkg.AddPackageCode == cluster.AddPackageCode)
            .InnerJoin<AddPackageDetail>((cluster, pkg, pkgDetail) => pkgDetail.AddPackageCode == pkg.AddPackageCode)
            .InnerJoin<CodeItemComb>((cluster, pkg, pkgDetail, comb) => comb.CombCode == pkgDetail.CombCode)
            .Where(cluster => cluster.ClusCode == query.ClusCode)
            .Select((cluster, pkg, pkgDetail, comb) => new
            {
                pkg.AddPackageCode,
                pkg.AddPackageName,
                pkg.OptionalQuantity,
                pkg.CompanyCode,
                pkg.Gender,
                pkg.Introduce,
                comb.CombCode,
                comb.CombName,
                comb.Price,
                comb.Description
            })
            .ToArrayAsync();

        return queryable.GroupBy(x => new
        {
            x.AddPackageCode,
            x.AddPackageName
        })
        .Select(x => new ClusterAddPackageDataOutput
        {
            AddPackageCode = x.Key.AddPackageCode,
            AddPackageName = x.Key.AddPackageName,
            OptionalQuantity = x.First().OptionalQuantity,
            CompanyCode = x.First().CompanyCode,
            Gender = x.First().Gender,
            Introduce = x.First().Introduce,
            AddPkgCombsData = x.Select(y => new AddPkgCombsData
            {
                CombCode = y.CombCode,
                CombName = y.CombName,
                Price = y.Price,
                Description = y.Description
            })
            .ToArray()
        }).ToArray();
    }

    /// <summary>
    /// 获取项目的相关关系
    /// </summary>
    /// <param name="comb"></param>
    /// <returns></returns>
    public async Task<ComRelationOutput> GetCombRelation(CombRelation comb)
    {
        // 查询当前组合相关的互斥组
        var currentMutex = Context.Queryable<CodeMutexComb>()
                            .Where(x => x.CombCode == comb.CombCode)
                            .Select(x => x.MutexCode)
                            .ToList();

        // 一次性获取所有相关互斥组合（包含组合名称）
        var mutexCombs = Context.Queryable<CodeMutexComb>()
                                .Where(x => currentMutex.Contains(x.MutexCode))
                                .Select<MutexCombDetailOutput>()
                                .ToList();

        var combDepends = await Context.Queryable<MapCombDependence>()
               .Where(x => x.CombCode == comb.CombCode)
               .Select<CombDependenceDetail>()
               .ToListAsync();

        var combContains = await Context.Queryable<MapCombContain>()
          .Where(x => x.CombCode == comb.CombCode)
          .Select<CombContainDetailOutput>()
          .ToListAsync();

        return new ComRelationOutput
        {
            MutexRelation = mutexCombs,
            DependRelation = combDepends,
            ContainRelation = combContains
        };
    }

    /// <summary>
    /// 计算项目加项
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    public async Task<CalCulateCombs> CalCulateCombsAsync(CalCulateCombs calcCombs)
    {
        return calcCombs.Operator switch
        {
            "+" => CalcAddCombs(calcCombs),
            "-" => CalcDelCombs(calcCombs),
            _ => throw new ArgumentException($"Unsupported operator: {calcCombs.Operator}")
        };
    }

    /// <summary>
    /// 检验项目关系是否通过
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    public async Task<bool> VerifyCombIsPass(List<ReturnCombs> calcCombs)
    {
        var combSet = new HashSet<string>(calcCombs.Select(x => x.CombCode).ToArray());

        #region  1.查询有无互斥的项目
        var mutexData = await Context.Queryable<CodeMutexComb>()
                       .Where(x => combSet.Contains(x.CombCode))
                       .ToListAsync();

        // 找出有冲突的互斥组
        var conflictGroup = mutexData.GroupBy(x => x.MutexCode).FirstOrDefault(g => g.Count() >= 2);
        if (conflictGroup != null)
        {
            Unify.SetError($"{string.Join(",", conflictGroup.Select(x => x.CombName))}存在互斥关系!");
            return false;
        }
        #endregion

        #region 2.查询有无依赖项目未带出
        var dependData = await Context.Queryable<MapCombDependence>()
                        //.Where(x => SqlFunc.ContainsArray(combSet, x.CombCode))
                        .Where(x => combSet.Contains(x.CombCode))
                        .ToListAsync();

        // 找出第一个缺失的依赖项
        var missingDependency = dependData.FirstOrDefault(item => !combSet.Contains(item.DependOnCombCode));
        if (missingDependency != null)
        {
            Unify.SetError($"{missingDependency.CombName}需要带出{missingDependency.DependOnCombName},请处理后再试");
            return false;
        }
        #endregion

        #region 3.查询包含关系
        // 查出哪些父级项目
        var parentCombs = await Context.Queryable<MapCombContain>()
                    //.Where(x => SqlFunc.ContainsArray(combSet, x.ChildCombCode))
                    .Where(x => combSet.Contains(x.ChildCombCode))
                    .Select(x => x.CombCode)
                    .ToArrayAsync();

        if (parentCombs.Length == 0)
            return true;

        var containData = await Context.Queryable<MapCombContain>()
                        .Where(x => SqlFunc.ContainsArray(parentCombs, x.CombCode))
                        .ToListAsync();

        foreach (var group in containData.GroupBy(x => new { x.CombCode, x.CombName }))
        {
            // 子项目数据
            var childCombCodes = group.Select(x => x.ChildCombCode).ToList();
            var exitsComb = combSet.Where(combCode => childCombCodes.Contains(combCode)).ToList();

            if (childCombCodes.All(childCode => combSet.Contains(childCode)))
            {
                var childNames = string.Join(",", group.Select(x => x.ChildCombName));
                Unify.SetError($"{childNames}可以合并成{group.Key.CombName},请处理..");
                return false;
            }
        }
        #endregion

        return true;
    }

    #region 私有方法
    /// <summary>
    /// 添加项目
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    private CalCulateCombs CalcAddCombs(CalCulateCombs calcCombs)
    {
        if (calcCombs.ReturnComb.Any(x => x.CombCode == calcCombs.CombCode))
        {
            Unify.SetError($"{calcCombs.CombName}已被选中，无需重复添加");
            return calcCombs;
        }

        calcCombs.ReturnComb.Add(new ReturnCombs { CombCode = calcCombs.CombCode, BeFrom = [], DependOrContain = "" });

        CalcCombMutex(calcCombs);
        CalcCombDepend(calcCombs);
        CalcCombContain(calcCombs);

        calcCombs.ReturnComb.DistinctBy(x => x.CombCode).ToList();
        return calcCombs;
    }

    /// <summary>
    /// 删除项目
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    private CalCulateCombs CalcDelCombs(CalCulateCombs calcCombs)
    {
        // 判断有无依赖
        var depends = calcCombs.ReturnComb.Where(x => x.BeFrom.Contains(calcCombs.CombCode)).ToList();
        if (depends.Count != 0)
        {
            Unify.SetError($"{calcCombs.CombName} 被其他项目依赖，无法删除");
            return calcCombs;
        }

        // 删除组合判断有没有关联
        var delCombs = calcCombs.ReturnComb.Where(x => x.CombCode == calcCombs.CombCode).FirstOrDefault();
        if (delCombs != null && delCombs.BeFrom.Count == 0)
            calcCombs.ReturnComb.Remove(delCombs);

        if (delCombs.DependOrContain == "depend")
        {
            var dependCombs = calcCombs.ReturnComb.Where(x => delCombs.BeFrom.Contains(x.CombCode)).ToList();
            calcCombs.ReturnComb.Remove(delCombs);
            // 判断这个组合有没有被其他组合依赖，如果有则不删除
            foreach (var dependComb in dependCombs)
            {
                if (calcCombs.ReturnComb.Any(x => x.CombCode != calcCombs.CombCode && x.BeFrom.Contains(dependComb.CombCode)))
                    continue;

                calcCombs.ReturnComb.Remove(dependComb);
            }
        }
        else
        {
            calcCombs.ReturnComb.Remove(delCombs);
        }

        return calcCombs;
    }

    /// <summary>
    /// 计算项目互斥
    /// </summary>
    /// <param name="calcCombs"></param>
    public void CalcCombMutex(CalCulateCombs calcCombs)
    {
        if (calcCombs.ReturnComb.Count == 0)
            return;

        // 查询当前项目相关的互斥组
        var currentMutexGroups = Context.Queryable<CodeMutexComb>()
                            .Where(x => x.CombCode == calcCombs.CombCode)
                            .Select(x => x.MutexCode)
                            .ToList();

        if (currentMutexGroups.Count == 0)
            return;

        // 通过互斥组查出对应的项目
        var mutexCombData = Context.Queryable<CodeMutexComb>()
                            .Where(x => currentMutexGroups.Contains(x.MutexCode))
                            .ToList();

        foreach (var mutexCombs in mutexCombData.GroupBy(x => x.MutexCode))
        {
            // 查出互斥组合数据，剔除掉选中的项目
            var mutexData = mutexCombs.Where(x => x.CombCode != calcCombs.CombCode && calcCombs.ReturnComb.Any(y => y.CombCode == x.CombCode)).ToList();
            if (mutexData.Count == 0)
                continue;

            var conflictNames = string.Join("、", mutexData.Select(c => c.CombName));
            Unify.SetError($"{calcCombs.CombName} 与 {conflictNames} 存在互斥关系，无法添加");
            return; // 发现冲突立即退出
        }
    }

    /// <summary>
    /// 计算项目依赖
    /// </summary>
    /// <param name="calcCombs"></param>
    public void CalcCombDepend(CalCulateCombs calcCombs)
    {
        var dependCombs = Context.Queryable<MapCombDependence>().Where(x => x.CombCode == calcCombs.CombCode).Select(x => x.DependOnCombCode).ToList();
        if (!dependCombs.Any())
            return;

        // 定位后修改数据
        var targetComb = calcCombs.ReturnComb.Where(x => x.CombCode == calcCombs.CombCode).First();
        targetComb.BeFrom = dependCombs;
        targetComb.DependOrContain = "depend";

        // 只添加不存在的依赖项
        foreach (var combCode in dependCombs)
        {
            if (!calcCombs.ReturnComb.Any(x => x.CombCode == combCode))
            {
                calcCombs.ReturnComb.Add(new ReturnCombs
                {
                    CombCode = combCode,
                    BeFrom = [],
                    DependOrContain = ""
                });
            }
        }
    }

    /// <summary>
    /// 计算项目包含
    /// </summary>
    /// <param name="calcCombs"></param>
    public void CalcCombContain(CalCulateCombs calcCombs)
    {
        // 先找出父级的数据
        var parentCombCodes = Context.Queryable<MapCombContain>().Where(x => x.ChildCombCode == calcCombs.CombCode).Select(x => x.CombCode).ToList();
        if (!parentCombCodes.Any())
            return;

        // 找出对应的包含数据
        var combContains = Context.Queryable<MapCombContain>().Where(x => SqlFunc.ContainsArray(parentCombCodes, x.CombCode))
                            .ToList()
                            .GroupBy(x => x.CombCode)
                            .ToDictionary(g => g.Key, g => g.Select(x => x.ChildCombCode).ToList());

        foreach (var (parentCode, childCodes) in combContains)
        {
            // 检查所有子项目是否都存在
            bool allChildrenExist = childCodes.All(childCode =>
                calcCombs.ReturnComb.Any(x => x.CombCode == childCode));

            if (allChildrenExist)
            {
                // 移除所有子项目
                calcCombs.ReturnComb.RemoveAll(x => childCodes.Contains(x.CombCode));

                // 添加父项目（避免重复）
                var containComb = calcCombs.ReturnComb.Where(x => x.CombCode == parentCode).FirstOrDefault();
                if (containComb == null)
                {
                    calcCombs.ReturnComb.Add(new ReturnCombs
                    {
                        CombCode = parentCode,
                        BeFrom = childCodes,
                        DependOrContain = "contain"
                    });
                }
                else
                {
                    containComb.BeFrom = childCodes;
                    containComb.DependOrContain = "contain";
                }
            }
        }
    }

    public async Task<List<CompanyClusterDetailsOutput>> GetCompanyClusterCombs(MapClusterCombInput query)
    {

       return await Context.Queryable<MapCompanyClusterComb>()
              .LeftJoin<CodeItemComb>((item, details) => item.CombCode == details.CombCode)
              .Where((item, details) => item.ClusCode == query.ClusCode)
              .Select((item, details) => new CompanyClusterDetailsOutput
              {
                  ComCode = item.CombCode,
                  ComName = details.CombName,
                  Price = details.Price,
                  Status = details.Status,
                  Gender = details.Gender,
                  IsPriority = details.IsPriority,
                  ClsCode = details.ClsCode,
                  Description = details.Description,
                  Attention = details.Attention,

              })
              .ToListAsync();
    }
    #endregion
}
