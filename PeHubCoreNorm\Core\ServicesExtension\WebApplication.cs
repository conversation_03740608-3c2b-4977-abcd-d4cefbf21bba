﻿using Hangfire;
using Hangfire.Dashboard;
using Masuit.Tools.Security;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.FileProviders;
using PeHubCoreNorm.Core;
using PeHubCoreNorm.Core.ServicesExtension;
using PeHubCoreNorm.Middlewares;
using PeHubCoreNorm.Utils;
using System.IO;
 

namespace PeHubCoreNorm;

public static class WebApplication
{
    public static void Run(string[] args,
        Action<IServiceCollection> beforeBuild = null,
        Action afterBuild = null)
    {
        var builder = Microsoft.AspNetCore.Builder.WebApplication.CreateBuilder(args);

        //初始化
        App.Configuration = builder.Configuration;
        builder.Configuration.AddJsonFile();
        builder.Logging.ClearProviders();
        builder.Host.UseNLog();
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddHttpClient();
        //设置机器idAddToMasuitTools
        Masuit.Tools.Systems.SnowFlakeNew.SetMachienId(1);

        //设置RSA
        var publicKey = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "JsonConfig", "public.key");
        var privateKey = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "JsonConfig", "private.key");
        if (!File.Exists(publicKey) || !File.Exists(privateKey))
        {
            var key = RsaCrypt.GenerateRsaKeys();
            File.WriteAllText(publicKey, key.PublicKey);
            File.WriteAllText(privateKey, key.PrivateKey);
        }

        //Services扩展
        var services = builder.Services;

        //IP获取
        services.Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            //仅允许受信任的代理和网络转接头。否则，可能会受到 IP 欺骗攻击 安全起见推荐白名单:options.KnownProxies.Add(IPAddress.Parse("127.0.0.1"));
            options.KnownProxies.Clear();
        });
 
		services.AddCors(App.Get<string>("CorsUrls").Split(",")); //跨域
		services.AddInjectAssembly(); //自动注入
        services.AddControllers(); // AddControllers 和 Json配置
        services.AddJwt(); //Jwt
        services.AddSwagger(); //Swagger

        services.AddCache(); //缓存
        services.AddFluent(); //验证
        services.AddCAP(); //CAP 内存

		if (App.Get<bool>("Hangfire:enabled"))
        {
            services.AddHangfire(); //添加定时作业使用sqllite
        }

		//注册IHttpClientFactory的实现到DI容器
		services.AddTransient<IHttpClientHelper, HttpClientHelper>();

        beforeBuild?.Invoke(services);

        var app = builder.Build();

        app.UseForwardedHeaders();
        app.UseSwagger();
        app.UseSwaggerUI();
        app.UseMiddleware<RealIPMiddleware>(); //Real-IP
        app.UseMiddleware<UnifyResultStopwatchMiddleware>();
        app.UseMiddleware<RequestDecryptMiddleware>();//解密
        app.UseMiddleware<RequestMiddleware>();
        app.UseCors("baseCors");
        app.UseAuthorization();
        app.MapControllers();


		//app.UseStaticFiles();//静态文件访问配置

		//静态文件访问配置,专门上传文件
		// 确保目录存在
		if (!Directory.Exists(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Uploads")))
        {
            Directory.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Uploads"));
        }
		app.UseStaticFiles(new StaticFileOptions
         {
             ServeUnknownFileTypes = true,
             FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Uploads")),//wwwroot相当于真实目录的Uploads
			 OnPrepareResponse = c =>
             {
                 c.Context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                 c.Context.Response.Headers.Append("Cache-Control", "public, max-age=604800");
             },
             RequestPath = new PathString("/nas")//src相当于别名，为了安全
         });


        app.UseWebSockets(new WebSocketOptions
        {
            KeepAliveInterval = TimeSpan.FromSeconds(60)
		});

		if (App.Get<bool>("Hangfire:enabled"))
		{
			app.UseHangfireDashboard(App.Get<string>("Hangfire:pathurl"), new DashboardOptions
			{
				Authorization = new[] { new HangfireAuthFilter(App.Get<string>("Hangfire:user"),
			App.Get<string>("Hangfire:pwd")) },
				DarkModeEnabled = true,
				DashboardTitle = "KR定时作业",
			});
		}
 

		app.UseMiddleware<WebsocketHandlerMiddleware>();

        App.ServiceProvider = app.Services;
        App.WebHostEnvironment = app.Environment;

        SqlSugarCodeFirst.Execute(); //CodeFirst

        afterBuild?.Invoke();

        app.Run();
    }
}