﻿using PeHubCoreNorm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    [SugarTable("UnitPersonnelList", TableDescription = "单位人员名单")]
    public class UnitPersonnelList: BaseEntity
    {
        [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
        public string CompanyCode { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "CompanyName", ColumnDescription = "单位名称", Length = 100)]
        public string CompanyName { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "Department", ColumnDescription = "单位部门", Length = 100)]
        public string Department { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态")]
        public int Status { get; set; } =0;

        [SugarColumn(IsNullable = true, ColumnName = "EmployeeName", ColumnDescription = "员工名字", Length = 100)]
        public string EmployeeName { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "Sex", ColumnDescription = "性别")]
        public int Sex { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "Ethnic", ColumnDescription = "民族", Length = 20)]
        public string Ethnic { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "Married", ColumnDescription = "婚姻状况")]
        public int Married { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "Birthday", ColumnDescription = "出生日期", Length = 20)]
        public string Birthday { get; set; }

        

          [SugarColumn(IsNullable = true, ColumnName = "Age", ColumnDescription = "年龄" )]
        public int Age { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "Tel", ColumnDescription = "手机号码", Length = 20)]
        public string Tel { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "IdNumberType", ColumnDescription = "证件号类型")]
        public int IdNumberType { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "IdNumber", ColumnDescription = "证件号码", Length = 100)]
        public string IdNumber { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "PackAgeCode", ColumnDescription = "套餐编码", Length = 100)]
        public string PackAgeCode { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "PackAgeName", ColumnDescription = "套餐名称", Length = 100)]
        public string PackAgeName { get; set; }

        [SugarColumn(IsNullable = true, ColumnName = "BatchNumber", ColumnDescription = "批次号")]
        public int BatchNumber { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "StartTime", ColumnDescription = "有效起始日期")]
        public DateTime StartTime { get; set; }


        [SugarColumn(IsNullable = true, ColumnName = "EndTime", ColumnDescription = "有效终止日期")]
        public DateTime EndTime { get; set; }


    }
}
