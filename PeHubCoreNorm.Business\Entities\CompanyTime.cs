﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位体检次数信息
/// </summary>
[SugarTable("CompanyTime", TableDescription = "单位体检次数信息")]
public class CompanyTime : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyTimes", ColumnDescription = "单位体检次数")]
    public int CompanyTimes { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "BeginDate", ColumnDescription = "开始日期")]
    public DateTime? BeginDate { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "EndDate", ColumnDescription = "结束日期")]
    public DateTime? EndDate { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "OnLineEndDate", ColumnDescription = "线上结束日期")]
    public DateTime? OnLineEndDate { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Description", ColumnDescription = "套餐简介", Length = 200)]
    public string Description { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Notice", ColumnDescription = "注意事项", Length = 200)]
    public string Notice { get; set; }
}
