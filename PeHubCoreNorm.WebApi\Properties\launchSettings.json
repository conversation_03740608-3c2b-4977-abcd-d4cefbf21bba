{"profiles": {"Development": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:8277", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Prod": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production", "ASPNETCORE_URLS": "https://localhost:8277"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:8277"}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production", "ASPNETCORE_URLS": "https://localhost:8277"}, "distributionName": ""}}, "$schema": "http://json.schemastore.org/launchsettings.json"}