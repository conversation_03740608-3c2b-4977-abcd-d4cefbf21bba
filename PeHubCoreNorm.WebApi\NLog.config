﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      autoReload="true"
      throwExceptions="true"
      internalLogLevel="Error"
      internalLogFile="nlogs/nlog-internal.log"
>
    <extensions>
        <add assembly="NLog.Web.AspNetCore"/>
    </extensions>

    <variable name="directory" value="${basedir}/logs"/>
    <!--<variable name="directory" value="/home/<USER>/" />-->

    <targets async="true">
        <!--${}内的参数不区分大小写-->
        <!-- Console -->
        <target name="Console"
                xsi:type="ColoredConsole"
                encoding="utf-8"
                layout="${longdate} [${level:uppercase=true}] ${logger} : ${message} ${exception:format=tostring}"/>

        <!-- Info -->
        <target name="Info"
                xsi:type="File"
                encoding="utf-8"
                maxArchiveDays="7"
                archiveAboveSize="20971520"
                archiveEvery="Day"
                fileName="${directory}/log/${date:format=yyyy-MM-dd}.log"
                layout="${longdate} [${level}] [${callsite-linenumber}] ${callsite} : ${message}"/>

        <!-- Error -->
        <target name="Error"
                xsi:type="File"
                encoding="utf-8"
                maxArchiveDays="7"
                archiveAboveSize="20971520"
                archiveEvery="Day"
                fileName="${directory}/bug/${date:format=yyyy-MM-dd}.log"
                layout="${longdate} [${level}] [${callsite-linenumber}] ${callsite} : ${message} ${exception:format=tostring}"/>
    </targets>

    <rules>

        <logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="Console,Info" final="true"/>
        <logger name="Microsoft.*" minlevel="Info" writeTo="Console" final="true"/>

        <logger name="*" minlevel="Info" writeTo="Console"/>
        <logger name="*" minlevel="Info" writeTo="Info"/>
        <logger name="*" minlevel="Error" writeTo="Error"/>
    </rules>
</nlog>