# UnitTitlePage 新增接口说明

## 接口概述

为 `UnitTitlePage` 实体类创建的新增接口，用于添加单位封面信息。

## 接口详情

### 1. 新增单位封面接口

**接口地址**: `POST /admin/UnitTitlePage/AddUnitTitlePage`

**权限要求**: 需要"添加单位封面"操作权限

**请求参数**: `UnitTitlePageAddInput`

```json
{
  "companyCode": "COMP001",                    // 单位编码（必填，最大50字符）
  "companyTimes": 1,                           // 体检次数（必填，大于0）
  "validityPeriod": "2024-12-31",              // 体检有效期限（必填，最大100字符）
  "notes": "体检须知内容...",                   // 须知（必填）
  "title": "2024年度体检",                     // 标题（必填，最大200字符）
  "imgData": "data:image/jpeg;base64,/9j/4AAQ...", // 封面图片数据（必填，Base64字符串）
  "fileSize": 102400                           // 文件大小（必填，大于0）
}
```

**返回结果**: `bool`
- `true`: 添加成功
- `false`: 添加失败

## 业务逻辑

### 1. 数据验证
- 所有必填字段验证
- 字符串长度限制验证
- 数值范围验证
- Base64 图片数据格式验证
- 支持的图片格式验证（JPEG、PNG、GIF、WebP）
- 图片大小限制验证（最大5MB）
- 文件大小与实际数据大小匹配验证

### 2. 唯一性检查
- 检查同一单位编码（CompanyCode）和体检次数（CompanyTimes）的组合是否已存在
- 如果已存在，返回错误信息

### 3. 数据处理
- 自动生成主键ID（雪花算法）
- 自动设置创建时间
- 自动设置创建用户信息
- 图片数据以 Base64 字符串形式存储到数据库
- 自动去除 data:image 前缀（如果存在）

### 4. 异常处理
- 捕获并记录异常日志
- 返回友好的错误信息

## 辅助接口

### 1. 检查是否存在
**接口地址**: `GET /admin/UnitTitlePage/CheckUnitTitlePageExists`

**参数**:
- `companyCode`: 单位编码
- `companyTimes`: 体检次数
- `excludeId`: 排除的ID（可选，编辑时使用）

### 2. 根据ID查询
**接口地址**: `GET /admin/UnitTitlePage/GetUnitTitlePageById`

**参数**:
- `id`: 主键ID

### 3. 根据单位信息查询
**接口地址**: `GET /admin/UnitTitlePage/GetUnitTitlePageByCompany`

**参数**:
- `companyCode`: 单位编码
- `companyTimes`: 体检次数

### 4. 获取图片
**接口地址**: `GET /admin/UnitTitlePage/GetUnitTitlePageImage`

**参数**:
- `id`: 主键ID

**返回**: 图片文件流（自动识别JPEG、PNG、GIF格式）

## 使用示例

### JavaScript 调用示例

```javascript
// 将文件转换为Base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

// 新增单位封面
async function addUnitTitlePage(imageFile) {
    // 转换图片为Base64
    const base64Data = await fileToBase64(imageFile);

    const formData = {
        companyCode: "COMP001",
        companyTimes: 1,
        validityPeriod: "2024-12-31",
        notes: "请按时参加体检，携带身份证...",
        title: "2024年度员工体检",
        imgData: base64Data, // 包含data:image前缀的完整Base64字符串
        fileSize: imageFile.size
    };

    try {
        const response = await fetch('/admin/UnitTitlePage/AddUnitTitlePage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();
        if (result.data === true) {
            console.log('添加成功');
        } else {
            console.error('添加失败:', result.msg);
        }
    } catch (error) {
        console.error('请求异常:', error);
    }
}

// 检查是否存在
async function checkExists(companyCode, companyTimes) {
    const response = await fetch(
        `/admin/UnitTitlePage/CheckUnitTitlePageExists?companyCode=${companyCode}&companyTimes=${companyTimes}`,
        {
            headers: {
                'Authorization': 'Bearer ' + token
            }
        }
    );
    const result = await response.json();
    return result.data;
}
```

### C# 调用示例

```csharp
// 新增单位封面
var input = new UnitTitlePageAddInput
{
    CompanyCode = "COMP001",
    CompanyTimes = 1,
    ValidityPeriod = "2024-12-31",
    Notes = "请按时参加体检，携带身份证...",
    Title = "2024年度员工体检",
    ImgData = imageBytes,
    FileSize = imageBytes.Length
};

var result = await _unitTitlePageService.AddUnitTitlePage(input);
```

## 注意事项

1. **图片数据**: 建议图片大小不超过5MB，支持JPEG、PNG、GIF格式
2. **唯一性**: 同一单位的同一体检次数只能有一个封面
3. **权限**: 需要相应的操作权限才能调用新增接口
4. **事务**: 新增操作在数据库事务中执行，确保数据一致性
5. **日志**: 所有操作都会记录详细的日志信息

## 错误码说明

- `单位编码:{companyCode}，体检次数:{companyTimes} 的封面已存在，请勿重复添加!`
- `图片数据不能为空!`
- `图片数据格式错误: {具体错误信息}`
- `不支持的图片格式，请使用JPEG、PNG、GIF或WebP格式!`
- `图片大小超出限制，最大支持5MB!`
- `文件大小与实际数据大小不匹配!`
- `添加单位封面失败，请稍后重试!`
