﻿using PeHubCoreNorm.System;

namespace PeHubCoreNorm.WebApi.Controllers.System;

[ApiExplorerSettings(GroupName = "System")]
[Route("mqtt")]
[IgnoreLog]
public class MQTTController : BaseControllerAuthorize
{
    private readonly IMQTTService _mQTTService;

    public MQTTController(IMQTTService mQTTService)
    {
        _mQTTService = mQTTService;
    }

    /// <summary>
    ///     获取当前mqtt配置信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("getParameter")]
    [AllowAnonymous]
    //[IgnoreLog]
    public async Task<dynamic> ScheduleList([FromQuery] dynamic input)
    {
        var config = await _mQTTService.GetMQTTConfig();
		var jobId = Hangfire.BackgroundJob.Schedule(() => Console.WriteLine("100s的延迟任务执行了！"),TimeSpan.FromSeconds(100));//一天后执行该任务

		return new
        {
            ClientId = $"SPX_{UserManager.UserAccount}_{UserManager.Device}",
            config?.Url,
            config?.UserName,
            config?.Password,
            Topics = new List<string> { $"SPXUI/{UserManager.UserId}" }
        };
    }

    /// <summary>
    ///     获取当前mqtt配置信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("Publish")]
    [AllowAnonymous]
    public async Task<dynamic> Publish([FromQuery] dynamic input)
    {
        var config = await _mQTTService.Publish("abc", "123");
        return "ok";
    }
}