﻿using Microsoft.AspNetCore.Mvc;

namespace PeHubCoreNorm;

/// <summary>
///     asp.net 原生Controller
/// </summary>
[ApiController]
[NotUnifyResult]
[AduitLog]
public class BaseControllerAspNet : ControllerBase
{
}

/// <summary>
///     一个最普通的Api (规范化返回结果)
/// </summary>
[ApiController]
[AduitLog]
public class BaseController : ControllerBase
{
}

/// <summary>
///     需要jwt授权
/// </summary>
[ApiController]
[Authorize]
[AduitLog]
public class BaseControllerAuthorize : ControllerBase
{
}

/// <summary>
///     需要接口授权
/// </summary>
[ApiController]
[Authorize]
[RolePermission]
[AduitLog]
public class BaseControllerRoleAuthorize : ControllerBase
{
}

/// <summary>
///     超级管理员可访问
/// </summary>
[ApiController]
[Authorize]
[AduitLog]
[SuperAdmin]
public class BaseControllerSuperAdmin : ControllerBase
{
}