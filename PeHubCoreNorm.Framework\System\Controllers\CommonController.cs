﻿using PeHubCoreNorm.Services.CommonService;

namespace PeHubCoreNorm.System;

[ApiExplorerSettings(GroupName = "Dev")]
[Route("dev/[controller]")]
[AllowAnonymous]
public class CommonController : BaseController
{
    private readonly ICommonService _commonService;

    public CommonController(ICommonService commonService)
    {
        _commonService = commonService;
    }

    /// <summary>
    ///     备份数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="config">租户ID</param>
    /// <returns></returns>
    [HttpPost("backupsSeedDataJson")]
    [ActionPermission(ActionType.Button, "备份数据成JSON", "公用方法")]
    public async Task BackupsSeedDataJson([FromBody] BaseIdsInput input, string config = SqlsugarConst.DB_Default)
    {
        if (input == null || input.Ids.Length == 0)
        {
            var list = new List<string>();
            list.Add("sys_menu");
            list.Add("sys_role");
            list.Add("sys_user");
            list.Add("dev_config");
            list.Add("dev_dict");
            list.Add("sys_relation");

            input.Ids = list.ToArray();
        }

        await _commonService.WriteSeedDataJson(input, config);
    }
}