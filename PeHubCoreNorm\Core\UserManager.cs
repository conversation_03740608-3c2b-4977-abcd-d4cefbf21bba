﻿namespace PeHubCoreNorm;

public class UserManager
{
    /// <summary>
    ///     当前用户Id
    /// </summary>
    public static string UserId => App.User?.FindFirst(ClaimConst.UserId)?.Value;

    /// <summary>
    ///     当前用户账号
    /// </summary>
    public static string UserAccount => App.User?.FindFirst(ClaimConst.Account)?.Value;

    /// <summary>
    ///     当前用户昵称
    /// </summary>
    public static string Name => App.User?.FindFirst(ClaimConst.Name)?.Value;

    /// <summary>
    ///     是否超级管理员
    /// </summary>
    public static bool SuperAdmin => App.User?.FindFirst(ClaimConst.SuperAdmin)?.Value == "Y";

    /// <summary>
    ///     登陆设备
    /// </summary>
    public static string Device => App.User?.FindFirst(ClaimConst.Device)?.Value;

	/// <summary>
	///     token包含的授权医院列表
	/// </summary>
	public static string OrgList => App.User?.FindFirst(ClaimConst.OrgList)?.Value;

    private static string _orgCode;

    /// <summary>
    /// 唯一键租户使用
    /// </summary>
    public static string OrgCode
    {
        get => _orgCode ?? App.HttpContext?.Request?.Headers[ClaimConst.OrgCode].ToString();
        set=>_orgCode = value;
    }

    /// <summary>
    /// 定时服务用到
    /// </summary>
    /// <param name="orgCode"></param>
    public static void setOrgCodeValue(string orgCode)
    {
        _orgCode = orgCode;
    }
}