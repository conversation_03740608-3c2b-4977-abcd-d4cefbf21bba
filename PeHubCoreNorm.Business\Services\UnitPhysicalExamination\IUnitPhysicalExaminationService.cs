﻿using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.UnitPhysicalExamination
{
    public interface IUnitPhysicalExaminationService: ITransient, ITenantDBTransient
    {

        /// <summary>
        /// 验证单位人员是否存在预体检记录 并返回记录
        /// </summary>
        /// <returns></returns>
        Task<UnitPersonnelList> AuthenticationToUnti(AuthenticationToUntiInput input);

        /// <summary>
        /// 根据条件查询单位人员详细信息（包含套餐信息）
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>单位人员详细信息</returns>
        Task<UnitPersonnelDetailOutput> GetUnitPersonnelWithPackageInfo(UnitPersonnelQueryInput input);

        
    }
}
