﻿using PeHubCoreNorm.Business.Input;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.UnitPhysicalExamination
{
    public interface IUnitPhysicalExaminationService: ITransient, ITenantDBTransient
    {

        /// <summary>
        /// 验证单位人员是否存在预体检记录 并返回记录
        /// </summary>
        /// <returns></returns>
        Task<UnitPersonnelList> AuthenticationToUnti(AuthenticationToUntiInput input);


        
      
    }
}
