﻿namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 单位控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class CompanyController : BaseControllerAuthorize
{
    private readonly ICompanyService _companyService;
    private readonly IBasicCodeService _basicCodeService;

    public CompanyController(ICompanyService companyService, IBasicCodeService basicCodeService)
    {
        _companyService = companyService;
        _basicCodeService = basicCodeService;
    }

    /// <summary>
    /// 单位信息查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetCompanyList")]
    [ActionPermission(ActionType.Query, "单位分页查询")]
    public async Task<SqlSugarPagedList<CompanyOutput>> GetCompanyList([FromBody] CompanyPageInput input)
    {
        return await _companyService.GetCompanyList(input);
    }

    /// <summary>
    /// 单位体检次数查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    [HttpGet("GetCompanyTimes")]
    [ActionPermission(ActionType.Query, "单位体检次数查询", "单位业务")]
    public async Task<CompanyTimeOutput[]> GetCompanyTimes([FromQuery] string companyCode)
    {
        return await _companyService.GetCompanyTimes(companyCode);
    }

    /// <summary>
    /// 单位体检套餐查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    [HttpGet("GetCompanyCluster")]
    [ActionPermission(ActionType.Query, "单位体检次数查询", "单位业务")]
    public async Task<CompanyClusterOutput[]> GetCompanyCluster([FromQuery] string companyCode, [FromQuery] int companyTimes)
    {
        return await _companyService.GetCompanyCluster(companyCode, companyTimes); 
    }

    /// <summary>
    /// 单位体检套餐控制查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="clusterCode"></param>
    /// <returns></returns>
    [HttpGet("GetCompanyClusterControl")]
    [ActionPermission(ActionType.Query, "单位体检套餐控制查询", "单位业务")]
    public async Task<CompanyClusterControlOutput> GetCompanyClusterControl([FromQuery] string companyCode, [FromQuery] string clusterCode)
    {
        return await _companyService.GetCompanyClusterControl(companyCode, clusterCode);
    }

    /// <summary>
    /// 保存单位体检套餐控制
    /// </summary>
    /// <param name="clusterControl"></param>
    /// <returns></returns>
    [HttpPost("SaveCompanyClusterControl")]
    [ActionPermission(ActionType.Button, "保存单位体检套餐控制", "单位业务")]
    public async Task<bool> SaveCompanyClusterControl([FromBody] CompanyClusterControl clusterControl)
    {
        return await _companyService.SaveCompanyClusterControl(clusterControl);
    }

    /// <summary>
    /// 获取单位套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    [HttpPost("GetMapCompanyClusterComb")]
    [ActionPermission(ActionType.Query, "获取单位套餐对应的组合", "基础业务")]
    public async Task<MapCompanyClusterCombOutput[]> GetMapCompanyClusterComb([FromBody] MapCompanyClusterCombInput clusterInput)
    {
        return await _companyService.GetMapCompanyClusterComb(clusterInput);
    }

    /// <summary>
    /// 保存团体套餐加收的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    [HttpPost("SaveCompanyClusterExtraComb")]
    [ActionPermission(ActionType.Button, "保存团体套餐加收的项目", "基础业务")]
    public async Task<bool> SaveCompanyClusterExtraComb([FromBody] ClusterExtraComb[] extraCombs)
    {
        return await _basicCodeService.SaveMapCompanyClusterExtraComb(extraCombs);
    }

    [HttpPost("GetCompanyClusterExtraComb")]
    [ActionPermission(ActionType.Query, "获取个检套餐外的项目", "基础业务")]
    public async Task<MapClusterExtraCombOutput[]> GetCompanyClusterExtraComb([FromBody] MapClusterExtraCombInput input)
    {
        return await _basicCodeService.GetCompanyClusterExtraComb(input);
    }

}