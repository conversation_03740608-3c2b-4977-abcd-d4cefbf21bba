namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单服务
/// </summary>
public class OrderService : BizDbRepository<PersonOrder>, IOrderService
{
    private readonly ILogger<OrderService> _logger;
    private readonly INumberSourceService _numberSourceService;

    public OrderService(ILogger<OrderService> logger, INumberSourceService numberSourceService)
    {
        _logger = logger;
        _numberSourceService = numberSourceService;
    }

    /// <summary>
    /// 添加个检订单流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> AddPersonOrderProcess(PersonOrderInput input)
    {
        // 预约前检查数据
        if (!await CheckAddPersonOrderData(input))
            return false;

        // 添加订单
        if (!await AddPersonOrder(input))
            return false;

        // 更新号源
        if (!await _numberSourceService.UpdatePersonNumberSource(input.BeginTime, input.SourceTypeID, input.TimeSlotID))
            return false;

        return true;
    }

    /// <summary>
    /// 取消个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelPersonOrder(CancelOrder input)
    {
        try
        {
            var order = await Context.Queryable<PersonOrder>().FirstAsync(x => x.Id == input.OrderId && x.MembersId == input.MemberId);
            if (order == null)
            {
                Unify.SetError("订单不存在，请返回订单页面重新查询");
                return false;
            }

            var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.待支付, OrderStatus.已支付 };
            if (!statusArray.Contains(order.Status))
            {
                Unify.SetError("订单状态已变更,请刷新页面查看最新状态!");
                return false;
            }

            // 允许取消订单的最晚时间是体检前1天18:00
            var cancelDeadline = order.BeginTime.Date.AddDays(-1).AddHours(18);
            if (DateTime.Now >= cancelDeadline)
            {
                Unify.SetError("已过有效取消时间（体检前一日18:00前），无法取消");
                return false;
            }

            switch (order.Status)
            {
                case OrderStatus.已预约:
                case OrderStatus.待支付:
                    order.Status = OrderStatus.已取消;
                    order.EndTime = DateTime.Now;
                    break;
                case OrderStatus.已支付:
                    // 退款处理（封装单独方法）
                    break;
            };

            await Context.Updateable(order).ExecuteCommandAsync();

            // 更新号源
            if (!await _numberSourceService.UpdatePersonNumberSource(order.BeginTime, order.SourceTypeID, order.TimeSlotID))
                return false;

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("CancelPersonOrder:{0}", ex.Message);
            Unify.SetError("取消订单失败,请联系工作人员!");
            return false;
        }
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderList>> GetOrderList(OrderQuery query)
    {
        return query.OrderType switch
        {
            0 => await GetAllOrders(query.MemberId),
            1 => await GetPersonOrder(query.MemberId),
            2 => await GetGroupOrder(query.MemberId),
            3 => await GetPayRecord(query.MemberId),
            _ => Unify.SetError("无效的查询订单类型")
        };

        #region 内部方法(获取所有类型订单)
        async Task<List<OrderList>> GetAllOrders(string memberId)
        {
            // 并行执行三种订单查询
            var personTask = GetPersonOrder(memberId);
            var groupTask = GetGroupOrder(memberId);
            var payTask = GetPayRecord(memberId);

            await Task.WhenAll(personTask, groupTask, payTask);

            var result = new List<OrderList>();
            result.AddRange(await personTask);
            result.AddRange(await groupTask);
            result.AddRange(await payTask);

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query)
    {
        return await Context.Queryable<OrderDetail>()
            .Where(x => x.OrderId == query.OrderId)
            .ToListAsync();
    }

    #region 私有方法
    /// <summary>
    /// 预约前检查数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<bool> CheckAddPersonOrderData(PersonOrderInput input)
    {
        // 检查组合的数据是否为空
        if (input.OrderDetail.Count == 0)
        {
            Unify.SetError("预约的组合数据不能为空,请检查后再试!");
            return false;
        }

        // 查询预约时段有无号源
        var source = await Context.Queryable<NS_IndividualSource>()
                    .Where(x => x.Date == input.BeginTime && x.SourceTypeID == input.SourceTypeID && x.TimeSlotID == input.TimeSlotID)
                    .FirstAsync();

        if (source == null || source.AvailableCapacity <= 0)
        {
            Unify.SetError("暂无号源！请留意日期号源数");
            return false;
        }

        // 查询有无未缴费的订单，如果有则需要缴费后才能继续预约
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
        if (Context.Queryable<PersonOrder>().Any(x => x.CardNo == input.CardNo && x.BeginTime.Date > DateTime.Now.Date && SqlFunc.ContainsArray(statusArray, x.Status)))
        {
            Unify.SetError("已存在体检预约！请先完成已约的订单或取消重新预约");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<bool> AddPersonOrder(PersonOrderInput input)
    {
        try
        {
            var order = input.Adapt<PersonOrder>();
            order.Id = IDUtils.GetId();
            order.Status = OrderStatus.已预约;
            order.CreateTime = DateTime.Now;
            order.IsVip = order.TotalPrice > 3000;// 总价大于3000为vip,规则可以改

            var orderDetail = input.OrderDetail.Adapt<List<OrderDetail>>();
            orderDetail.ForEach(x => x.OrderId = order.Id);

            // 添加订单及详情
            await Context.Insertable(order).ExecuteCommandAsync();
            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            return true;
        }
        catch (Exception ex)
        {
            Unify.SetError("添加订单失败,请联系工作人员!");
            _logger.LogError(ex, "AddPersonOrder参数：{@message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPersonOrder(string memberId)
    {
        return await Context.Queryable<PersonOrder>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetGroupOrder(string memberId)
    {
        // TODO:团体订单表暂未创建
        return [];
    }

    /// <summary>
    /// 获取缴费订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPayRecord(string memberId)
    {
        return await Context.Queryable<PayRecord>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }
    #endregion
}
