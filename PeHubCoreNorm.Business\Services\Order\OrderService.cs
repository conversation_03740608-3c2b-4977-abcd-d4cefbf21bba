﻿using SqlSugar;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单服务
/// </summary>
public class OrderService : BizDbRepository<PersonOrder>, IOrderService
{
    private readonly ILogger<OrderService> _logger;

    public OrderService(ILogger<OrderService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> AddPersonOrder(PersonOrderInput input)
    {
        // TODO:查询预约时段有无号源

        //查询有无未缴费的订单，如果有则需要缴费后才能继续预约
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
        if (Context.Queryable<PersonOrder>().Any(x => x.CardNo == input.CardNo && x.BeginTime.Date > DateTime.Now.Date && SqlFunc.ContainsArray(statusArray, x.Status)))
        {
            Unify.SetError("已存在体检预约！请先完成已约的订单或取消重新预约");
            return false;
        }

        if (input.OrderDetail.Count == 0)
        {
            Unify.SetError("预约的组合数据不能为空,请检查后再试!");
            return false;
        }

        var order = input.Adapt<PersonOrder>();
        order.Id = IDUtils.GetId();
        order.Status = OrderStatus.已预约;
        order.CreateTime = DateTime.Now;
        order.IsVip = order.TotalPrice > 3000;// 总价大于3000为vip,规则可以改

        var orderDetail = input.OrderDetail.Select(x => new OrderDetail
        {
            CombCode = x.CombCode,
            CombName = x.CombName,
            Price = x.Price,
            CombType = x.CombType,
            OrderType = x.OrderType
        }).ToArray();

        try
        {
            Context.Ado.BeginTran();
            // 添加订单及详情
            await Context.Insertable(order).ExecuteCommandAsync();
            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            // TODO:更新号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("AddPersonOrder:{0}", ex.Message);
            Unify.SetError("生成订单异常,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 取消个检未支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelNoPayPersonOrder(CancelOrder input)
    {
        var order = await Context.Queryable<PersonOrder>()
            .Where(x => x.Id == input.OrderId && x.MembersId == input.MemberId && x.Status == OrderStatus.待支付)
            .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在，请返回订单页面重新查询");
            return false;
        }

        if (DateTime.Now.Date >= order.BeginTime.Date)
        {
            Unify.SetError("已过有效取消时间，无法取消!");
            return false;
        }

        try
        {
            Context.Ado.BeginTran();

            order.Status = OrderStatus.已取消;
            order.EndTime = DateTime.Now;
            await Context.Updateable(order).ExecuteCommandAsync();

            // TODO:恢复号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("CancelNoPayPersonOrder:{0}", ex.Message);
            Unify.SetError("取消未支付订单失败,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 取消个检支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelPayPersonOrder(CancelOrder input)
    {
        var order = await Context.Queryable<PersonOrder>()
           .Where(x => x.Id == input.OrderId && x.MembersId == input.MemberId && x.Status == OrderStatus.已支付)
           .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在，请返回订单页面重新查询");
            return false;
        }

        if (DateTime.Now.Date >= order.BeginTime.Date)
        {
            Unify.SetError("已过有效取消时间，无法取消!");
            return false;
        }

        try
        {
            Context.Ado.BeginTran();

            // TODO: 微信退费

            order.Status = OrderStatus.已退费;
            order.EndTime = DateTime.Now;
            await Context.Updateable(order).ExecuteCommandAsync();

            // TODO:恢复号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("CancelPayPersonOrder:{0}", ex.Message);
            Unify.SetError("取消支付订单失败,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderList>> GetOrderList(OrderQuery query)
    {
        return query.OrderType switch
        {
            0 => await GetAllOrders(query.MemberId),
            1 => await GetPersonOrder(query.MemberId),
            2 => await GetGroupOrder(query.MemberId),
            3 => await GetPayRecord(query.MemberId),
            _ => Unify.SetError("无效的查询订单类型")
        };

        #region 内部方法(获取所有类型订单)
        async Task<List<OrderList>> GetAllOrders(string memberId)
        {
            // 并行执行三种订单查询
            var personTask = GetPersonOrder(memberId);
            var groupTask = GetGroupOrder(memberId);
            var payTask = GetPayRecord(memberId);

            await Task.WhenAll(personTask, groupTask, payTask);

            var result = new List<OrderList>();
            result.AddRange(await personTask);
            result.AddRange(await groupTask);
            result.AddRange(await payTask);

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query)
    {
        return await Context.Queryable<OrderDetail>()
            .Where(x => x.OrderId == query.OrderId)
            .ToListAsync();
    }

    #region 团检订单相关方法

    /// <summary>
    /// 添加团检订单
    /// </summary>
    /// <param name="input">团检订单输入</param>
    /// <returns></returns>
    public async Task<bool> AddTeamOrder(TeamOrderInput input)
    {
        try
        {
            // 检查是否已存在相同证件号的未完成订单
            var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
            if (Context.Queryable<TeamOrder>().Any(x => x.CardNo == input.CardNo &&
                x.CompanyCode == input.CompanyCode &&
                x.CompanyTimes == input.CompanyTimes &&
                x.BeginTime.Date > DateTime.Now.Date &&
                SqlFunc.ContainsArray(statusArray, x.Status)))
            {
                Unify.SetError("该人员在此单位此次体检中已存在预约！请先完成已约的订单或取消重新预约");
                return false;
            }

            if (input.OrderDetail.Count == 0)
            {
                Unify.SetError("预约的组合数据不能为空,请检查后再试!");
                return false;
            }

            var order = input.Adapt<TeamOrder>();
            order.Id = IDUtils.GetId();
            order.Status = OrderStatus.已预约;
            order.CreateTime = DateTime.Now;
            order.IsVip = order.TotalPrice > 3000; // 总价大于3000为vip

            var orderDetail = input.OrderDetail.Select(x => new OrderDetail
            {
                Id = IDUtils.GetId(),
                OrderId = order.Id,
                CombCode = x.CombCode,
                CombName = x.CombName,
                Price = x.Price,
                CombType = x.CombType,
                OrderType = x.OrderType
            }).ToArray();

            Context.Ado.BeginTran();

            // 添加订单及详情
            await Context.Insertable(order).ExecuteCommandAsync();
            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            // TODO: 更新号源

            Context.Ado.CommitTran();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("AddTeamOrder:{0}", ex.Message);
            Unify.SetError("生成团检订单异常,请联系工作人员!");
            Context.Ado.RollbackTran();
            return false;
        }
    }

    /// <summary>
    /// 批量添加团检订单
    /// </summary>
    /// <param name="input">批量团检订单输入</param>
    /// <returns></returns>
    public async Task<BatchTeamOrderResult> AddBatchTeamOrder(BatchTeamOrderInput input)
    {
        var result = new BatchTeamOrderResult
        {
            TotalCount = input.TeamOrders.Count
        };

        try
        {
            Context.Ado.BeginTran();

            foreach (var (teamOrder, index) in input.TeamOrders.Select((order, i) => (order, i)))
            {
                try
                {
                    // 设置批量订单的公共信息
                    teamOrder.CompanyCode = input.CompanyCode;
                    teamOrder.CompanyName = input.CompanyName;
                    teamOrder.CompanyTimes = input.CompanyTimes;
                    teamOrder.BeginTime = input.BeginTime;
                    teamOrder.TimeId = input.TimeId;
                    teamOrder.TimeSlotName = input.TimeSlotName;

                    var addResult = await AddTeamOrder(teamOrder);
                    if (addResult)
                    {
                        result.SuccessCount++;
                        // 这里需要获取刚添加的订单ID，可以通过修改AddTeamOrder方法返回ID
                    }
                    else
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = teamOrder.Name,
                            CardNo = teamOrder.CardNo,
                            ErrorMessage = "添加订单失败"
                        });
                    }
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.FailureDetails.Add(new BatchFailureDetail
                    {
                        Index = index,
                        Name = teamOrder.Name,
                        CardNo = teamOrder.CardNo,
                        ErrorMessage = ex.Message
                    });
                }
            }

            if (result.FailedCount == 0)
            {
                Context.Ado.CommitTran();
                result.Success = true;
                result.Message = $"批量添加成功，共添加{result.SuccessCount}条订单";
            }
            else
            {
                Context.Ado.RollbackTran();
                result.Success = false;
                result.Message = $"批量添加部分失败，成功{result.SuccessCount}条，失败{result.FailedCount}条";
            }
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            _logger.LogError("AddBatchTeamOrder:{0}", ex.Message);
            result.Success = false;
            result.Message = "批量添加团检订单异常";
        }

        return result;
    }

    /// <summary>
    /// 编辑团检订单
    /// </summary>
    /// <param name="input">团检订单编辑输入</param>
    /// <returns></returns>
    public async Task<bool> EditTeamOrder(TeamOrderEditInput input)
    {
        try
        {
            var order = await Context.Queryable<TeamOrder>().FirstAsync(x => x.Id == input.Id);
            if (order == null)
            {
                Unify.SetError("订单不存在");
                return false;
            }

            // 只有已预约状态的订单才能编辑
            if (order.Status != OrderStatus.已预约)
            {
                Unify.SetError("只有已预约状态的订单才能编辑");
                return false;
            }

            Context.Ado.BeginTran();

            // 更新订单信息
            order.Name = input.Name;
            order.CardNo = input.CardNo;
            order.CardType = input.CardType;
            order.Tel = input.Tel;
            order.Birthday = input.Birthday;
            order.Sex = input.Sex;
            order.MarryStatus = input.MarryStatus;
            order.ClusCode = input.ClusCode;
            order.ClusName = input.ClusName;
            order.TotalPrice = input.TotalPrice;
            order.TimeId = input.TimeId;
            order.TimeSlotName = input.TimeSlotName;
            order.BeginTime = input.BeginTime;
            order.IsVip = input.TotalPrice > 3000;

            await Context.Updateable(order).ExecuteCommandAsync();

            // 删除原有订单详情
            await Context.Deleteable<OrderDetail>().Where(x => x.OrderId == input.Id).ExecuteCommandAsync();

            // 添加新的订单详情
            var orderDetail = input.OrderDetail.Select(x => new OrderDetail
            {
                Id = IDUtils.GetId(),
                OrderId = input.Id,
                CombCode = x.CombCode,
                CombName = x.CombName,
                Price = x.Price,
                CombType = x.CombType,
                OrderType = x.OrderType
            }).ToArray();

            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            Context.Ado.CommitTran();
            return true;
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            _logger.LogError("EditTeamOrder:{0}", ex.Message);
            Unify.SetError("编辑团检订单异常,请联系工作人员!");
            return false;
        }
    }

    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input">取消团检订单输入</param>
    /// <returns></returns>
    public async Task<bool> CancelTeamOrder(CancelTeamOrderInput input)
    {
        try
        {
            var order = await Context.Queryable<TeamOrder>()
                .Where(x => x.Id == input.OrderId && x.MembersId == input.MemberId)
                .FirstAsync();

            if (order == null)
            {
                Unify.SetError("订单不存在，请返回订单页面重新查询");
                return false;
            }

            // 检查订单状态
            if (order.Status == OrderStatus.已取消 || order.Status == OrderStatus.已退费)
            {
                Unify.SetError("订单已取消，无需重复操作");
                return false;
            }

            if (DateTime.Now.Date >= order.BeginTime.Date)
            {
                Unify.SetError("已过有效取消时间，无法取消!");
                return false;
            }

            Context.Ado.BeginTran();

            // 根据订单状态决定取消方式
            if (order.Status == OrderStatus.已支付)
            {
                // TODO: 微信退费
                order.Status = OrderStatus.已退费;
            }
            else
            {
                order.Status = OrderStatus.已取消;
            }

            order.CancelTime = DateTime.Now;
            order.EndTime = DateTime.Now;
            order.ErrorMsg = input.CancelReason;

            await Context.Updateable(order).ExecuteCommandAsync();

            // TODO: 恢复号源

            Context.Ado.CommitTran();
            return true;
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            _logger.LogError("CancelTeamOrder:{0}", ex.Message);
            Unify.SetError("取消团检订单异常,请联系工作人员!");
            return false;
        }
    }

    /// <summary>
    /// 获取团检订单分页列表
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<TeamOrderBriefOutput>> GetTeamOrderPageList(TeamOrderQueryInput input)
    {
        var query = Context.Queryable<TeamOrder>()
            .WhereIF(!string.IsNullOrEmpty(input.MembersId), x => x.MembersId == input.MembersId)
            .WhereIF(!string.IsNullOrEmpty(input.CompanyCode), x => x.CompanyCode == input.CompanyCode)
            .WhereIF(!string.IsNullOrEmpty(input.CompanyName), x => x.CompanyName.Contains(input.CompanyName))
            .WhereIF(input.CompanyTimes.HasValue, x => x.CompanyTimes == input.CompanyTimes.Value)
            .WhereIF(input.Status.HasValue, x => x.Status == input.Status.Value)
            .WhereIF(!string.IsNullOrEmpty(input.Name), x => x.Name.Contains(input.Name))
            .WhereIF(!string.IsNullOrEmpty(input.CardNo), x => x.CardNo.Contains(input.CardNo))
            .WhereIF(!string.IsNullOrEmpty(input.Tel), x => x.Tel.Contains(input.Tel))
            .WhereIF(input.StartTime.HasValue, x => x.CreateTime >= input.StartTime.Value)
            .WhereIF(input.EndTime.HasValue, x => x.CreateTime <= input.EndTime.Value)
            .WhereIF(input.BeginTimeStart.HasValue, x => x.BeginTime >= input.BeginTimeStart.Value)
            .WhereIF(input.BeginTimeEnd.HasValue, x => x.BeginTime <= input.BeginTimeEnd.Value)
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), x =>
                x.Name.Contains(input.SearchKey) ||
                x.CardNo.Contains(input.SearchKey) ||
                x.Tel.Contains(input.SearchKey) ||
                x.CompanyName.Contains(input.SearchKey))
            .OrderByIF(!string.IsNullOrEmpty(input.SortField), $"{input.SortField} {input.SortOrder}")
            .OrderBy(x => x.CreateTime, OrderByType.Desc)
            .Select<TeamOrderBriefOutput>();

        return await query.ToPagedListAsync(input.pageNum, input.pageSize);
    }

    /// <summary>
    /// 根据ID获取团检订单详情
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    public async Task<TeamOrderOutput> GetTeamOrderById(string orderId)
    {
        var order = await Context.Queryable<TeamOrder>()
            .Where(x => x.Id == orderId)
            .Select<TeamOrderOutput>()
            .FirstAsync();

        if (order != null)
        {
            // 获取订单详情
            order.OrderDetails = await Context.Queryable<OrderDetail>()
                .Where(x => x.OrderId == orderId)
                .Select<TeamOrderDetailOutput>()
                .ToListAsync();
        }

        return order;
    }

    /// <summary>
    /// 获取团检订单统计信息
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">单位体检次数</param>
    /// <returns></returns>
    public async Task<TeamOrderStatisticsOutput> GetTeamOrderStatistics(string companyCode, int? companyTimes = null)
    {
        var query = Context.Queryable<TeamOrder>()
            .Where(x => x.CompanyCode == companyCode)
            .WhereIF(companyTimes.HasValue, x => x.CompanyTimes == companyTimes.Value);

        var orders = await query.ToListAsync();

        var statistics = new TeamOrderStatisticsOutput
        {
            CompanyCode = companyCode,
            CompanyName = orders.FirstOrDefault()?.CompanyName,
            CompanyTimes = companyTimes ?? orders.FirstOrDefault()?.CompanyTimes ?? 0,
            TotalCount = orders.Count,
            BookedCount = orders.Count(x => x.Status == OrderStatus.已预约),
            PaidCount = orders.Count(x => x.Status == OrderStatus.已支付),
            CompletedCount = orders.Count(x => x.Status == OrderStatus.已完成),
            CancelledCount = orders.Count(x => x.Status == OrderStatus.已取消 || x.Status == OrderStatus.已退费),
            TotalAmount = orders.Sum(x => x.TotalPrice),
            PaidAmount = orders.Where(x => x.Status == OrderStatus.已支付 || x.Status == OrderStatus.已完成).Sum(x => x.TotalPrice)
        };

        return statistics;
    }

    /// <summary>
    /// 批量取消团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <param name="cancelReason">取消原因</param>
    /// <returns></returns>
    public async Task<BatchTeamOrderResult> BatchCancelTeamOrder(List<string> orderIds, string cancelReason)
    {
        var result = new BatchTeamOrderResult
        {
            TotalCount = orderIds.Count
        };

        try
        {
            Context.Ado.BeginTran();

            foreach (var (orderId, index) in orderIds.Select((id, i) => (id, i)))
            {
                try
                {
                    var order = await Context.Queryable<TeamOrder>().FirstAsync(x => x.Id == orderId);
                    if (order == null)
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = "未知",
                            CardNo = "未知",
                            ErrorMessage = "订单不存在"
                        });
                        continue;
                    }

                    if (order.Status == OrderStatus.已取消 || order.Status == OrderStatus.已退费)
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = order.Name,
                            CardNo = order.CardNo,
                            ErrorMessage = "订单已取消"
                        });
                        continue;
                    }

                    if (DateTime.Now.Date >= order.BeginTime.Date)
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = order.Name,
                            CardNo = order.CardNo,
                            ErrorMessage = "已过有效取消时间"
                        });
                        continue;
                    }

                    // 取消订单
                    if (order.Status == OrderStatus.已支付)
                    {
                        order.Status = OrderStatus.已退费;
                    }
                    else
                    {
                        order.Status = OrderStatus.已取消;
                    }

                    order.CancelTime = DateTime.Now;
                    order.EndTime = DateTime.Now;
                    order.ErrorMsg = cancelReason;

                    await Context.Updateable(order).ExecuteCommandAsync();

                    result.SuccessCount++;
                    result.SuccessOrderIds.Add(orderId);
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.FailureDetails.Add(new BatchFailureDetail
                    {
                        Index = index,
                        Name = "未知",
                        CardNo = "未知",
                        ErrorMessage = ex.Message
                    });
                }
            }

            Context.Ado.CommitTran();
            result.Success = true;
            result.Message = $"批量取消完成，成功{result.SuccessCount}条，失败{result.FailedCount}条";
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            _logger.LogError("BatchCancelTeamOrder:{0}", ex.Message);
            result.Success = false;
            result.Message = "批量取消团检订单异常";
        }

        return result;
    }

    /// <summary>
    /// 导入团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <returns></returns>
    public async Task<BatchTeamOrderResult> ImportTeamOrders(List<string> orderIds)
    {
        var result = new BatchTeamOrderResult
        {
            TotalCount = orderIds.Count
        };

        try
        {
            Context.Ado.BeginTran();

            foreach (var (orderId, index) in orderIds.Select((id, i) => (id, i)))
            {
                try
                {
                    var order = await Context.Queryable<TeamOrder>().FirstAsync(x => x.Id == orderId);
                    if (order == null)
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = "未知",
                            CardNo = "未知",
                            ErrorMessage = "订单不存在"
                        });
                        continue;
                    }

                    if (order.Status != OrderStatus.已支付)
                    {
                        result.FailedCount++;
                        result.FailureDetails.Add(new BatchFailureDetail
                        {
                            Index = index,
                            Name = order.Name,
                            CardNo = order.CardNo,
                            ErrorMessage = "只有已支付的订单才能导入"
                        });
                        continue;
                    }

                    // 导入订单
                    order.Status = OrderStatus.已导入;
                    order.RegNo = GenerateRegNo(); // 生成体检流水号

                    await Context.Updateable(order).ExecuteCommandAsync();

                    result.SuccessCount++;
                    result.SuccessOrderIds.Add(orderId);
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.FailureDetails.Add(new BatchFailureDetail
                    {
                        Index = index,
                        Name = "未知",
                        CardNo = "未知",
                        ErrorMessage = ex.Message
                    });
                }
            }

            Context.Ado.CommitTran();
            result.Success = true;
            result.Message = $"批量导入完成，成功{result.SuccessCount}条，失败{result.FailedCount}条";
        }
        catch (Exception ex)
        {
            Context.Ado.RollbackTran();
            _logger.LogError("ImportTeamOrders:{0}", ex.Message);
            result.Success = false;
            result.Message = "批量导入团检订单异常";
        }

        return result;
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 生成体检流水号
    /// </summary>
    /// <returns></returns>
    private string GenerateRegNo()
    {
        return DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);
    }

    #endregion

    #region 私有方法
    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPersonOrder(string memberId)
    {
        return await Context.Queryable<PersonOrder>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetGroupOrder(string memberId)
    {
        return await Context.Queryable<TeamOrder>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取缴费订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPayRecord(string memberId)
    {
        return await Context.Queryable<PayRecord>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }
    #endregion

}
