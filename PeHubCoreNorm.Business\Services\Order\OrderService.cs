﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单服务
/// </summary>
public class OrderService : BizDbRepository<PersonOrder>, IOrderService
{

    public OrderService()
    {
            
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderList>> GetOrderList(OrderQuery query)
    {
        return query.OrderType switch
        {
            0 => await GetAllOrders(query.MemberId),
            1 => await GetPersonOrder(query.MemberId),
            2 => await GetGroupOrder(query.MemberId),
            3 => await GetPayRecord(query.MemberId),
            _ => Unify.SetError("无效的查询订单类型")
        };

        #region 内部方法(获取所有类型订单)
        async Task<List<OrderList>> GetAllOrders(string memberId)
        {
            // 并行执行三种订单查询
            var personTask = GetPersonOrder(memberId);
            var groupTask = GetGroupOrder(memberId);
            var payTask = GetPayRecord(memberId);

            await Task.WhenAll(personTask, groupTask, payTask);

            var result = new List<OrderList>();
            result.AddRange(await personTask);
            result.AddRange(await groupTask);
            result.AddRange(await payTask);

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query)
    {
        return await Context.Queryable<OrderDetail>()
            .Where(x => x.OrderId == query.OrderId)
            .ToListAsync();
    }

    #region 私有方法
    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPersonOrder(string memberId) 
    {
        return await Context.Queryable<PersonOrder>()
            .Where(x=>x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetGroupOrder(string memberId)
    {
        // TODO:团体订单表暂未创建
        return [];
    }

    /// <summary>
    /// 获取缴费订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPayRecord(string memberId)
    {
        return await Context.Queryable<PayRecord>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }
    #endregion

}
