using SqlSugar;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单服务
/// </summary>
public class OrderService : BizDbRepository<PersonOrder>, IOrderService
{
    private readonly ILogger<OrderService> _logger;

    public OrderService(ILogger<OrderService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> AddPersonOrder(PersonOrderInput input)
    {
        // TODO:查询预约时段有无号源

        //查询有无未缴费的订单，如果有则需要缴费后才能继续预约
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
        if (Context.Queryable<PersonOrder>().Any(x => x.CardNo == input.CardNo && x.BeginTime.Date > DateTime.Now.Date && SqlFunc.ContainsArray(statusArray, x.Status)))
        {
            Unify.SetError("已存在体检预约！请先完成已约的订单或取消重新预约");
            return false;
        }

        if (input.OrderDetail.Count == 0)
        {
            Unify.SetError("预约的组合数据不能为空,请检查后再试!");
            return false;
        }

        var order = input.Adapt<PersonOrder>();
        order.Id = IDUtils.GetId();
        order.Status = OrderStatus.已预约;
        order.CreateTime = DateTime.Now;
        order.IsVip = order.TotalPrice > 3000;// 总价大于3000为vip,规则可以改

        var orderDetail = input.OrderDetail.Select(x => new OrderDetail
        {
            CombCode = x.CombCode,
            CombName = x.CombName,
            Price = x.Price,
            CombType = x.CombType,
            OrderType = x.OrderType
        }).ToArray();

        try
        {
            Context.Ado.BeginTran();
            // 添加订单及详情
            await Context.Insertable(order).ExecuteCommandAsync();
            await Context.Insertable(orderDetail).ExecuteCommandAsync();

            // TODO:更新号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("AddPersonOrder:{0}", ex.Message);
            Unify.SetError("生成订单异常,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 取消个检未支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelNoPayPersonOrder(CancelOrder input)
    {
        var order = await Context.Queryable<PersonOrder>()
            .Where(x => x.Id == input.OrderId && x.MembersId == input.MemberId && x.Status == OrderStatus.待支付)
            .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在，请返回订单页面重新查询");
            return false;
        }

        if (DateTime.Now.Date >= order.BeginTime.Date)
        {
            Unify.SetError("已过有效取消时间，无法取消!");
            return false;
        }

        try
        {
            Context.Ado.BeginTran();

            order.Status = OrderStatus.已取消;
            order.EndTime = DateTime.Now;
            await Context.Updateable(order).ExecuteCommandAsync();

            // TODO:恢复号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("CancelNoPayPersonOrder:{0}", ex.Message);
            Unify.SetError("取消未支付订单失败,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 取消个检支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CancelPayPersonOrder(CancelOrder input)
    {
        var order = await Context.Queryable<PersonOrder>()
           .Where(x => x.Id == input.OrderId && x.MembersId == input.MemberId && x.Status == OrderStatus.已支付)
           .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在，请返回订单页面重新查询");
            return false;
        }

        if (DateTime.Now.Date >= order.BeginTime.Date)
        {
            Unify.SetError("已过有效取消时间，无法取消!");
            return false;
        }

        try
        {
            Context.Ado.BeginTran();

            // TODO: 微信退费

            order.Status = OrderStatus.已退费;
            order.EndTime = DateTime.Now;
            await Context.Updateable(order).ExecuteCommandAsync();

            // TODO:恢复号源

            Context.Ado.CommitTran();

        }
        catch (Exception ex)
        {
            _logger.LogError("CancelPayPersonOrder:{0}", ex.Message);
            Unify.SetError("取消支付订单失败,请联系工作人员!");
            Context.Ado.RollbackTran();
        }

        return true;
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderList>> GetOrderList(OrderQuery query)
    {
        return query.OrderType switch
        {
            0 => await GetAllOrders(query.MemberId),
            1 => await GetPersonOrder(query.MemberId),
            2 => await GetGroupOrder(query.MemberId),
            3 => await GetPayRecord(query.MemberId),
            _ => Unify.SetError("无效的查询订单类型")
        };

        #region 内部方法(获取所有类型订单)
        async Task<List<OrderList>> GetAllOrders(string memberId)
        {
            // 并行执行三种订单查询
            var personTask = GetPersonOrder(memberId);
            var groupTask = GetGroupOrder(memberId);
            var payTask = GetPayRecord(memberId);

            await Task.WhenAll(personTask, groupTask, payTask);

            var result = new List<OrderList>();
            result.AddRange(await personTask);
            result.AddRange(await groupTask);
            result.AddRange(await payTask);

            return result;
        }
        #endregion
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query)
    {
        return await Context.Queryable<OrderDetail>()
            .Where(x => x.OrderId == query.OrderId)
            .ToListAsync();
    }

    #region 私有方法
    /// <summary>
    /// 获取个检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPersonOrder(string memberId)
    {
        return await Context.Queryable<PersonOrder>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetGroupOrder(string memberId)
    {
        // TODO:团体订单表暂未创建
        return [];
    }

    /// <summary>
    /// 获取缴费订单
    /// </summary>
    /// <param name="memberId"></param>
    /// <returns></returns>
    private async Task<List<OrderList>> GetPayRecord(string memberId)
    {
        return await Context.Queryable<PayRecord>()
            .Where(x => x.MembersId == memberId)
            .Select<OrderList>()
            .ToListAsync();
    }
    #endregion

}
