﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    public class CreateUnitPersonInput
    {

  

        /// <summary>
        /// 单位编码
        /// </summary>
        public string CompanyCode { get; set; }

      
        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }


        /// <summary>
        /// 部门
        /// </summary>
     
        public string Department { get; set; }

        /// <summary>
        /// 人员名单状态【0,1】【未激活，激活】
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public int Sex { get; set; }

        /// <summary>
        /// 民族
        /// </summary>
        public string Ethnic { get; set; }


        /// <summary>
        /// 婚姻状况
        /// </summary>
        public int Married {  get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }


       /// <summary>
       /// 电话
       /// </summary>
        public string Tel { get; set; }


        /// <summary>
        /// 证件号类型
        /// </summary>
        public int IdNumberType { get; set; }

  
        /// <summary>
        /// 证件号码
        /// </summary>
        public string IdNumber { get; set; }


        /// <summary>
        /// 套餐编码
        /// </summary>
        public string PackAgeCode { get; set; }


        /// <summary>
        /// 套餐名称
        /// </summary>
        public string PackAgeName { get; set; }

       /// <summary>
       /// 体检次数
       /// </summary>
        public int BatchNumber { get; set; }


       
    }

    public class Page 
    {
        /// <summary>
        ///     当前页码
        /// </summary>
        public virtual int pageNum { get; set; } = 1;

        /// <summary>
        ///     每页条数
        /// </summary>
        [Range(1, 2000, ErrorMessage = "页码容量超过最大限制")]
        public virtual int pageSize { get; set; } = 10;

 
    }

    public class QueryUnitPersonInput: Page
    {

        /// <summary>
        /// 单位编码
        /// </summary>
        public string? CompanyCode { get; set; }


        /// <summary>
        /// 人员名单状态【0,1】【未激活，激活】
        /// </summary>
        public int? Status { get; set; } = 0;

        
        /// <summary>
        /// 姓名
        /// </summary>
        public string? EmployeeName { get; set; }


        /// <summary>
        /// 体检次数
        /// </summary>
        public int? BatchNumber { get; set; } = 1;



        /// <summary>
        /// 套餐编码
        /// </summary>
        public string? PackAgeCode { get; set; }

    }
}
