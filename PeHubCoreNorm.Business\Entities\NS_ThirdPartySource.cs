﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 第三方号源信息表
    /// </summary>
    [SugarTable("NS_ThirdPartySource", TableDescription = "第三方号源信息表")]
    public class NS_ThirdPartySource : NS_BaseEntry
    {
        /// <summary>
        /// 第三方机构编码
        /// </summary>
        [SugarColumn(ColumnName = "ThirdPartyCode", ColumnDescription = "第三方机构编码")]
        public virtual string ThirdPartyCode { get; set; }
    }
}
