﻿using System;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 资讯
    /// </summary>
    [SugarTable("RealTimeInfo", TableDescription = "资讯")]
    public class RealTimeInfo : BaseEntity
    {

        /// <summary>
        /// 标题
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Title", ColumnDescription = "标题", Length = 50)]
        public virtual string Title { get; set; }


        /// <summary>
        /// 展示图片地址
        /// </summary>
        //[SugarColumn(IsNullable = false, ColumnName = "ShowPictures", ColumnDescription = "展示图片地址", Length = 2000)]
        //public virtual string ShowPicturesUrl { get; set; }

        /// <summary>
        /// 缩列图地址
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "DiagramUrl", ColumnDescription = "缩列图地址", Length = 2000)]
        public virtual string DiagramUrl { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态", Length = 10)]
        public virtual string Status { get; set; }

        /// <summary>
        /// TypeCode
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "TypeCode", ColumnDescription = "类别编码", Length = 30)]
        public virtual string TypeCode { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Content", ColumnDescription = "内容",Length = int.MaxValue)]
        public virtual string Content { get; set; }


    }
}
