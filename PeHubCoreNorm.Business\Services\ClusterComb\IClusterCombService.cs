﻿using PeHubCoreNorm.Business.Output;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐组合业务
/// </summary>
public interface IClusterCombService : ITransient
{
    /// <summary>
    /// 获取套餐列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<CodeClusterOutput[]> GetClusterList(ClusterQuery query);

    /// <summary>
    /// 获取套餐包含的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<ClusterClsCombs[]> GetClusterCombs(MapClusterCombInput query);

    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<ClusterClsCombs[]> GetMapClusterExtraComb(MapClusterExtraCombInput query);

    /// <summary>
    /// 获取个检套餐加项包
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<ClusterAddPackageDataOutput[]> GetClusterAddPackage(ClusterAddPackageInput query);

    /// <summary>
    /// 获取项目的相关关系
    /// </summary>
    /// <param name="comb"></param>
    /// <returns></returns>
    Task<ComRelationOutput> GetCombRelation(CombRelation comb);

    /// <summary>
    /// 计算项目加项
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    Task<CalCulateCombs> CalCulateCombsAsync(CalCulateCombs calcCombs);

    /// <summary>
    /// 检验项目关系是否通过
    /// </summary>
    /// <param name="calcCombs"></param>
    /// <returns></returns>
    Task<bool> VerifyCombIsPass(List<ReturnCombs> calcCombs);
}
