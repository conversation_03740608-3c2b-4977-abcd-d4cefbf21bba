﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 同步服务接口
/// </summary>
public interface ISyncService:ITransient
{
    #region 同步体检单位业务
    /// <summary>
    /// 同步单位信息
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCompany();

    /// <summary>
    /// 同步单位体检次数信息
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCompanyTimes();

    /// <summary>
    /// 同步单位套餐信息
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCompanyCluster();

    /// <summary>
    /// 同步单位套餐的组合对应信息
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCompanyClusterComb();
    #endregion

    #region 同步个检套餐及对应组合
    /// <summary>
    /// 同步个检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncClusterAndCombs();
    #endregion

    #region 同步体检组合
    /// <summary>
    /// 同步组合
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCodeItemComb();
    #endregion

    #region 同步项目分类
    /// <summary>
    /// 同步项目分类
    /// </summary>
    /// <returns></returns>
    Task<bool> SyncCodeItemCls();
    #endregion
}
