﻿using Microsoft.AspNetCore.Http;
using PeHubCoreNorm.Utils.IOUtils;
using SharpCompress.Common;
using System.IO;

namespace PeHubCoreNorm.System;

[ApiExplorerSettings(GroupName = "Dev")]
[Route("dev/[controller]")]
[IgnoreLog]

public class UploadController : BaseControllerAuthorize
{
	private readonly IDictService _dictService;
	public UploadController(IDictService dictService)
	{
		_dictService = dictService;
	}

	[HttpPost("upload")]
	[DisableRequestSizeLimit]
	[ActionPermission(ActionType.Button, "各类文件上传")]
	[AllowAnonymous]
	public async Task<IActionResult> UploadFile(IFormFile file,string type)
	{
		var list=await _dictService.GetValuesByDictValue(PeHubCoreNormConst.FILE_TYPE);

		if (list.Count(x => x == type)==0)
		{
			return BadRequest("上传类型不存在！");
		}
		var request = new FileUploadRequest
		{
			File = file,
			AllowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx", ".pdf", ".ppt" },
			MaxFileSize = 30 * 1024 * 1024, // 10MB
			CustomFileName = type+"_" + DateTime.Now.ToString("yyyyMMddHHmmss"),
			SaveFilePath=App.WebHostEnvironment.WebRootPath,
			SubDirectory = type
		};

		FileUploadResponse result = await FileUploadUtils.UploadFileAsync(request);

		if (result.Success)
		{
			return Ok(new
			{
				result.FileName,
				FileSize = $"{result.FileSize / 1024} KB",
				RelativePath = result.FilePath,
				result.VirtualPath
			});
		}

		return BadRequest(result.Message);
	}

}