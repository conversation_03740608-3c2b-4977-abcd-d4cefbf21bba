﻿    
using PeHubCoreNorm.Utils.IOUtils;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 体检须知和轮播图业务接口
/// </summary>
public interface INoticeAndImageService : ITransient
{
    /// <summary>
    /// 新增轮播图
    /// </summary>
    /// <param name="carouselImage"></param>
    /// <returns></returns>
    bool AddCarouselImages(CarouselImage carouselImage);
    /// <summary>
    /// 新增体检须知
    /// </summary>
    /// <param name="notice"></param>
    /// <returns></returns>
    bool AddNotices(Notice notice);
    bool AddRealTimeInfos(RealTimeInfo info);

    /// <summary>
    /// 删除轮播图
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    bool DeleteCarouselImage(string[] ids);
    //Task<FileUploadResponse> DeleteFiles(string filePath);

    /// <summary>
    /// 删除资讯
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    bool DeleteRealTimeInfo(string[] ids);

    /// <summary>
    /// 获取全部体检须知
    /// </summary>
    /// <returns></returns>
    List<Notice> GetAllNotices();

    /// <summary>
    /// 获取轮播图
    /// </summary>
    /// <param name="imageType"></param>
    /// <param name="state"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<CarouselImage>> GetCarouselImages(CarouselImageInput image);
    /// <summary>
    /// 根据页面获取轮播图
    /// </summary>
    /// <param name="page"></param>
    /// <returns></returns>
    Task<Object> GetCarouselImagesByPage(string page);

    /// <summary>
    /// 根据Id获取体检须知
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Notice> GetNoticeById(string id);
    /// <summary>
    /// 根据typeCode获取体检须知
    /// </summary>
    /// <param name="typeCode"></param>
    /// <returns></returns>
    Task<Notice> GetNoticesByTypeCode(string typeCode);

    /// <summary>
    /// 获取体检须知(根据TypeCode，Status）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<Notice>> GetSqlNotices(NoticeInput input);
    /// <summary>
    /// 获取资讯（筛选、分页,不返回富文本内容）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<RealTimeInfo>> GetTimeInfos(RealTimeInfoInput input);
    /// <summary>
    /// 根据typeCode获取num个最新资讯信息
    /// </summary>
    /// <param name="typeCode"></param>
    /// <param name="num"></param>
    /// <returns></returns>
    Task<Object> GetNewRealTimeInfos(string typeCode, int num);

    /// <summary>
    /// 更新轮播图
    /// </summary>
    /// <param name="carouselImage"></param>
    /// <returns></returns>
    bool UpdateCarouselImages(CarouselImage carouselImage);

    /// <summary>
    /// 更新轮播图状态
    /// </summary>
    /// <param name="carouselImage"></param>
    /// <returns></returns>
    bool UpdateCarouselImageStatus(CarouselImage carouselImage);
    /// <summary>
    /// 根据Id更新条件须知内容
    /// </summary>
    /// <param name="notice"></param>
    /// <returns></returns>
    bool UpdateNoticeContentById(UpdateNoticeInput notice);

    /// <summary>
    /// 根据id修改体检须知的状态
    /// </summary>
    /// <param name="notice"></param>
    /// <returns></returns>
    bool UpdateNoticeStatus(UpdateStatusInput notice);
    /// <summary>
    /// 根据id更新资讯内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    bool UpdateRealTimeInfoContentById(UpdateContentInput input);
    /// <summary>
    /// 更新资讯（Title、Status、DiagramUrl、TypeCode）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    bool UpdateRealTimeInfos(UpdateRealTimeInfosInput input);

    /// <summary>
    /// 根据id修改资讯状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    bool UpdateRealTimeInfoStatus(UpdateStatusInput input);
    /// <summary>
    /// 根据id获取资讯内容
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<string> GetNewRealTimeContent(string id);
}