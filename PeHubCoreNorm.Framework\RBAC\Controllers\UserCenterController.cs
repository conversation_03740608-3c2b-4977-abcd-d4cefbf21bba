﻿namespace PeHubCoreNorm.RBAC;

[ApiExplorerSettings(GroupName = "RBAC")]
[Route("sys/[controller]")]
public class UserCenterController : BaseControllerAuthorize
{
    private readonly ISysMenuService _sysMenuService;
    private readonly ISysUserService _sysUserService;

    public UserCenterController(ISysMenuService sysMenuService,
        ISysUserService sysUserService)
    {
        _sysMenuService = sysMenuService;
        _sysUserService = sysUserService;
    }

    /// <summary>
    ///     获取当前登陆者的菜单信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("loginMenu")]
    [ActionPermission(ActionType.Query, "获取当前登陆者的菜单信息", "用户中心管理")]
    [IgnoreLog]
    public async Task<dynamic> LoginMenu(string id)
    {
        var OwnMenus = await _sysMenuService.GetOwnMenus(UserManager.UserId);

        if (!string.IsNullOrEmpty(id)) return OwnMenus.FirstOrDefault(x => x.Id == id).Children.ToList();
        return OwnMenus;
    }

    /// <summary>
    ///     未读消息数
    /// </summary>
    /// <returns></returns>
    [HttpGet("UnReadCount")]
    [ActionPermission(ActionType.Query, "未读消息数", "用户中心管理")]
    public async Task<dynamic> UnReadCount()
    {
        return await Task.FromResult(0);
    }

    /// <summary>
    ///     修改密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("updatePassword")]
    [ActionPermission(ActionType.Button, "修改密码", "用户中心管理")]
    public async Task UpdatePassword([FromBody] UpdatePasswordInput input)
    {
        await _sysUserService.UpdatePassword(input);
    }

    /// <summary>
    ///     修改头像
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("updateAvatar")]
    [ActionPermission(ActionType.Button, "修改头像", "用户中心管理")]
    public async Task<dynamic> UpdateAvatar([FromForm] BaseFileInput input)
    {
        return await _sysUserService.UpdateAvatar(input);
    }

    /// <summary>
    ///     编辑个人信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("updateUserInfo")]
    [ActionPermission(ActionType.Button, "编辑个人信息", "用户中心管理")]
    public async Task UpdateUserInfo([FromBody] UpdateInfoInput input)
    {
        await _sysUserService.UpdateUserInfo(input);
    }


    /// <summary>
    ///     修改默认模块
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("setDefaultModule")]
    [ActionPermission(ActionType.Button, "设置默认", "用户中心管理")]
    [IgnoreLog]
    public async Task SetDefaultModule([FromBody] SetDefaultModuleInput input)
    {
        await _sysUserService.SetDefaultModule(input);
    }
}