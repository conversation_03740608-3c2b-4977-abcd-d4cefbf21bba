﻿<Project Sdk="Microsoft.NET.Sdk">

<PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
	<GenerateDocumentationFile>True</GenerateDocumentationFile>
</PropertyGroup>

<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<Optimize>False</Optimize>
</PropertyGroup>

<ItemGroup>
	<ProjectReference Include="..\PeHubCoreNorm.Framework\PeHubCoreNorm.Framework.csproj" />
	<ProjectReference Include="..\PeHubCoreNorm.Plugins\PeHubCoreNorm.Services.csproj" />
	<ProjectReference Include="..\PeHubCoreNorm\PeHubCoreNorm.csproj" />
</ItemGroup>

</Project>
