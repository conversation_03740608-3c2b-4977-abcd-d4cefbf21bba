﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位套餐对应的组合表
/// </summary>
[SugarTable(TableName = "MapCompanyClusterComb", TableDescription = "单位套餐对应的组合关系")]
public class MapCompanyClusterComb : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 20)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }
}