﻿using Hangfire;
using Microsoft.Extensions.Hosting;

namespace PeHubCoreNorm.Job
{
    public class OrderSyncJob : BackgroundService, ITransient
	{
		private readonly ILogger<OrderSyncJob> _logger;

		public OrderSyncJob(ILogger<OrderSyncJob> logger)
		{
			_logger = logger;
		}


		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{

			try
			{
				if (!App.Get<bool>("Hangfire:enabled"))
				{
					return;
				}
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动成功");

				}, stoppingToken);
 
 
				RecurringJob.AddOrUpdate("seconds", () => Console.WriteLine("作业持续成功了,ordersync!"), "*/50 * * * * *");
				 
			}
			catch (Exception e)
			{
				_logger.LogError(e, "orderSnyc作业报错了");
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动失败");
					 
				}, stoppingToken);
			}

		}
	}
}
