﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Job
{
    public class OrderSyncJob : BackgroundService, ITransient
	{
		private readonly ILogger<OrderSyncJob> _logger;

		public OrderSyncJob(ILogger<OrderSyncJob> logger)
		{
			_logger = logger;
		}


		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{

			try
			{
				if (!App.Get<bool>("Hangfire:enabled"))
				{
					return;
				}
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动成功");

				}, stoppingToken);
				UserManager.setOrgCodeValue("H0002");

				var _syncService = App.ServiceProvider.GetService<ISyncService>();

				RecurringJob.AddOrUpdate("seconds", () => _syncService.SyncCodeItemComb("H0002"), "*/20 * * * * *");
				 
			}
			catch (Exception e)
			{
				_logger.LogError(e, "orderSnyc作业报错了");
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动失败");
					 
				}, stoppingToken);
			}

		}
	}
}
