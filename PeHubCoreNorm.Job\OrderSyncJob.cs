﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Job
{
    public class OrderSyncJob : BackgroundService, ITransient
	{
		private readonly ILogger<OrderSyncJob> _logger;

		public OrderSyncJob(ILogger<OrderSyncJob> logger)
		{
			_logger = logger;
		}

		public async Task MyBackgroundJobMethod()
		{
			UserManager.setAreaCodeValue("A01");
			var _syncService = App.ServiceProvider.GetService<ISyncService>();
			await _syncService.SyncCodeItemComb();
		}


		protected override async Task ExecuteAsync(CancellationToken stoppingToken)
		{

			try
			{
				if (!App.Get<bool>("Hangfire:enabled"))
				{
					return;
				}
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动成功");

				}, stoppingToken);

				RecurringJob.AddOrUpdate("seconds", () => MyBackgroundJobMethod(), "0 0 0,13 * * ?");
				 
			}
			catch (Exception e)
			{
				_logger.LogError(e, "orderSnyc作业报错了");
				await Task.Run(() =>
				{
					Console.WriteLine("OrderSnycJob......作业启动失败");
					 
				}, stoppingToken);
			}

		}
	}
}
