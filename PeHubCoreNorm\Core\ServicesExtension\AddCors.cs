﻿namespace PeHubCoreNorm;

public static partial class ServicesExtension
{
	public static IServiceCollection AddCors(this IServiceCollection services, string[] urldata)
	{
		services.AddCors(options =>
		{
			options.AddPolicy("baseCors",
					builder =>
					{
						builder.WithOrigins(urldata)
						.SetPreflightMaxAge(TimeSpan.FromHours(1))
						.AllowAnyHeader().AllowAnyMethod().AllowCredentials();
					});
		});

		return services;
	}
}