﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 项目分类入参
/// </summary>
public class CodeItemClsInput
{
}

/// <summary>
/// 添加项目分类参数
/// </summary>
public class CodeItemClsAddInput
{
    /// <summary>
    /// 项目分类代码
    /// </summary>
    [Required(ErrorMessage = "项目分类代码不能为空")]
    public string ClsCode { get; set; }

    /// <summary>
    /// 项目分类名称
    /// </summary>
    [Required(ErrorMessage = "项目分类名称不能为空")]
    public string ClsName { get; set; }

    /// <summary>
    /// 分类简称
    /// </summary>
    [Required(ErrorMessage = "分类简称")]
    public string ShortName { get; set; }
}

/// <summary>
/// 修改项目分类参数
/// </summary>
public class CodeItemClsEditInput : CodeItemClsAddInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public string Id { get; set; }
}