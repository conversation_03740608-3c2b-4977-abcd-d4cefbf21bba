﻿
namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 团检号源信息表
    /// </summary>
    [SugarTable("CarouselImage", TableDescription = "轮播图管理")]
    public class CarouselImage : BaseEntity
    {

        /// <summary>
        /// 页面
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "ImagePage", ColumnDescription = "页面", Length = 30)]
        public string ImagePage { get; set; } 

        /// <summary>
        /// 图片base64
        /// </summary>
        [SugarColumn(IsNullable = false,ColumnName = "ImageValue", ColumnDescription = "图片base64", ColumnDataType = StaticConfig.CodeFirst_BigString)]
        public string ImageValue { get; set; }

        /// <summary>
        /// 是否启用 N:禁用 Y:启用
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "N:禁用,Y:启用", Length = 6)]
        public string Status { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnName = "Title", ColumnDescription = "标题", Length = 30)]
        public string Title { get; set; }

    }
}
