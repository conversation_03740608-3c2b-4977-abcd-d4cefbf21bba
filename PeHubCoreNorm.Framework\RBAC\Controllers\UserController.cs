﻿namespace PeHubCoreNorm.RBAC;

[ApiExplorerSettings(GroupName = "RBAC")]
[Route("sys/[controller]")]
public class UserController : BaseControllerRoleAuthorize
{
    private readonly ISysUserService _sysUserService;

    public UserController(ISysUserService sysUserService,
        IRoleService roleService)
    {
        _sysUserService = sysUserService;
    }

    /// <summary>
    ///     用户分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [ActionPermission(ActionType.Query, "用户分页查询", "用户管理")]
    public async Task<dynamic> Page([FromQuery] UserPageInput input)
    {
        return await _sysUserService.Page(input);
    }


    /// <summary>
    ///     用户详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("detail")]
    [ActionPermission(ActionType.Query, "用户详情", "用户管理")]
    public async Task<dynamic> Detail([FromQuery] BaseIdInput input)
    {
        return await _sysUserService.GetSysUserById(input.Id);
    }

    /// <summary>
    ///     获取用户拥有角色
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("ownRoleRoleSelect")]
    [ActionPermission(ActionType.Query, "获取用户拥有角色", "用户管理")]
    public async Task<dynamic> OwnRoleRoleSelect([FromQuery] BaseIdInput input)
    {
        return await _sysUserService.OwnRoleRoleSelect(input);
    }

    /// <summary>
    ///     添加用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("add")]
    [ActionPermission(ActionType.Button, "添加用户", "用户管理")]
    public async Task Add([FromBody] UserAddInput input)
    {
        await _sysUserService.Add(input);
    }

    /// <summary>
    ///     修改用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("edit")]
    [ActionPermission(ActionType.Button, "修改用户", "用户管理")]
    public async Task Edit([FromBody] UserEditInput input)
    {
        await _sysUserService.Edit(input);
    }

    /// <summary>
    ///     删除用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    [ActionPermission(ActionType.Button, "删除用户", "用户管理")]
    public async Task Delete([FromBody] BaseIdsInput input)
    {
        await _sysUserService.Delete(input);
    }

    /// <summary>
    ///     禁用用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("disableUser")]
    [ActionPermission(ActionType.Button, "禁用用户", "用户管理")]
    public async Task DisableUser([FromBody] BaseIdInput input)
    {
        await _sysUserService.DisableUser(input);
    }

    /// <summary>
    ///     启用用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("enableUser")]
    [ActionPermission(ActionType.Button, "启用用户", "用户管理")]
    public async Task EnableUser([FromBody] BaseIdInput input)
    {
        await _sysUserService.EnableUser(input);
    }

    /// <summary>
    ///     重置密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("resetPassword")]
    [ActionPermission(ActionType.Button, "重置密码", "用户管理")]
    public async Task ResetPassword([FromBody] BaseIdInput input)
    {
        await _sysUserService.ResetPassword(input);
    }

    /// <summary>
    ///     给用户授权角色
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("grantRole")]
    [ActionPermission(ActionType.Button, "给用户授权角色", "用户管理")]
    public async Task GrantRole([FromBody] UserGrantRoleInput input)
    {
        await _sysUserService.GrantRole(input);
    }
}