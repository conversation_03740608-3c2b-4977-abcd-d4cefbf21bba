﻿namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
///  套餐组合业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class OrderController : BaseControllerAuthorize
{
    private readonly IOrderService _orderService;
    public OrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderList")]
    [ActionPermission(ActionType.Query, "GetOrderList", "APP端用户业务")]
    public async Task<List<OrderList>> GetOrderList([FromBody] OrderQuery query)
    {
        return await _orderService.GetOrderList(query);
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderDetail")]
    [ActionPermission(ActionType.Query, "GetOrderDetail", "APP端用户业务")]
    public async Task<List<OrderDetail>> GetOrderDetail([FromBody] OrderDetailQuery query)
    {
        return await _orderService.GetOrderDetail(query);
    }
}
