﻿namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 订单业务
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class OrderController : BaseControllerAuthorize
{
    private readonly IOrderService _orderService;
    public OrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost("AddPersonOrder")]
    [ActionPermission(ActionType.Button, "AddPersonOrder", "APP端用户业务")]
    public async Task<bool> AddPersonOrder([FromBody] PersonOrderInput order)
    {
        return await _orderService.AddPersonOrder(order);
    }

    /// <summary>
    /// 取消个检未支付订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost("CancelNoPayPersonOrder")]
    [ActionPermission(ActionType.Button, "CancelNoPayPersonOrder", "APP端用户业务")]
    public async Task<bool> CancelNoPayPersonOrder([FromBody] CancelOrder order)
    {
        return await _orderService.CancelNoPayPersonOrder(order);
    }

    /// <summary>
    /// 取消个检支付订单
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost("CancelPayPersonOrder")]
    [ActionPermission(ActionType.Button, "CancelPayPersonOrder", "APP端用户业务")]
    public async Task<bool> CancelPayPersonOrder([FromBody] CancelOrder order)
    {
        return await _orderService.CancelPayPersonOrder(order);
    }

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderList")]
    [ActionPermission(ActionType.Query, "GetOrderList", "APP端用户业务")]
    public async Task<List<OrderList>> GetOrderList([FromBody] OrderQuery query)
    {
        return await _orderService.GetOrderList(query);
    }

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost("GetOrderDetail")]
    [ActionPermission(ActionType.Query, "GetOrderDetail", "APP端用户业务")]
    public async Task<List<OrderDetail>> GetOrderDetail([FromBody] OrderDetailQuery query)
    {
        return await _orderService.GetOrderDetail(query);
    }

    /// <summary>
    /// 添加团检订单
    /// </summary>
    /// <param name="input">团检订单输入</param>
    /// <returns></returns>
    [HttpPost("AddTeamOrder")]
    [ActionPermission(ActionType.Button, "AddTeamOrder", "APP端用户业务")]
    public async Task<bool> AddTeamOrder([FromBody] TeamOrderInput input)
    {
        input.MembersId = UserManager.UserId;
        return await _orderService.AddTeamOrder(input);
    }

    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input">取消团检订单输入</param>
    /// <returns></returns>
    [HttpPost("CancelTeamOrder")]
    [ActionPermission(ActionType.Button, "CancelTeamOrder", "APP端用户业务")]
    public async Task<bool> CancelTeamOrder([FromBody] CancelTeamOrderInput input)
    {
        input.MemberId = UserManager.UserId;
        return await _orderService.CancelTeamOrder(input);
    }
}
