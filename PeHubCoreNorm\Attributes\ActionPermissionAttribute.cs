﻿namespace PeHubCoreNorm;

public class ActionPermissionAttribute : Attribute
{
    /// <summary>
    ///     接口权限标注
    /// </summary>
    /// <param name="type">类型:0查询接口,1按钮接口</param>
    /// <param name="name">接口名称,用于UI配置菜单和httpLog记录</param>
    public ActionPermissionAttribute(ActionType type, string name, string typeName = "未分类")
    {
        AuthType = type;
        Name = name;
        TypeName = typeName;
    }

    public ActionType AuthType { get; }
    public string Name { get; }

    /// <summary>
    ///     功能分类
    /// </summary>
    public string TypeName { get; }
}

public enum ActionType
{
    /// <summary>
    ///     查询接口
    /// </summary>
    Query = 0,

    /// <summary>
    ///     操作接口
    /// </summary>
    Button = 1
}