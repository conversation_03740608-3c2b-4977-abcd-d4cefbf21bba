﻿using System.Text.RegularExpressions;
using Masuit.Tools;
using PeHubCoreNorm.System;
using PeHubCoreNorm.Utils;

namespace PeHubCoreNorm.RBAC;

public class SysUserService : DbRepository<SysUser>, ISysUserService
{
    private readonly ICacheService _cacheService;
    private readonly IConfigService _configService;
    private readonly IRelationService _relationService;

    public SysUserService(ICacheService cacheService,
        IRelationService relationService,
        IConfigService configService)
    {
        _cacheService = cacheService;
        _relationService = relationService;
        _configService = configService;
    }

    public async Task Add(UserAddInput input)
    {
        var check = await CheckInput(input); //检查参数
        if (check != "OK")
        {
            Unify.SetError(check);
            return;
        }

        var sysUser = input.Adapt<SysUser>(); //实体转换

		var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_PWD_POLICY); //获取密码策略
		var basePwd = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_DEFAULT_PASSWORD)
		   .ConfigValue; // 
        
		sysUser.Password = CryptogramUtil.Sm2Encrypt(basePwd) ; //设置密码
        sysUser.SuperAdmin = "N";
        await InsertAsync(sysUser); //添加数据
    }

    public async Task Delete(BaseIdsInput input)
    {
        var ids = input.Ids.ToList();
        if (ids.Count > 0)
        {
            foreach (var item in ids) DeleteUserCache(item);

            var result = await itenant.UseTranAsync(async () =>
            {
                //删除用户
                await DeleteByIdsAsync(ids.Cast<object>().ToArray());
                var relationRep = ChangeRepository<DbRepository<SysRelation>>(); //切换仓储
                //删除关系表
                var delRelations = new List<string>
                    { CateGoryConst.Relation_SYS_USER_HAS_MENU, CateGoryConst.Relation_SYS_USER_HAS_ROLE };
                await relationRep.DeleteAsync(it => ids.Contains(it.ObjectId) && delRelations.Contains(it.Category));
            });
            if (result.IsSuccess) //如果成功了
            {
            }
            else
            {
                throw result.ErrorException;
            }
        }
    }

    public async Task Edit(UserEditInput input)
    {
        await CheckInput(input); //检查参数
        var exist = await GetByIdAsync(input.Id); //获取用户信息
        if (exist != null)
        {
            var isSuperAdmin = exist.Account == "SuperAdmin"; //判断是否有超管
            if (isSuperAdmin && !UserManager.SuperAdmin)
            {
                Unify.SetError("不可修改系统内置超管用户账号");
                return;
            }

            var name = exist.Name; //姓名
            var sysUser = input.Adapt<SysUser>(); //实体转换
            if (await Context.Updateable(sysUser).IgnoreColumns(it =>
                    new
                    {
                        //忽略更新字段
                        it.Password,
                        it.LastLoginIp,
                        it.LastLoginTime
                    }).ExecuteCommandAsync() > 0) //修改数据
                //删除用户token缓存
                DeleteUserCache(input.Id);
        }
    }

    public async Task<SysUser> GetSysUserByAccount(string account)
    {
        var user = await GetFirstAsync(x => x.Account == account);
        //针对不忽略大小写的数据库再次进行强制验证大小写是否一致
        if (user?.Account != account) return default;
        return user;
    }

    public async Task<SysUser> GetSysUserById(string userId)
    {
        var user = await Context.Queryable<SysUser>()
            .Where(u => u.Id == userId)
            .Select(u => new SysUser
            {
                Id = u.Id.SelectAll()
            }).FirstAsync();
        return user;
    }

    public async Task SetLogined(string userId)
    {
        await Context.Updateable<SysUser>()
            .SetColumns(x => x.LastLoginTime == DateTime.Now)
            .Where(x => x.Id == userId)
            .ExecuteCommandAsync();
    }

    public async Task GrantRole(UserGrantRoleInput input)
    {
        var sysUser = await GetByIdAsync(input.Id); //获取用户信息
        if (sysUser != null)
        {
            if (sysUser.SuperAdmin == "Y")
            {
                Unify.SetError("不能给超管分配角色");
                return;
            }

            //删除用户的接口缓存信息
            DeleteUserCache(input.Id);

            await _relationService.SaveRelationBatch(CateGoryConst.Relation_SYS_USER_HAS_ROLE, input.Id,
                input.RoleIdList.Select(it => it.ToString()).ToList(), null, true);

            var roleOrg = string.Join(",",Context.Queryable<SysRole>().Where(it => input.RoleIdList.Contains(it.Id))
                .Select(it => it.ExtJson).ToList()).Split(",").Distinct().ToList();
 
			sysUser.ExtJson = string.Join(",", roleOrg);//更新授权院区

			await UpdateSetColumnsTrueAsync(it => new SysUser
			{
				ExtJson = sysUser.ExtJson
			}, it => it.Id == sysUser.Id); //更新用户信息

		}
    }

    public async Task<SqlSugarPagedList<SysUser>> Page(UserPageInput input)
    {
		var query = GetQuery(input); //获取查询条件
        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    public async Task<List<string>> OwnRole(BaseIdInput input)
    {
        var relations =
            await _relationService.GetRelationListByObjectIdAndCategory(input.Id,
                CateGoryConst.Relation_SYS_USER_HAS_ROLE);
        return relations.Select(it => it.TargetId).ToList();
    }

    public async Task<List<RoleSelectorOutPut>> OwnRoleRoleSelect(BaseIdInput input)
    {
        var relations =
            await _relationService.GetRelationListByObjectIdAndCategory(input.Id,
                CateGoryConst.Relation_SYS_USER_HAS_ROLE);
        var roleIds = relations.Select(it => it.TargetId).ToList();
        var roleList = await Context.Queryable<SysRole>().Where(it => roleIds.Contains(it.Id))
            .Select<RoleSelectorOutPut>().ToListAsync();
        return roleList;
    }


    public async Task<List<string>> OwnRoleCode()
    {
        var relations =
            await _relationService.GetRelationListByObjectIdAndCategory(UserManager.UserId,
                CateGoryConst.Relation_SYS_USER_HAS_ROLE);

        var roleslist = await Context.Queryable<SysRole>()
            .Where(x => relations.Select(x => x.TargetId).Contains(x.Id))
            .Select(it => it.Code)
            .ToListAsync();

        return roleslist;
    }


    public async Task<List<string>> OwnButtonCodeList()
    {
        if (UserManager.SuperAdmin)
        {
            var codes = await Context.Queryable<SysMenu>().Where(x => x.Category == CateGoryConst.Menu_BUTTON)
                .Select(x => x.Code)
                .ToListAsync();

            return codes;
        }
        else
        {
            var roleIds = await OwnRole(new BaseIdInput { Id = UserManager.UserId });

            //角色包含多少菜单
            var relations = await Context.Queryable<SysRelation>()
                .InnerJoin<SysMenu>((x, y) => x.TargetId == y.Id)
                .Where((x, y) => roleIds.Contains(x.ObjectId) && x.Category == CateGoryConst.Relation_SYS_ROLE_HAS_MENU)
                .Select((x, y) => x)
                .ToListAsync();

            //按钮Id
            var buttonIds = relations.SelectMany(x => x.ExtJson.ToObject<List<string>>()).Distinct();

            var codes = await Context.Queryable<SysMenu>()
                .Where(x => x.Category == CateGoryConst.Menu_BUTTON && buttonIds.Contains(x.Id))
                .Select(x => x.Code)
                .ToListAsync();
            return codes;
        }
    }

    public async Task<List<string>> OwnPermissionCodeList()
    {
        //用户的包含的APIs
        var result = new List<string>();
        result = _cacheService.HashGetOne<List<string>>(CacheConst.Cache_UserRelation,
            CacheConst.Field_UserHasApi(UserManager.UserId));
        if (result != null && result.Count > 0) return result;
        result ??= [];

        var roleIds = await OwnRole(new BaseIdInput { Id = UserManager.UserId });

        //角色包含多少菜单
        var relations = await Context.Queryable<SysRelation>()
            .InnerJoin<SysMenu>((x, y) => x.TargetId == y.Id)
            .Where((x, y) => roleIds.Contains(x.ObjectId) && x.Category == CateGoryConst.Relation_SYS_ROLE_HAS_MENU)
            .Select((x, y) => x)
            .ToListAsync();

        //菜单Id + 按钮Id
        var menuIds = relations.Select(x => x.TargetId).ToList();
        var buttonIds = relations.SelectMany(x => x.ExtJson.ToObject<List<string>>());
        menuIds.AddRange(buttonIds);
        menuIds = menuIds.Distinct().ToList();

        //菜单对应的菜单和按钮
        var menus = await Context.Queryable<SysMenu>()
            .Where(x => menuIds.Contains(x.Id))
            .ToListAsync();

        foreach (var item in menus)
            if (!string.IsNullOrEmpty(item.Apis))
                result.AddRange(item.Apis.Split(','));

        if (result.Count > 0)
            _cacheService.HashAdd(CacheConst.Cache_UserRelation, CacheConst.Field_UserHasApi(UserManager.UserId),
                result);

        return result;
    }

    public async Task DisableUser(BaseIdInput input)
    {
        var sysUser = await GetByIdAsync(input.Id); //获取用户信息
        if (sysUser != null)
        {
            var innerAdmin = sysUser.Account.ToLower() == "SuperAdmin".ToLower(); //判断是否有超管
            if (innerAdmin)
            {
                Unify.SetError("不可禁用系统内置超管用户账号");
                return;
            }

            //删除用户的接口缓存信息
            DeleteUserCache(input.Id);

            await UpdateAsync(it => new SysUser
            {
                status = "N"
            }, it => it.Id == input.Id);
        }
    }

    public async Task EnableUser(BaseIdInput input)
    {
        await UpdateAsync(it => new SysUser
        {
            status = "Y"
        }, it => it.Id == input.Id);
    }

    public async Task ResetPassword(BaseIdInput input)
    {
        DeleteUserCache(input.Id);
		var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_PWD_POLICY); //获取密码策略
		var basePwd= loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_DEFAULT_PASSWORD)
		   .ConfigValue; // 
		var password = CryptogramUtil.Sm2Encrypt(basePwd);
        var lastLoginTime = DateTime.Now;
        await UpdateAsync(it => new SysUser
        {
            LastLoginTime = lastLoginTime,
            Password = password
        }, it => it.Id == input.Id);
    }

    public async Task<dynamic> UpdatePassword(UpdatePasswordInput input)
    {
        //获取用户信息
        var userInfo = await GetSysUserById(UserManager.UserId);
        var password = CryptogramUtil.Sm2Decrypt(input.Password);//SM2解密

        if (CryptogramUtil.Sm2Decrypt(userInfo.Password) != password) throw new CustomException("原密码错误");
        var newPassword = CryptogramUtil.Sm2Decrypt(input.NewPassword);//sm2解密
		if (string.IsNullOrEmpty(newPassword)) throw new CustomException("新密码加密格式错误");

		var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_PWD_POLICY); //获取密码策略
        var containNumber = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_CONTAIN_NUM)
            .ConfigValue.ToBoolean(); //是否包含数字
        var containLower = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_CONTAIN_LOWER)
            .ConfigValue.ToBoolean(); //是否包含小写
        var containUpper = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_CONTAIN_UPPER)
            .ConfigValue.ToBoolean(); //是否包含大写
        var containChar = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_CONTAIN_CHARACTER)
            .ConfigValue.ToBoolean(); //是否包含特殊字符
        var minLength = loginPolicy.FirstOrDefault(it => it.ConfigKey == PeHubCoreNormConst.PWD_MIN_LENGTH).ConfigValue
            .ToInt(); //最小长度
        if (minLength > newPassword.Length)
            throw new CustomException($"密码长度不能小于{minLength}");
        if (containNumber && !Regex.IsMatch(newPassword, "[0-9]"))
            throw new CustomException("密码必须包含数字");
        if (containLower && !Regex.IsMatch(newPassword, "[a-z]"))
            throw new CustomException("密码必须包含小写字母");
        if (containUpper && !Regex.IsMatch(newPassword, "[A-Z]"))
            throw new CustomException("密码必须包含大写字母");
        if (containChar && !Regex.IsMatch(newPassword, "[~!@#$%^&*()_+`\\-={}|\\[\\]:\";'<>?,./]"))
            throw new CustomException("密码必须包含特殊字符");
        // var similarity = PwdUtil.Similarity(password, newPassword);
        // if (similarity > 80)
        //     throw Oops.Bah($"新密码请勿与旧密码过于相似");
        //newPassword = CryptogramUtil.Sm4Encrypt(newPassword);//SM4加密
        userInfo.Password = input.NewPassword;
        await UpdateSetColumnsTrueAsync(it => new SysUser { Password = input.NewPassword },
            it => it.Id == userInfo.Id); //更新密码
        //_sysUserService.DeleteUserFromRedis(UserManager.UserId);//redis删除用户数据

        return default;
    }

    public async Task<string> UpdateAvatar(BaseFileInput input)
    {
        var userInfo = await GetByIdAsync(UserManager.UserId);
        var file = input.File;
        using var fileStream = file.OpenReadStream(); //获取文件流
        var bytes = new byte[fileStream.Length];
        fileStream.Read(bytes, 0, bytes.Length);
        fileStream.Close();
        var base64String = Convert.ToBase64String(bytes); //转base64
        var avatar = "data:image/png;base64," + base64String; //转图片
        userInfo.Avatar = avatar;
        await Context.Updateable(userInfo).UpdateColumns(it => new { it.Avatar }).ExecuteCommandAsync(); //修改密码
        return avatar;
    }

    public async Task<dynamic> UpdateUserInfo(UpdateInfoInput input)
    {
        //如果手机号不是空
        if (!string.IsNullOrEmpty(input.Phone))
            if (!input.Phone.MatchPhoneNumber()) //判断是否是手机号格式
                return Unify.SetError("手机号码格式错误");
        if (!string.IsNullOrEmpty(input.Email))
        {
            var match = input.Email.MatchEmail();
            if (!match.isMatch)
                return Unify.SetError("邮箱格式错误");
        }


        //更新指定字段
        var result = await UpdateAsync(it => new SysUser
        {
            Email = input.Email,
            Name = input.Name,
            Phone = input.Phone
        }, it => it.Id == UserManager.UserId);

        return default;
    }

    public async Task SetDefaultModule(SetDefaultModuleInput input)
    {
        //获取用户信息
        var userInfo = await GetSysUserById(UserManager.UserId);
        //如果是默认模块
        if (input.IfDefault)
            userInfo.DefaultModule = input.Id;
        else
            userInfo.DefaultModule = null;
        await Context.Updateable(userInfo).UpdateColumns(it => new { it.DefaultModule }).ExecuteCommandAsync(); //修改默认模块
    }

    public void DeleteUserCache(string userId)
    {
		//用户角色信息 该功能结合 actionfilter 的 缓存使用
		_cacheService.HashDel<List<string>>(CacheConst.Cache_UserRelation,
            CacheConst.Field_UserHasApi(UserManager.UserId));
        _cacheService.HashDel<List<SysMenu>>(CacheConst.Cache_UserRelationMenu,
            CacheConst.Field_UserHasMenu(UserManager.UserId));


		//用户Token信息 该功能结合 actionfilter 的 缓存使用
		foreach (var item in LoginDevices.AllowDevices)
        {
            var key = CacheConst.Cache_UserToken + userId + ":" + item;
            _cacheService.Remove(key);
        }
    }

    #region 私有方法

    private async Task<string> CheckInput(SysUser sysUser)
    {
        //判断账号重复,直接从redis拿
        var user = await GetFirstAsync(x => x.Account == sysUser.Account);
        if (user != null && user.Id != sysUser.Id)
            return $"存在重复的账号:{sysUser.Account}";

        //如果手机号不是空
        //if (!string.IsNullOrEmpty(sysUser.Phone))
        //{
        //    if (!sysUser.Phone.MatchPhoneNumber())//验证手机格式
        //        return ($"手机号码：{sysUser.Phone} 格式错误");
        //}
        return "OK";
    }

    private ISugarQueryable<SysUser> GetQuery(UserPageInput input)
    {
        var orlist = new List<string>();
        if (!string.IsNullOrEmpty(input.orgChoose))
        {
			orlist=input.orgChoose.Split(",").Distinct().ToList();
        }

		var query = Context.Queryable<SysUser>()
            .WhereIF(input.Expression != null, input.Expression?.ToExpression()) //动态查询
            .WhereIF(!string.IsNullOrEmpty(input.status), u => u.status == input.status) //根据状态查询
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey),
                u => u.Name.Contains(input.SearchKey) || u.Account.Contains(input.SearchKey)) //根据关键字查询
			.WhereIF(orlist.Count() > 0, it => string.IsNullOrWhiteSpace(it.ExtJson) || orlist.All(v => it.ExtJson.Contains(v)))//是否在一个院区
			.WhereIF(!UserManager.SuperAdmin, u => u.SuperAdmin == PeHubCoreNormConst.STATUS_DISABLED) //根据状态查询
			.OrderByIF(!string.IsNullOrEmpty(input.SortField), $" {input.SortField} {input.SortOrder}")
            .OrderByDescending(u => u.Id) //排序
            .Select(u => new SysUser
            {
                Id = u.Id.SelectAll()
            })
            .Mapper(u =>
            {
                u.Password = null; //密码清空
            });
        return query;
    }

    #endregion
}