﻿
namespace PeHubCoreNorm.Services.SyncBusinessService;

#region 体检接口地址
/// <summary>
/// 体检接口地址
/// </summary>
public class PeInterfaceName
{ /// <summary>
  /// 获取套餐
  /// </summary>
    public static string PersonalPackage { get { return "/api/Peis/KrWx/Order/GetPersonalPackage"; } }

    /// <summary>
    /// 根据套餐编码获取套餐包含的组合
    /// </summary>
    public static string CombsByClusCode { get { return "/api/Peis/KrWx/Order/GetCombsByClusCode"; } }

    /// <summary>
    /// 获取所有项目分类下的组合
    /// </summary>
    public static string ItemClsComb { get { return "/api/Peis/KrWx/Order/GetItemClsComb"; } }

    /// <summary>
    /// 获取报告列表
    /// </summary>
    public static string ReportList { get { return "/api/Peis/KrWx/Order/GetReportList"; } }

    /// <summary>
    /// 获取报告详情
    /// </summary>
    public static string ReportDetail { get { return "/api/Peis/KrWx/Order/GetReportDetail"; } }

    /// <summary>
    /// 获取体检系统单位数据
    /// </summary>
    public static string GetCompanyList { get { return "/api/Peis/KrWx/Order/GetCompanyList"; } }

    /// <summary>
    /// 同步个人订单数据
    /// </summary>
    public static string SyncPersonOrder { get { return "/api/Peis/KrWx/Order/SyncPersonOrder"; } }

    /// <summary>
    /// 删除个人订单信息
    /// </summary>
    public static string DeletePersonRecord { get { return "/api/Peis/KrWx/Order/DeletePersonRecord"; } }

    /// <summary>
    /// 插入缴费明细
    /// </summary>
    public static string AddPayInfo { get { return "/api/Peis/KrWx/Order/AddPayInfo"; } }

    /// <summary>
    /// 团体登录
    /// </summary>
    public static string TeamLogin { get { return "/api/Peis/KrWx/Order/TeamLogin"; } }

    /// <summary>
    /// 删除团体订单信息
    /// </summary>
    public static string DeleteTeamRecord { get { return "/api/Peis/KrWx/Order/DeleteTeamRecord"; } }

    /// <summary>
    /// 更新登记信息
    /// </summary>
    public static string ModifyRegInfo { get { return "/api/Peis/KrWx/Order/ModifyRegInfo"; } }

    /// <summary>
    /// 获取支付链接
    /// </summary>
    public static string GetWeChatPayUrl { get { return "/api/Peis/KrWx/Order/GetWeChatPayUrl"; } }

    /// <summary>
    /// 获取支付状态
    /// </summary>
    public static string GetPayStatus { get { return "/api/Peis/KrWx/Order/GetPayStatus"; } }

    /// <summary>
    /// 获取报告Pdf
    /// </summary>
    public static string GetReportPdf { get { return "/api/Peis/KrWx/Order/GetReportPdf"; } }

    /// <summary>
    /// 获取所有单位
    /// </summary>
    public static string GetAllCompany { get { return "/api/Peis/KrWx/Order/GetAllCompany"; } }

    /// <summary>
    /// 获取所有单位体检次数
    /// </summary>
    public static string GetAllCompanyTimes { get { return "/api/Peis/KrWx/Order/GetAllCompanyTimes"; } }

    /// <summary>
    /// 获取所有单位套餐
    /// </summary>
    public static string GetAllCompanyCluster { get { return "/api/Peis/KrWx/Order/GetAllCompanyCluster"; } }

    /// <summary>
    /// 获取所有单位套餐的组合对应
    /// </summary>
    public static string GetAllMapCompanyClusterComb { get { return "/api/Peis/KrWx/Order/GetAllMapCompanyClusterComb"; } }

    /// <summary>
    /// 获取所有组合
    /// </summary>
    public static string GetAllComb { get { return "/api/Peis/KrWx/Order/GetAllComb"; } }

    /// <summary>
    /// 获取个检套餐及组合对应
    /// </summary>
    public static string GetClusterAndCombs { get { return "/api/Peis/KrWx/Order/GetClusterAndCombs"; } }

    /// <summary>
    /// 获取所有项目分类
    /// </summary>
    public static string GetAllItemCls { get { return "/api/Peis/KrWx/Order/GetAllItemCls"; } }
}
#endregion

#region 体检映射类
/// <summary>
/// 体检组合映射类
/// </summary>
public class PeCombsDto
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }
    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }
    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public int Sex { get; set; }
    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
    /// <summary>
    /// 分类代码
    /// </summary>
    public string ClsCode { get; set; }
    /// <summary>
    /// 提示信息
    /// </summary>
    public string Hint { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string Note { get; set; }
}

/// <summary>
/// 体检套餐映射类
/// </summary>
public class PeClusterCombsDto
{
    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }
    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }
    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public int Sex { get; set; }
    /// <summary>
    /// 体检分类
    /// </summary>
    public int PeCls { get; set; }
    /// <summary>
    /// 年龄下限
    /// </summary>
    public int LowerAgeLimit { get; set; }
    /// <summary>
    /// 年龄上限
    /// </summary>
    public int UpperAgeLimit { get; set; }
    /// <summary>
    /// 套餐简介
    /// </summary>
    public string Description { get; set; }
    /// <summary>
    /// 注意事项
    /// </summary>
    public string Notice { get; set; }
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }
    /// <summary>
    /// 组合价格
    /// </summary>
    public decimal CombPrice { get; set; }
}
#endregion

#region 体检接口返参结构
/// <summary>
/// 体检数据结构
/// </summary>
public class ResponseResult
{
    /// <summary>
    /// 返回状态代码0 成功,其他失败
    /// </summary>
    public bool success { get; set; }

    /// <summary>
    /// 调用错误返回信息
    /// </summary>
    public string returnMsg { get; set; }

    /// <summary>
    /// 调用成功后返回信息
    /// </summary>
    public object returnData { get; set; }

    /// <summary>
    /// 多少页
    /// </summary>
    public int pageSum { get; set; }

    /// <summary>
    /// 多少条
    /// </summary>
    public int countSum { get; set; }
}
#endregion