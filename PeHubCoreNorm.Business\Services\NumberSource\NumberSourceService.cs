﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Logging;
using PeHubCoreNorm.Utils;
using System.Buffers.Text;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 号源管理业务接口
/// </summary>
public class NumberSourceService : BizDbRepository<NS_GlobalSource>, INumberSourceService
{
    private readonly ILogger<NumberSourceService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="httpClientHelper"></param>
    public NumberSourceService(
        ILogger<NumberSourceService> logger)
    {
        _logger = logger;
    }

    #region 时段管理    

    /// <summary>
    /// 获取所有时段
    /// </summary>
    /// <returns></returns>
    public async Task<TimeSlotEntryOutput[]> GetTimeSlotEntry()
    {
        var query = Context.Queryable<NS_TimeSlotEntry>().ToArray()
            .Adapt<List<TimeSlotEntryOutput>>();
        return query.ToArray();
    }

    /// <summary>
    /// 添加时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> AddTimeSlotEntry(AddTimeSlotEntryInput input)
    {
        var data = Context.Queryable<NS_TimeSlotEntry>().First(x => x.Id == input.Id);
        if (data != null)
        {
            return Unify.SetError($"编码:{input.Id}的时段已存在，请勿重复操作!");
        }
        var timeSlotEntry = input.Adapt<NS_TimeSlotEntry>();
        return await Context.Insertable(timeSlotEntry).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 更新时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> EditTimeSlotEntry(EditTimeSlotEntryInput input)
    {
        var data = Context.Queryable<NS_TimeSlotEntry>().First(x => x.Id == input.Id);
        if (data == null)
        {
            return Unify.SetError($"编码:{input.Id}的时段不存在，请刷新页面!");
        }
        var timeSlotEntry = input.Adapt<NS_TimeSlotEntry>();
        return await Context.Updateable(timeSlotEntry).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 删除时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> DeleteTimeSlotEntry(DeleteTimeSlotEntryInput input)
    {
        if (input.Ids.Length > 0)
        {
            return await Context.Deleteable<NS_TimeSlotEntry>().In(input.Ids).ExecuteCommandAsync() > 0;
        }
        return false;
    }

    /// <summary>
    /// 获取指定类型的号源时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TimeSlotOutput[]> GetTimeSlot()
    {
        var query = Context.Queryable<NS_TimeSlot>()
            .InnerJoin<NS_TimeSlotEntry>((a, b) => a.TimeSlotEntryID == b.Id)
            .InnerJoin<NS_SourceTypes>((a, b) => a.SourceTypeID == b.Id).ToArray()
            .Adapt<List<TimeSlotOutput>>();
        return query.ToArray();
    }

    #endregion

    #region 号源类型关联时段

    /// <summary>
    /// 获取全部号源类型
    /// </summary>
    /// <returns></returns>
    public async Task<SourceTypeOutput[]> GetSourceType()
    {
        var query = Context.Queryable<NS_SourceTypes>().ToArray()
            .Adapt<List<SourceTypeOutput>>();
        return query.ToArray();
    }

    /// <summary>
    /// 通过号源类型获取已关联时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<TimeSlotOutput[]> GetTimeSlotByType(TimeSlotInput input)
    {
        var query = Context.Queryable<NS_TimeSlot>()
            .InnerJoin<NS_TimeSlotEntry>((ts, tse) => ts.TimeSlotEntryID == tse.Id)
            .InnerJoin<NS_SourceTypes>((ts, tse, st) => ts.SourceTypeID == st.Id)
            .Where(ts => ts.SourceTypeID == input.SourceTypeID).Select<TimeSlotOutput>().ToArray();

        return query;
    }

    /// <summary>
    /// 添加号源类型对应时段编码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> AddTimeSlotByType(AddTimeSlotByTypeInput input)
    {
        NS_TimeSlot slot = Context.Queryable<NS_TimeSlot>().First(x => x.Id == input.SourceTypeID && x.TimeSlotEntryID == input.TimeSlotEntryID);
        if (slot == null)
        {
            return Unify.SetError($"号源类型：{input.SourceTypeID}对应,编码为:{input.TimeSlotEntryID}的关联关系已存在，请勿重复操作!");
        }
        var timeSlot = input.Adapt<NS_TimeSlot>();
        return await Context.Insertable(timeSlot).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 更新号源类型对应时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> EditTimeSlotByType(EditTimeSlotByTypeInput input)
    {
        List<NS_TimeSlot> slots = Context.Queryable<NS_TimeSlot>().Where(x => x.SourceTypeID == input.SourceTypeID).ToList();
        List<NS_TimeSlot> notInSlots = slots.Where(x => !input.TimeSlotEntryID.Contains(x.TimeSlotEntryID)).ToList();

        string[] timeSlotEntryList = slots.Select(x => x.TimeSlotEntryID).ToArray();
        // 找出array1中有但array2中没有的元素
        string[] onlyInArray1 = input.TimeSlotEntryID.Except(timeSlotEntryList).ToArray();
        if (notInSlots.Count > 0)
        {
            await Context.Deleteable<NS_TimeSlot>().In(notInSlots.Select(x => x.Id)).ExecuteCommandAsync();
        }
        foreach (var item in onlyInArray1)
        {
            NS_TimeSlot ts = new NS_TimeSlot();
            ts.SourceTypeID = input.SourceTypeID;
            ts.TimeSlotEntryID = item;
            await Context.Insertable(ts).ExecuteCommandAsync();
        }
        return true;
    }

    /// <summary>
    /// 删除号源类型对应时段
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> DeleteTimeSlotByType(DeleteTimeSlotByTypeInput input)
    {
        return await Context.Deleteable<NS_TimeSlotEntry>().In(input.Id).ExecuteCommandAsync() > 0;
    }

    #endregion

    #region 号源管理
    /// <summary>
    /// 获取个检类型
    /// </summary>
    /// <returns></returns>
    public async Task<PersonNumberSourceOutput[]> GetPersonType()
    {
        return Context.Queryable<NS_SourceTypes>().Where(x => !string.IsNullOrEmpty(x.ClsCode)).ToArrayAsync().Adapt<PersonNumberSourceOutput[]>();
    }

    /// <summary>
    /// 获取某月号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetNumberSource(NumberSourceInput input)
    {
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();
        string tableName = sourceTypes.First(x => x.Id == input.SourceTypeID).SourceTypeTable;
        var query = Context.Queryable<NS_GlobalSource>().AS(tableName)
            .Where(x => x.Date.ToString("yyyy-MM") == (input.Year + "-" + input.Month))
            //.WhereIF(!string.IsNullOrWhiteSpace(input.SourceTypeID), x => x.TimeSlotID == input.SourceTypeID)
            .GroupBy(x => new { x.Date, x.IsVacation, x.Week })
            .Select(x => new NumberSourceOutput
            {
                Date = x.Date,
                TotalCapacity = SqlFunc.AggregateSumNoNull(x.TotalCapacity),
                UsedCapacity = SqlFunc.AggregateSumNoNull(x.UsedCapacity),
                AvailableCapacity = SqlFunc.AggregateSumNoNull(x.AvailableCapacity),
                Week = x.Week,
                IsVacation = x.IsVacation
            });
        return query.ToArray();
    }

    /// <summary>
    /// 获取某一天的号源（分时段）
    /// </summary>
    /// <param name="NSInput"></param>
    /// <returns></returns>
    public async Task<NumberSourceOutput[]> GetNumberSourceByDate(NumberSourceByDayInput input)
    {
        List<NumberSourceOutput> query = new List<NumberSourceOutput>();
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();
        string tableName = sourceTypes.First(x => x.Id == input.SourceTypeID).SourceTypeTable;
        query = Context.Queryable<NS_GlobalSource>().AS(tableName)
            .Where(x => x.Date.ToString("yyyy-MM-dd") == (input.Year + "-" + input.Month + "-" + input.Day)).ToArray()
            .Adapt<List<NumberSourceOutput>>();
        //.Select(x => new NumberSourceOutput
        //{
        //    Date = x.Date,
        //    TotalCapacity = x.TotalCapacity,
        //    UsedCapacity = x.UsedCapacity,
        //    AvailableCapacity = x.AvailableCapacity,
        //    Week = x.Week,
        //    IsVacation = x.IsVacation,
        //    TimeSlotID = x.TimeSlotID
        //}).ToList();
        return query.ToArray();
    }

    /// <summary>
    /// 编辑号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> EditNumberSource(List<EditNumberSourceInput> input)
    {
        List<NS_SourceTypes> sourceTypes = Context.Queryable<NS_SourceTypes>().ToList();//获取全部号源类型
        foreach (var item in input)//遍历入参
        {
            //判断号源类型
            NS_SourceTypes sourceType = sourceTypes.First(x => x.Id == item.SourceTypeID);
            if (sourceType == null)
            {
                Unify.SetError($"{item.SourceTypeID}号源类型无效，请检查！");
                return false;
            }
            switch (sourceType.SourceTypeTable)
            {
                case "NS_GlobalSource":
                    bool isSuccessByGlobal = EdidGlobalSource(item, out string msg);
                    if (!isSuccessByGlobal)
                    {
                        Unify.SetError(msg);
                    }
                    return isSuccessByGlobal;
                case "NS_IndividualSource":
                    bool isSuccessByIndividual = EdidIndividualSource(item, out string msg1);
                    if (!isSuccessByIndividual)
                    {
                        Unify.SetError(msg1);
                    }
                    return isSuccessByIndividual;
                    // TODO：团体 补检 第三方实现

            }

        }
        return true;
    }

    /// <summary>
    /// 删除/清空号源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> DeleteNumberSource(DeleteNumberSourceInput input)
    {
        DateTime times = Convert.ToDateTime(input.StartTime);
        int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;
        for (int i = 0; i <= nums; i++)
        {
            var bookdate = times.AddDays(i).ToString("yyyy-MM-dd");
            var bookNoList = Context.Queryable<NS_GlobalSource>().Where(x => x.Date.ToString("yyyy-MM-dd") == bookdate).ToList();
            foreach (var item in bookNoList)
            {
                if (item.UsedCapacity == 0)
                {
                    Context.Deleteable(item);
                }
                else
                {
                    return Unify.SetError($"{bookdate}已有{item.UsedCapacity}人预约，禁止清除");
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 设置号源休假
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<dynamic> SetNumberSourceVacation(List<SetNumberSourceVacationInput> input)
    {
        foreach (var item in input)
        {
            DateTime times = Convert.ToDateTime(item.StartTime);
            int nums = Convert.ToDateTime(item.EndTime).Subtract(Convert.ToDateTime(item.StartTime)).Days;

            for (int i = 0; i <= nums; i++)
            {
                var bookdate = times.AddDays(i).ToString("yyyy-MM-dd");
                var result = Context.Queryable<NS_GlobalSource>().First(n => n.Date.ToString("yyyy-MM-dd") == bookdate && n.TimeSlotID.Contains(item.TimeSlotID));
                if (result == null)
                {
                    int isoWeekNumber = ((int)times.AddDays(i).DayOfWeek + 6) % 7 + 1;
                    NS_GlobalSource booksum = new NS_GlobalSource();
                    booksum.TimeSlotID = item.TimeSlotID;
                    booksum.Date = times.AddDays(i);
                    booksum.Week = isoWeekNumber;
                    booksum.TotalCapacity = 0;
                    booksum.AvailableCapacity = 0;
                    booksum.IsVacation = item.Statu;
                    Context.Insertable(booksum);
                }
                else
                {
                    //已预约不能设置休假
                    if (item.Statu == "T" && result.UsedCapacity > 0)
                    {
                        return await Unify.SetError($"日期:{result.Date.ToString("yyyy-MM-dd")},时段：{result.TimeSlotID}号源已使用不能设置休假!");
                    }
                    result.IsVacation = item.Statu;
                    Context.Updateable(result);
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 编辑全局号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool EdidGlobalSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源
                var result = Context.Queryable<NS_GlobalSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_GlobalSource sum = new NS_GlobalSource();
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    /// <summary>
    /// 检查当前时段是否和号源类型有关联
    /// </summary>
    /// <param name="sourceTypeID"></param>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    private bool CheckAssociations(string sourceTypeID, string timeSlotID)
    {
        NS_TimeSlot timeSlot = Context.Queryable<NS_TimeSlot>().First(x => x.Id == timeSlotID);
        if (timeSlot != null)
        {
            return timeSlot.SourceTypeID == sourceTypeID;
        }
        return false;
    }

    /// <summary>
    /// 检查全局号源中和要修改的号源时段是否存在
    /// </summary>
    /// <param name="timeSlotID"></param>
    /// <returns></returns>
    private bool CheckGlobalIsIncludeSlot(string timeSlotID)
    {
        List<NS_TimeSlot> timeSlots = Context.Queryable<NS_TimeSlot>().ToList();//获取全部时段关联信息
        var globalTimeSlotEntryIDs = timeSlots.Where(x => x.SourceTypeID == "464564896820001").Select(x => x.TimeSlotEntryID).ToArray();
        NS_TimeSlot checkSlot = timeSlots.First(x => x.Id == timeSlotID);//待检查时段的关联信息
        if (checkSlot != null)
        {
            return globalTimeSlotEntryIDs.Contains(checkSlot.TimeSlotEntryID);
        }
        return false;
    }

    /// <summary>
    /// 消费全局号源
    /// </summary>
    /// <param name="isAddNumber"></param>
    /// <param name="timeSlotID"></param>
    /// <param name="date"></param>
    /// <param name="total"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool GlobalConsumption(bool isAddNumber, string timeSlotID, DateTime date, int total, out string msg)
    {
        msg = "成功";
        List<NS_TimeSlot> timeSlots = Context.Queryable<NS_TimeSlot>().ToList();//获取全部时段关联信息
        //var globalTimeSlotEntryIDs = timeSlots.Where(x => x.SourceTypeID == "464564896820001").Select(x => x.TimeSlotEntryID).ToArray();
        NS_TimeSlot checkSlot = timeSlots.First(x => x.Id == timeSlotID);//待检查时段的关联信息
        if (checkSlot != null)
        {
            NS_TimeSlot globalTimeSlot = timeSlots.First(x => x.SourceTypeID == "464564896820001" && x.TimeSlotEntryID == checkSlot.TimeSlotEntryID);
            if (globalTimeSlot != null)
            {
                NS_GlobalSource globalSource = Context.Queryable<NS_GlobalSource>().First(x => x.Date == date && x.TimeSlotID == globalTimeSlot.Id);
                if (isAddNumber)//如果是增加号源的情况
                {
                    if (globalSource.AvailableCapacity < total)
                    {
                        msg = "总号源不足本次抵扣！";
                        return false;
                    }
                    else
                    {
                        globalSource.AvailableCapacity -= total;
                        globalSource.UsedCapacity += total;
                        if (Context.Updateable(globalSource).ExecuteCommand() > 0)
                        {
                            return true;
                        }
                    }
                }
                else//减少号源的情况
                {
                    globalSource.AvailableCapacity += total;
                    globalSource.UsedCapacity -= total;
                    if (Context.Updateable(globalSource).ExecuteCommand() > 0)
                    {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /// <summary>
    /// 编辑个检号源
    /// </summary>
    /// <param name="input"></param>
    /// <param name="msg"></param>
    /// <returns></returns>
    private bool EdidIndividualSource(EditNumberSourceInput input, out string msg)
    {
        msg = "成功";
        try
        {
            //判断当前时段是否和号源类型有关联
            if (!CheckAssociations(input.SourceTypeID, input.TimeSlotID))
            {
                msg = $"{input.SourceTypeID}和{input.TimeSlotID}无关联，请检查后再试！";
                return false;
            }
            //判断全局时段中是否包含当前时段
            if (!CheckGlobalIsIncludeSlot(input.TimeSlotID))
            {
                msg = $"全局号源时段中不存在{input.TimeSlotID}，请检查后再试！";
                return false;
            }
            DateTime starTime = Convert.ToDateTime(input.StartTime);//转换开始时间
            int nums = Convert.ToDateTime(input.EndTime).Subtract(Convert.ToDateTime(input.StartTime)).Days;//计算开始时间和结束时间的日期差
            for (int i = 0; i <= nums; i++)//循坏日期差
            {
                var bookdate = Convert.ToDateTime(starTime.AddDays(i).ToString("yyyy-MM-dd"));//设置从开始时间到结束时间的指定时段的号源                
                //消费或增加全局号源
                bool isAddNumner = true;
                var result = Context.Queryable<NS_IndividualSource>().First(x => x.Date == bookdate && x.TimeSlotID == input.TimeSlotID && x.SourceTypeID == input.SourceTypeID);//从数据库中查询当前日期的当前时段是否已经存在
                if (result == null)//如果不存在，则新增
                {
                    //检查和操作全局号源
                    if (!GlobalConsumption(isAddNumner, input.TimeSlotID, bookdate, input.TotalCapacity, out msg))
                    {
                        return false;
                    }
                    int weekNumber = ((int)Convert.ToDateTime(bookdate).DayOfWeek + 6) % 7 + 1;
                    NS_IndividualSource sum = new NS_IndividualSource();
                    sum.SourceTypeID = input.SourceTypeID;
                    sum.Date = bookdate;
                    sum.TotalCapacity = input.TotalCapacity;
                    sum.AvailableCapacity = input.TotalCapacity;
                    sum.UsedCapacity = 0;
                    sum.TimeSlotID = input.TimeSlotID;
                    sum.IsVacation = input.Statu;
                    sum.Week = weekNumber;
                    if (Context.Insertable(sum).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
                else//存在则修改
                {
                    isAddNumner = false;
                    if (result.IsVacation == "T" && nums > 0)
                    {
                        continue;
                    }
                    else
                    {
                        if (!GlobalConsumption(isAddNumner, input.TimeSlotID, bookdate, input.TotalCapacity - result.TotalCapacity, out msg))
                        {
                            return false;
                        }
                        result.IsVacation = input.Statu;
                        result.AvailableCapacity += input.TotalCapacity - result.TotalCapacity;
                        result.TotalCapacity = input.TotalCapacity;
                    }
                    if (Context.Updateable(result).ExecuteCommand() > 0)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            msg = ex.Message;
            return false;
        }
    }

    #endregion

}
