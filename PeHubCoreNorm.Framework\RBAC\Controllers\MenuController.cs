﻿namespace PeHubCoreNorm.RBAC;

[ApiExplorerSettings(GroupName = "RBAC")]
[Route("sys/[controller]")]
public class MenuController : BaseControllerRoleAuthorize
{
    private readonly ISysMenuService _sysMenuService;

    public MenuController(ISysMenuService sysMenuService)
    {
        _sysMenuService = sysMenuService;
    }

    /// <summary>
    ///     模块选择
    /// </summary>
    /// <returns></returns>
    [HttpGet("moduleSelector")]
    [ActionPermission(ActionType.Query, "模块选择", "菜单管理")]
    public async Task<dynamic> ModuleSelector()
    {
        return await _sysMenuService.GetListByCategory(CateGoryConst.Menu_MODULE);
    }

    /// <summary>
    ///     获取菜单树
    /// </summary>
    /// <returns></returns>
    [HttpGet("tree")]
    [ActionPermission(ActionType.Query, "获取菜单树", "菜单管理")]
    public async Task<dynamic> Tree([FromQuery] MenuTreeInput input)
    {
        return await _sysMenuService.Tree(input);
    }

    /// <summary>
    ///     获取菜单树选择器
    /// </summary>
    /// <returns></returns>
    [HttpGet("menuTreeSelector")]
    [ActionPermission(ActionType.Query, "获取菜单树选择器", "菜单管理")]
    public async Task<dynamic> MenuTreeSelector([FromQuery] MenuTreeInput input)
    {
        return await _sysMenuService.Tree(input);
    }

    /// <summary>
    ///     添加菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("add")]
    [ActionPermission(ActionType.Button, "添加菜单", "菜单管理")]
    public async Task Add([FromBody] MenuAddInput input)
    {
        await _sysMenuService.Add(input);
    }

    /// <summary>
    ///     编辑菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("edit")]
    [ActionPermission(ActionType.Button, "编辑菜单", "菜单管理")]
    public async Task Edit([FromBody] MenuEditInput input)
    {
        await _sysMenuService.Edit(input);
    }

    /// <summary>
    ///     删除菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    [ActionPermission(ActionType.Button, "删除菜单", "菜单管理")]
    public async Task Delete([FromBody] BaseIdsInput input)
    {
        await _sysMenuService.Delete(input);
    }

    /// <summary>
    ///     更改模块
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("changeModule")]
    [ActionPermission(ActionType.Button, "更改模块", "菜单管理")]
    public async Task ChangeModule([FromBody] MenuChangeModuleInput input)
    {
        await _sysMenuService.ChangeModule(input);
    }

    /// <summary>
    ///     获取菜单的所有接口信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("menuApiList")]
    [ActionPermission(ActionType.Query, "获取菜单的所有接口信息", "按键管理")]
    public dynamic menuApiList()
    {
        var retval = _sysMenuService.GetAuthCodeAttributes();
        if (retval.Count == 0)
            Unify.SetError("请添ActionPermission配置");
        return retval;
    }


    /// <summary>
    ///     更改apis
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("changeApiList")]
    [ActionPermission(ActionType.Button, "更改菜单对应的apis", "按键管理")]
    public async Task ChangeApiList([FromBody] MenuAuthInput input)
    {
        await _sysMenuService.ChangeApiList(input);
    }
}