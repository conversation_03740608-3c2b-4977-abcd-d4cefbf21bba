﻿using System.IO;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using Masuit.Tools.Security;

namespace PeHubCoreNorm;

public class PwdUtils
{
    private static readonly string _privateKey;

    static PwdUtils()
    {
        PublicKey = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "JsonConfig", "public.key");
        PublicKey = File.ReadAllText(PublicKey);
        _privateKey = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "JsonConfig", "private.key");
        _privateKey = File.ReadAllText(_privateKey);
    }

    public static string PublicKey { get; }

    public static string DefaultPwd()
    {
        return Encrypt("123456");
    }

    /// <summary>
    ///     加密
    /// </summary>
    /// <param name="string"></param>
    /// <returns></returns>
    public static string Encrypt(string @string)
    {
        return @string.RSAEncrypt(PublicKey);
    }

    /// <summary>
    ///     解密
    /// </summary>
    /// <param name="string"></param>
    /// <returns></returns>
    public static string Decrypt(string @string)
    {
        return @string.RSADecrypt(_privateKey);
    }

    /// <summary>
    ///     密码相似度
    /// </summary>
    /// <param name="oldPassword"></param>
    /// <param name="newPassword"></param>
    /// <returns></returns>
    public static double Similarity(string oldPassword, string newPassword)
    {
        var editDistance = LevenshteinDistance(oldPassword, newPassword);
        var similarity = 1.0 - editDistance / (double)Math.Max(oldPassword.Length, newPassword.Length);
        return similarity * 100;
    }

    public static bool IsValidPassword(string password)
    {
        var pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{6,50}$";
        return Regex.IsMatch(password, pattern);
    }

    /// <summary>
    ///     计算莱文斯坦距离算法
    /// </summary>
    /// <param name="s1"></param>
    /// <param name="s2"></param>
    /// <returns></returns>
    public static int LevenshteinDistance(string s1, string s2)
    {
        var distance = new int[s1.Length + 1, s2.Length + 1];

        for (var i = 0; i <= s1.Length; i++)
            distance[i, 0] = i;

        for (var j = 0; j <= s2.Length; j++)
            distance[0, j] = j;

        for (var i = 1; i <= s1.Length; i++)
        for (var j = 1; j <= s2.Length; j++)
        {
            var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;

            distance[i, j] = Math.Min(
                Math.Min(distance[i - 1, j] + 1, distance[i, j - 1] + 1),
                distance[i - 1, j - 1] + cost);
        }

        return distance[s1.Length, s2.Length];
    }

    /// <summary>
    /// md5加密
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
	public static string GetMD5(string input)
	{
		using MD5 md5 = MD5.Create();
		byte[] inputBytes = Encoding.UTF8.GetBytes(input); // 使用 UTF-8 编码[3]()
		byte[] hashBytes = md5.ComputeHash(inputBytes);
		StringBuilder sb = new StringBuilder();
		foreach (byte b in hashBytes)
		{
			sb.Append(b.ToString("x2")); // "x2" 确保两位十六进制[3]()
		}
		return sb.ToString();
	}


}