﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 记录产生短信的临时验证码
/// </summary>
[SugarTable(TableName = "VerificationMessage", TableDescription = "短信验证码")]
public class VerificationMessage
{
    /// <summary>
    /// 电话
    /// </summary>
    [SugarColumn(IsNullable = false, IsPrimaryKey = true, Length = 20)]
    public string Tel { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "Code", ColumnDescription = "验证码", Length = 10)]
    public string Code { get; set; }

    /// <summary>
    /// 发送次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "SendTimes", ColumnDescription = "发送次数")]
    public int SendTimes { get; set; }

    /// <summary>
    /// 错误次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "ErrorTimes", ColumnDescription = "错误次数")]
    public int ErrorTimes { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "UpdateTime", ColumnDescription = "更新时间")]
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// CreateTime
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreateTime { get; set; }

}
