﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 保存套餐信息及对应组合
/// </summary>
public class SaveClusterAndMapComb
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 是否固定套餐(Y:不允许修改组合对应 N:允许修改组合对应)
    /// </summary>
    public string IsFixed { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public string IsEnabled { get; set; }

    /// <summary>
    /// 体检分类
    /// </summary>
    public string PeCls { get; set; }

    /// <summary>
    /// 加项规则
    /// </summary>
    public string AddRule { get; set; }

    /// <summary>
    /// 限制年龄标识
    /// </summary>
    public string IsAgeLimit { get; set; }

    /// <summary>
    /// 年龄下限
    /// </summary>
    public int LowerAgeLimit { get; set; }

    /// <summary>
    /// 年龄上限
    /// </summary>
    public int UpperAgeLimit { get; set; }

    /// <summary>
    /// 套餐简介
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string Notice { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public string Tag { get; set; }

    /// <summary>
    /// 套餐对应的组合
    /// </summary>
    public List<ClusterCombs> ClusterCombs { get; set; }
}

/// <summary>
/// 套餐对应的组合
/// </summary>
public class ClusterCombs
{
    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
}