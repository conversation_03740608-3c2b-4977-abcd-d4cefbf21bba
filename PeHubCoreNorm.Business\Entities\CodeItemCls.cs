﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 项目分类表
/// </summary>
[SugarTable(TableName = "CodeItemCls", TableDescription = "项目分类表")]
public class CodeItemCls : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClsCode", ColumnDescription = "项目分类代码", Length = 10)]
    public string ClsCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClsName", ColumnDescription = "项目分类名称", Length = 30)]
    public string ClsName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ShortName", ColumnDescription = "分类简称", Length = 30)]
    public string ShortName { get; set; }
}
