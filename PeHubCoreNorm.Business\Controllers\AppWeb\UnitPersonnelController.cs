using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using PeHubCoreNorm.Business.Services.UnitPhysicalExamination;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 单位人员控制器（前端应用）
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/appweb/[controller]")]
[AllowAnonymous]
public class UnitPersonnelController : BaseControllerAuthorize
{
    private readonly IUnitPhysicalExaminationService _unitPhysicalExaminationService;

    public UnitPersonnelController(IUnitPhysicalExaminationService unitPhysicalExaminationService)
    {
        _unitPhysicalExaminationService = unitPhysicalExaminationService;
    }

    /// <summary>
    /// 验证单位人员身份并获取详细信息（包含套餐信息）
    /// </summary>
    /// <param name="input">验证条件</param>
    /// <returns>人员详细信息</returns>
    [HttpPost("AuthenticationAndGetDetail")]
    [AllowAnonymous]
    public async Task<UnitPersonnelDetailOutput> AuthenticationAndGetDetail([FromBody] UnitPersonnelQueryInput input)
    {
        try
        {
            // 先验证身份
            var personnel = await _unitPhysicalExaminationService.AuthenticationToUnti(new AuthenticationToUntiInput
            {
                CompanyCode = input.CompanyCode,
                BatchNumber = input.BatchNumber,
                IdNumber = input.IdNumber,
                Tel = input.Tel
            });

            if (personnel == null)
            {
                Unify.SetError("身份验证失败，请检查输入信息是否正确");
                return null;
            }

            // 获取详细信息
            return await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
        }
        catch (Exception ex)
        {
            Unify.SetError("查询失败：" + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// 验证单位人员身份
    /// </summary>
    /// <param name="input">验证条件</param>
    /// <returns>验证结果</returns>
    [HttpPost("AuthenticationToUnit")]
    [AllowAnonymous]
    public async Task<object> AuthenticationToUnit([FromBody] UnitPersonnelQueryInput input)
    {
        try
        {
            var personnel = await _unitPhysicalExaminationService.AuthenticationToUnti(new AuthenticationToUntiInput
            {
                CompanyCode = input.CompanyCode,
                BatchNumber = input.BatchNumber,
                IdNumber = input.IdNumber,
                Tel = input.Tel
            });

            if (personnel == null)
            {
                return new { success = false, message = "身份验证失败，请检查输入信息是否正确" };
            }

            return new 
            { 
                success = true, 
                message = "身份验证成功",
                data = new
                {
                    id = personnel.Id,
                    employeeName = personnel.EmployeeName,
                    companyName = personnel.CompanyName,
                    department = personnel.Department,
                    packageName = personnel.PackAgeName,
                    status = personnel.Status,
                    validPeriod = $"{personnel.StartTime:yyyy-MM-dd} 至 {personnel.EndTime:yyyy-MM-dd}"
                }
            };
        }
        catch (Exception ex)
        {
            return new { success = false, message = "验证失败：" + ex.Message };
        }
    }

    /// <summary>
    /// 获取单位人员套餐信息
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>套餐信息</returns>
    [HttpPost("GetPackageInfo")]
    [AllowAnonymous]
    public async Task<PackageDetailInfo> GetPackageInfo([FromBody] UnitPersonnelQueryInput input)
    {
        try
        {
            var personnel = await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
            return personnel?.PackageDetail;
        }
        catch (Exception ex)
        {
            Unify.SetError("获取套餐信息失败：" + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// 检查人员是否有效
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>检查结果</returns>
    [HttpPost("CheckPersonnelValid")]
    [AllowAnonymous]
    public async Task<object> CheckPersonnelValid([FromBody] UnitPersonnelQueryInput input)
    {
        try
        {
            var personnel = await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
            
            if (personnel == null)
            {
                return new { valid = false, message = "人员信息不存在" };
            }

            if (personnel.Status != 1)
            {
                return new { valid = false, message = "人员状态未激活" };
            }

            if (DateTime.Now < personnel.StartTime)
            {
                return new { valid = false, message = "体检有效期未开始" };
            }

            if (DateTime.Now > personnel.EndTime)
            {
                return new { valid = false, message = "体检有效期已过期" };
            }

            return new 
            { 
                valid = true, 
                message = "人员信息有效",
                data = new
                {
                    employeeName = personnel.EmployeeName,
                    companyName = personnel.CompanyName,
                    department = personnel.Department,
                    packageName = personnel.PackAgeName,
                    packagePrice = personnel.PackageDetail?.Price,
                    validUntil = personnel.EndTime.ToString("yyyy-MM-dd")
                }
            };
        }
        catch (Exception ex)
        {
            return new { valid = false, message = "检查失败：" + ex.Message };
        }
    }

    /// <summary>
    /// 获取人员基本信息（用于预约等场景）
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>基本信息</returns>
    [HttpPost("GetPersonnelBasicInfo")]
    [AllowAnonymous]
    public async Task<object> GetPersonnelBasicInfo([FromBody] UnitPersonnelQueryInput input)
    {
        try
        {
            var personnel = await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
            
            if (personnel == null)
            {
                Unify.SetError("人员信息不存在");
                return null;
            }

            return new
            {
                id = personnel.Id,
                employeeName = personnel.EmployeeName,
                sex = personnel.Sex,
                sexText = personnel.SexText,
                age = personnel.Age,
                birthday = personnel.Birthday,
                tel = personnel.Tel,
                idNumber = personnel.IdNumber,
                idNumberType = personnel.IdNumberType,
                idNumberTypeText = personnel.IdNumberTypeText,
                companyCode = personnel.CompanyCode,
                companyName = personnel.CompanyName,
                department = personnel.Department,
                packageCode = personnel.PackAgeCode,
                packageName = personnel.PackAgeName,
                batchNumber = personnel.BatchNumber,
                married = personnel.Married,
                marriedText = personnel.MarriedText,
                ethnic = personnel.Ethnic
            };
        }
        catch (Exception ex)
        {
            Unify.SetError("获取基本信息失败：" + ex.Message);
            return null;
        }
    }

    
}
