﻿using SqlSugar;

namespace PeHubCoreNorm;

/// <summary>
///     CodeFirst功能类
/// </summary>
public static class CodeFirstUtils
{
    /// <summary>
    ///     判断类型是否实现某个泛型
    /// </summary>
    /// <param name="type">类型</param>
    /// <param name="generic">泛型类型</param>
    /// <returns>bool</returns>
    public static bool HasImplementedRawGeneric(this Type type, Type generic)
    {
        // 检查接口类型
        var isTheRawGenericType = type.GetInterfaces().Any(IsTheRawGenericType);
        if (isTheRawGenericType) return true;

        // 检查类型
        while (type != null && type != typeof(object))
        {
            isTheRawGenericType = IsTheRawGenericType(type);
            if (isTheRawGenericType) return true;
            type = type.BaseType;
        }

        return false;

        // 判断逻辑
        bool IsTheRawGenericType(Type type)
        {
            return generic == (type.IsGenericType ? type.GetGenericTypeDefinition() : type);
        }
    }

    /// <summary>
    ///     List转DataTable
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list"></param>
    /// <returns></returns>
    public static DataTable ToDataTable<T>(this List<T> list)
    {
        DataTable result = new();
        if (list.Count > 0)
        {
            // result.TableName = list[0].GetType().Name; // 表名赋值
            var propertys = list[0].GetType().GetProperties();
            foreach (var pi in propertys)
            {
                var colType = pi.PropertyType;
                if (colType.IsGenericType && colType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    colType = colType.GetGenericArguments()[0];
                if (IsIgnoreColumn(pi))
                    continue;
                if (IsJsonColumn(pi)) //如果是json特性就是sting类型
                    colType = typeof(string);
                result.Columns.Add(pi.Name, colType);
            }

            for (var i = 0; i < list.Count; i++)
            {
                ArrayList tempList = new();
                foreach (var pi in propertys)
                {
                    if (IsIgnoreColumn(pi))
                        continue;
                    var obj = pi.GetValue(list[i], null);
                    if (IsJsonColumn(pi)) //如果是json特性就是转化为json格式
                        obj = obj?.ToJson(); //如果json字符串是空就传null
                    tempList.Add(obj);
                }

                var array = tempList.ToArray();
                result.LoadDataRow(array, true);
            }
        }

        return result;
    }

    /// <summary>
    ///     排除SqlSugar忽略的列
    /// </summary>
    /// <param name="pi"></param>
    /// <returns></returns>
    private static bool IsIgnoreColumn(PropertyInfo pi)
    {
        var sc = pi.GetCustomAttributes<SugarColumn>(false).FirstOrDefault(u => u.IsIgnore);
        return sc != null;
    }

    private static bool IsJsonColumn(PropertyInfo pi)
    {
        var sc = pi.GetCustomAttributes<SugarColumn>(false).FirstOrDefault(u => u.IsJson);
        return sc != null;
    }
}