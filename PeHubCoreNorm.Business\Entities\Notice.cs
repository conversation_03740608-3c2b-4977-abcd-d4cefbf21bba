﻿using System;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 体检须知管理
    /// </summary>
    [SugarTable("Notice", TableDescription = "体检须知管理")]
    public class Notice : BaseEntity
    {
        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态", Length = 10)]
        public virtual string Status { get; set; }

        /// <summary>
        /// TypeCode
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "TypeCode", ColumnDescription = "类别编码", Length = 30)]
        public virtual string TypeCode { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnName = "Content", ColumnDescription = "内容",Length = int.MaxValue)]
        public virtual string Content { get; set; }


    }
}
