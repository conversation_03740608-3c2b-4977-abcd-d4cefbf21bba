﻿namespace PeHubCoreNorm.Template;

/// <summary>
///     学生信息
/// </summary>
[SugarTable("template_student", TableDescription = "学生信息")]
public class Student : BaseEntity
{
	/// <summary>
	///     姓名
	/// </summary>
	[SugarColumn(ColumnName = "Name", ColumnDescription = "姓名", Length = 200)]
    public virtual string Name { get; set; }

	/// <summary>
	///     年龄
	/// </summary>
	[SugarColumn(ColumnName = "Age", ColumnDescription = "年龄")]
    public virtual int Age { get; set; }
}