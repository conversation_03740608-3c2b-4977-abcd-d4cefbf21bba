using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;

namespace PeHubCoreNorm.Business.Services.IdentityAuthentication;

/// <summary>
/// 身份认证服务接口
/// </summary>
public interface IIdentityAuthenticationService : ITransient
{
    /// <summary>
    /// 身份认证
    /// </summary>
    /// <param name="input">认证输入参数</param>
    /// <returns>认证结果</returns>
    Task<IdentityAuthenticationOutput> AuthenticateIdentity(IdentityAuthenticationInput input);

    /// <summary>
    /// 发送短信验证码
    /// </summary>
    /// <param name="input">发送短信输入参数</param>
    /// <returns>发送结果</returns>
    Task<SendSmsCodeOutput> SendSmsCode(SendSmsCodeInput input);

    /// <summary>
    /// 验证短信验证码
    /// </summary>
    /// <param name="tel">电话号码</param>
    /// <param name="code">验证码</param>
    /// <returns>验证结果</returns>
    Task<bool> VerifySmsCode(string tel, string code);

    /// <summary>
    /// 根据证件号码和电话查找已激活的人员
    /// </summary>
    /// <param name="idNumber">证件号码</param>
    /// <param name="tel">电话号码</param>
    /// <param name="companyCode">单位编码（可选）</param>
    /// <param name="batchNumber">体检次数（可选）</param>
    /// <returns>人员信息列表</returns>
    Task<List<PersonnelInfo>> FindActivePersonnel(string idNumber, string tel, string companyCode = null, int? batchNumber = null);

    /// <summary>
    /// 生成认证令牌
    /// </summary>
    /// <param name="personnelInfo">人员信息</param>
    /// <returns>认证令牌</returns>
    Task<string> GenerateAuthToken(PersonnelInfo personnelInfo);

    /// <summary>
    /// 验证认证令牌
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <returns>人员信息</returns>
    Task<PersonnelInfo> ValidateAuthToken(string token);
}
