﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 短信发送记录
/// </summary>
[SugarTable(TableName = "SmsRecord", TableDescription = "短信发送记录")]
public class SmsRecord
{
    /// <summary>
    /// 电话
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Tel { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "Code", ColumnDescription = "验证码", Length = 10)]
    public string Code { get; set; }

    /// <summary>
    /// CreateTime
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 短信接口响应
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "SmsResult", ColumnDescription = "短信接口响应", Length = int.MaxValue)]
    public string SmsResult { get; set; }
}
