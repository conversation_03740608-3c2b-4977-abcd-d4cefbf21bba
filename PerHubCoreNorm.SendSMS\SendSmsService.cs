﻿using System.Net.Sockets;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Polly;

#pragma warning disable CS8618 // 在退出构造函数时，不可为 null 的字段必须包含非 null 值。请考虑声明为可以为 null。

namespace PerHubCoreNorm.SendSMS;

public class SendSmsService : ISendSmsService
{
    private const string ApiUrl = "https://api.example.com/sendSms";
    private const string ApiKey = "your_api_key";
    private const string ApiSecret = "your_api_secret";
    private readonly HttpClientHelper _httpClientHelper;
    private readonly ILogger<SendSmsService> _logger;

    public SendSmsService(HttpClientHelper httpClientHelper, ILogger<SendSmsService> logger)
    {
        _httpClientHelper = httpClientHelper;
        _logger = logger;
    }

    public async Task<SendSmsResult> SendSmsAsync(SendSmsRequest request)
    {
        var retry = Policy.Handle<SocketException>()
            .Or<HttpRequestException>()
            .WaitAndRetryAsync(
                3,
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                (exception, retryCount, context) => { _logger.LogError(exception.Message); });
        _logger.LogInformation("发送短信验证码：{0}", request.Message);
        var policyResult = await retry.ExecuteAndCaptureAsync(async () => { return await SendSms(request); });
        if (policyResult.Result == null)
            return new SendSmsResult
            {
                success = false,
                message = "发送短信验证码失败，请稍后再试。"
            };
        return policyResult.Result;
    }

    public async Task SendSmsBatchAsync(List<string> phoneNumbers, string message)
    {
        throw new NotImplementedException();
    }

    private async Task<SendSmsResult> SendSms(SendSmsRequest message)
    {
        var headers = new Dictionary<string, string>
        {
            { "Content-Type", "application/json" },
            { "Authorization", $"Basic {ApiKey}:{ApiSecret}" }
        };
        var result = await _httpClientHelper.PostAsync<SendSmsResult>(ApiUrl, JsonSerializer.Serialize(message),
            default, default);
        return result;
    }
}

public class Template_param
{
    public string Code { get; set; }
}

public class SendSmsRequest
{
    public string PhoneNumber { get; set; }
    public string Message { get; set; }
    public string SignName { get; set; }
    public string TemplateCode { get; set; }
    public Template_param template_param { get; set; }
}

public class SendSmsResult
{
    public bool success { get; set; }
    public string message { get; set; }
    public string request_id { get; set; }
    public string data { get; set; }
}