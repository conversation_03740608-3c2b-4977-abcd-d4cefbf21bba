﻿namespace PeHubCoreNorm.RBAC;

public interface ISysUserService : ITransient
{
    Task<SysUser> GetSysUserByAccount(string account);

    Task<SysUser> GetSysUserById(string userId);

    Task SetLogined(string userId);

    Task<SqlSugarPagedList<SysUser>> Page(UserPageInput input);

    Task Add(UserAddInput input);

    Task Edit(UserEditInput input);

    Task Delete(BaseIdsInput input);

    Task EnableUser(BaseIdInput input);

    Task DisableUser(BaseIdInput input);

    Task ResetPassword(BaseIdInput input);

    Task GrantRole(UserGrantRoleInput input);

    Task<List<string>> OwnRole(BaseIdInput input);

    Task<List<string>> OwnButtonCodeList();

    Task<List<string>> OwnPermissionCodeList();

    Task<dynamic> UpdatePassword(UpdatePasswordInput input);

    Task<string> UpdateAvatar(BaseFileInput input);

    Task<dynamic> UpdateUserInfo(UpdateInfoInput input);

    /// <summary>
    ///     修改默认模块
    /// </summary>
    /// <param name="input">默认模块输入参数</param>
    /// <returns></returns>
    Task SetDefaultModule(SetDefaultModuleInput input);

    /// <summary>
    ///     获取codelist
    /// </summary>
    /// <returns></returns>
    Task<List<string>> OwnRoleCode();


    Task<List<RoleSelectorOutPut>> OwnRoleRoleSelect(BaseIdInput input);
}