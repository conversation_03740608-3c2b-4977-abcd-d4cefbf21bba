﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检订单入参
/// </summary>
public class PersonOrderInput
{
    /// <summary>
    /// 用户id
    /// </summary>
    public string MembersId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public string CardType { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime Birthday { get; set; }

    /// <summary>
    /// 性别(1:男 2:女)
    /// </summary>
    public string Sex { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string? MarryStatus { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 订单总价格
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 时间段编码Id
    /// </summary>
    public string TimeId { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 客户体检时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 订单详情
    /// </summary>
    public List<PersonOrderDetailInput> OrderDetail { get; set; }
}

/// <summary>
/// 订单明细入参
/// </summary>
public class PersonOrderDetailInput
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 组合类型(1:套餐 2:加项包)
    /// </summary>
    public int CombType { get; set; }

    /// <summary>
    /// 订单类型(1:个人 2:团体)
    /// </summary>
    public int OrderType { get; set; }
}