﻿using Microsoft.Extensions.DependencyInjection.Extensions;
using NewLife.Caching;
using PeHubCoreNorm.Cache;

namespace PeHubCoreNorm.Core.ServicesExtension;

public static class ServicesExtension
{
    public static IServiceCollection AddCache(this IServiceCollection services)
    {
        var cacheSettings = App.Get<CacheSettings>("CacheSettings");

        if (cacheSettings.UseRedis)
        {
            var rs = cacheSettings.RedisSettings;

            //注入NewLife.Caching Redis
            var redis = new FullRedis($"{rs.Host}:{rs.Port}", rs.Password, rs.Db)
            {
                Timeout = rs.TimeOut
            };

            services.TryAddSingleton<ICache>(redis);
            services.AddSingleton<Redis>(redis);
            services.AddSingleton(redis);

            //注入二次封装的Redis
            services.AddSingleton<ICacheService, RedisCacheService>();
        }
        else
        {
            // services.AddMemoryCache();
            services.AddSingleton<ICacheService, MemoryCacheService>();
        }

        return services;
    }
}