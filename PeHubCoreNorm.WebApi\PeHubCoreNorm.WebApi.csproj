﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>latest</LangVersion>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <InvariantGlobalization>false</InvariantGlobalization>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <SatelliteResourceLanguages>zh-Hans</SatelliteResourceLanguages>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\PeHubCoreNorm.Business\PeHubCoreNorm.Business.csproj" />
        <ProjectReference Include="..\PeHubCoreNorm.Framework\PeHubCoreNorm.Framework.csproj" />
        <ProjectReference Include="..\PeHubCoreNorm.Job\PeHubCoreNorm.Job.csproj" />
        <ProjectReference Include="..\PeHubCoreNorm.Template\PeHubCoreNorm.Template.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Update="JsonConfig\Common.All.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="JsonConfig\i18n\en-us.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="JsonConfig\i18n\zh-cn.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="JsonConfig\i18n\zh-tw.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="NLog.config">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <Content Update="SeedData\PeHubCoreNorm\student.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
          <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <None Update="ip2region.db">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="wwwroot\Uploads\" />
    </ItemGroup>

</Project>
