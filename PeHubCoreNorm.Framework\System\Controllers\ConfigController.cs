﻿using System.IO;

namespace PeHubCoreNorm.System;

[ApiExplorerSettings(GroupName = "System")]
[Route("sys/[controller]")]
public class ConfigController : BaseControllerRoleAuthorize
{
    private readonly IConfigService _configService; //系统配置服务

    public ConfigController(IConfigService configService)
    {
        _configService = configService;
    }

    /// <summary>
    ///     获取系统基础配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/api/ico")]
    [AllowAnonymous]
    [IgnoreLog]
    public async Task<dynamic> Ico()
    {
        var list = await _configService.GetListByCategory(CateGoryConst.Config_SYS_BASE);
        var iocobj = list.Where(x => x.ConfigKey.Equals("SYS_ICO")).FirstOrDefault();

        var icoBase64 = iocobj.ConfigValue.Split(",")[1]; //获取ico的base64
        //转为byte数组
        var bytes = Convert.FromBase64String(icoBase64);
        //将byte数组转为流
        var stream = new MemoryStream(bytes);
        return new FileStreamResult(stream, "image/x-icon");
    }

    /// <summary>
    ///     获取系统基础配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("sysBaseList")]
    [AllowAnonymous]
	[IgnoreLog]
	//[ActionPermission(ActionType.Query, "获取系统基础配置","系统配置")]
	public async Task<List<DevConfig>> SysBaseList()
    {
        var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_LOGIN_POLICY); //登录策略
        return await _configService.GetListByCategory(CateGoryConst.Config_SYS_BASE);
    }


    /// <summary>
    ///     获取系统基础配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("loginPolicy")]
    [AllowAnonymous]
	[IgnoreLog]

	public async Task<dynamic> LoginPolicy()
    {
        var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_LOGIN_POLICY); //登录策略
        return loginPolicy;
    }

    /// <summary>
    ///     获取配置列表
    /// </summary>
    /// <param name="category">分类</param>
    /// <returns></returns>
    [HttpGet("list")]
    [ActionPermission(ActionType.Query, "获取配置列表", "系统配置")]
    public async Task<dynamic> List(string category)
    {
        return await _configService.GetListByCategory(category);
    }

    /// <summary>
    ///     配置分页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [ActionPermission(ActionType.Query, "配置分页", "系统配置")]
    public async Task<dynamic> Page([FromQuery] ConfigPageInput input)
    {
        return await _configService.Page(input);
    }

    /// <summary>
    ///     添加配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("add")]
    [ActionPermission(ActionType.Button, "添加配置", "系统配置")]
    public async Task Add([FromBody] ConfigAddInput input)
    {
        await _configService.Add(input);
    }

    /// <summary>
    ///     修改配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("edit")]
    [ActionPermission(ActionType.Button, "修改配置", "系统配置")]
    public async Task Edit([FromBody] ConfigEditInput input)
    {
        await _configService.Edit(input);
    }

    /// <summary>
    ///     删除配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    [ActionPermission(ActionType.Button, "删除配置", "系统配置")]
    public async Task Delete([FromBody] ConfigDeleteInput input)
    {
        await _configService.Delete(input);
    }

    /// <summary>
    ///     配置批量更新
    /// </summary>
    /// <returns></returns>
    [HttpPost("editBatch")]
    [ActionPermission(ActionType.Button, "配置批量更新", "系统配置")]
    public async Task EditBatch([FromBody] List<DevConfig> devConfigs)
    {
        await _configService.EditBatch(devConfigs);
    }
}