﻿using Hangfire.Server;
using Microsoft.Extensions.Logging;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Utils;
using System.Drawing.Printing;
using System.Linq.Expressions;
using System.Xml.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace PeHubCoreNorm.Business;

public class UnitPersonnelListService : BizDbRepository<UnitPersonnelList>, IUnitPersonnelListService
{
    private readonly ILogger<UnitPersonnelListService> _logger;

    public UnitPersonnelListService(ILogger<UnitPersonnelListService> logger)
    {
        _logger = logger;
    }

    public async Task<UnitPersonnelList> CreateUnitPersons(UnitPersonnelList unitPerson)
    {

        return await Context.Insertable(unitPerson).ExecuteReturnEntityAsync();
    }

    public async Task<SqlSugarPagedList<UnitPersonnelList>> GetUnitPersons(QueryUnitPersonInput query)
    {
        Expression<Func<UnitPersonnelList, bool>> exp = Expressionable.Create<UnitPersonnelList>() //创建表达式
        .AndIF(query.Status >= 0, it => it.Status == query.Status)
        .AndIF(!string.IsNullOrEmpty(query.CompanyCode), it => it.CompanyCode == query.CompanyCode)
        .AndIF(query.BatchNumber > 0, it => it.BatchNumber == query.BatchNumber)
        .AndIF(!string.IsNullOrEmpty(query.EmployeeName), it => it.EmployeeName == query.EmployeeName)
        .AndIF(!string.IsNullOrEmpty(query.PackAgeCode), it => it.PackAgeCode == query.PackAgeCode)
        .ToExpression();

        RefAsync<int> total = 0;
        return  await Context.Queryable<UnitPersonnelList>().Where(exp).ToPagedListAsync(query.pageNum, query.pageSize);

    }


    public async Task<int> ActivationUnitPersons(Expression<Func<UnitPersonnelList, bool>> whereExpression, Expression<Func<UnitPersonnelList, UnitPersonnelList>> updateExpression)
    {
        return await Context.Updateable<UnitPersonnelList>()
                      .Where(whereExpression)
                      .SetColumns(updateExpression)
                      .ExecuteCommandAsync();
    }

    public async Task<int> DelUnitPersons(Expression<Func<UnitPersonnelList, bool>> whereExpression)
    {
        return await Context.Deleteable<UnitPersonnelList>()
                 .Where(whereExpression)
                      .ExecuteCommandAsync();
    }

    public async Task<int> BatchCreateUnitPersons(List<UnitPersonnelList> unitPersons)
    {
       return  await Context.Insertable(unitPersons).ExecuteCommandAsync();


    }

    public async Task<List<UnitPersonnelList>> VerifyPersonInfo(AuthenticationToUntiInput input)
    {
        Expression<Func<UnitPersonnelList, bool>> exp = Expressionable.Create<UnitPersonnelList>() //创建表达式
        .AndIF(!string.IsNullOrEmpty(input.IdNumber), it => it.IdNumber == input.IdNumber)
        .AndIF(!string.IsNullOrEmpty(input.Tel), it => it.Tel == input.Tel)
        .And(x=>x.Status==1)
        .ToExpression();

        return await Context.Queryable<UnitPersonnelList>().Where(exp).ToListAsync();
    }
}


