﻿using SqlSugar;

namespace PeHubCoreNorm;

/// <summary>
///     仓储模式对象
/// </summary>
public class DbRepository<T> : SimpleClient<T> where T : class, new()
{
    protected ITenant itenant; //多租户事务、GetConnection、IsAnyConnection等功能

    public DbRepository(ISqlSugarClient context = null) : base(context) //注意这里要有默认值等于null
    {
        Context = DbContext.Db.GetConnectionScopeWithAttr<T>(); //ioc注入的对象
        itenant = DbContext.Db;
    }

    #region 仓储方法拓展

    #region 插入

    /// <summary>
    ///     批量插入判断走普通导入还是大数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="threshold">阈值</param>
    /// <returns></returns>
    public virtual async Task<int> InsertOrBulkCopy(List<T> data, int threshold = 10000)
    {
        if (data.Count > threshold)
            return await Context.Fastest<T>().BulkCopyAsync(data); //大数据导入
        return await Context.Insertable(data).ExecuteCommandAsync(); //普通导入
    }

    #endregion 插入

    #region 列表

    /// <summary>
    ///     获取列表指定多个字段
    /// </summary>
    /// <param name="whereExpression">查询条件</param>
    /// <param name="selectExpression">查询字段</param>
    /// <returns></returns>
    public virtual Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression,
        Expression<Func<T, T>> selectExpression)
    {
        return Context.Queryable<T>().Where(whereExpression).Select(selectExpression).ToListAsync();
    }

    /// <summary>
    ///     获取列表指定单个字段
    /// </summary>
    /// <param name="whereExpression">查询条件</param>
    /// <param name="selectExpression">查询字段</param>
    /// <returns></returns>
    public virtual Task<List<string>> GetListAsync(Expression<Func<T, bool>> whereExpression,
        Expression<Func<T, string>> selectExpression)
    {
        return Context.Queryable<T>().Where(whereExpression).Select(selectExpression).ToListAsync();
    }

    /// <summary>
    ///     获取列表指定单个字段
    /// </summary>
    /// <param name="whereExpression">查询条件</param>
    /// <param name="selectExpression">查询字段</param>
    /// <returns></returns>
    public virtual Task<List<long>> GetListAsync(Expression<Func<T, bool>> whereExpression,
        Expression<Func<T, long>> selectExpression)
    {
        return Context.Queryable<T>().Where(whereExpression).Select(selectExpression).ToListAsync();
    }

    #endregion 列表

    #region 单查

    /// <summary>
    ///     获取指定表的单个字段
    /// </summary>
    /// <param name="whereExpression">查询条件</param>
    /// <param name="selectExpression">查询字段</param>
    /// <returns></returns>
    public virtual Task<string> GetFirstAsync(Expression<Func<T, bool>> whereExpression,
        Expression<Func<T, string>> selectExpression)
    {
        return Context.Queryable<T>().Where(whereExpression).Select(selectExpression).FirstAsync();
    }

    /// <summary>
    ///     获取指定表的单个字段
    /// </summary>
    /// <param name="whereExpression">查询条件</param>
    /// <param name="selectExpression">查询字段</param>
    /// <returns></returns>
    public virtual Task<long> GetFirstAsync(Expression<Func<T, bool>> whereExpression,
        Expression<Func<T, long>> selectExpression)
    {
        return Context.Queryable<T>().Where(whereExpression).Select(selectExpression).FirstAsync();
    }


    /// <summary>
    ///     根据条件查询获取自动分表的单个数据
    /// </summary>
    /// <param name="whereExpression">条件表达式</param>
    /// <param name="getTableNamesFunc">分表查询表达式</param>
    /// <returns>实体</returns>
    public virtual Task<T> GetFirstSplitTableAsync(Expression<Func<T, bool>> whereExpression,
        Func<List<SplitTableInfo>, IEnumerable<SplitTableInfo>> getTableNamesFunc)
    {
        return Context.Queryable<T>().Where(whereExpression).SplitTable(getTableNamesFunc).FirstAsync();
    }

    #endregion 单查

    #endregion 仓储方法拓展
}