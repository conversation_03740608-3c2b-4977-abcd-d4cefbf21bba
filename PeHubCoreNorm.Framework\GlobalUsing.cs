﻿global using DotNetCore.CAP;
global using FluentValidation;
global using Mapster;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using PeHubCoreNorm;
global using SqlSugar;
global using System;
global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System.Data;
global using System.Linq;
global using System.Reflection;
global using System.Threading.Tasks;
global using ICacheService = PeHubCoreNorm.Cache.ICacheService;