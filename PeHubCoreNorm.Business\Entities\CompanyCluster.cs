﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位体检套餐信息
/// </summary>
[SugarTable("CompanyCluster", TableDescription = "单位体检套餐信息")]
public class CompanyCluster : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyTimes", ColumnDescription = "体检次数", Length = 8)]
    public int CompanyTimes { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 8)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusName", ColumnDescription = "套餐名称", Length = 100)]
    public string ClusName { get; set; }
}