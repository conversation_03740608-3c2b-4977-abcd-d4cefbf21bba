# 单位人员查询服务系统说明

## 系统概述

单位人员查询服务系统是体检预约系统的核心组件，专门处理企业单位人员信息的查询和管理。系统通过连表查询的方式，将单位人员信息与套餐信息进行关联，为用户提供完整的人员体检信息。

## 核心功能

### 1. 身份验证查询
- **多条件验证** - 通过单位编码、批次号、证件号、手机号四重验证
- **身份认证** - 验证人员身份的真实性和有效性
- **安全保障** - 确保只有合法人员才能查询到相关信息

### 2. 人员信息查询
- **详细信息查询** - 获取人员完整的基本信息和套餐信息
- **分页查询** - 支持大数据量的分页查询
- **条件筛选** - 支持多种条件的组合查询
- **关键词搜索** - 支持姓名、证件号、手机号的模糊搜索

### 3. 套餐信息关联
- **套餐详情** - 获取套餐的名称、编码、价格等详细信息
- **套餐规则** - 包含年龄限制、性别限制、加项规则等
- **套餐描述** - 提供套餐简介、注意事项等说明信息

### 4. 数据统计分析
- **人员统计** - 按部门、性别、年龄等维度统计人员信息
- **套餐统计** - 统计各套餐的使用情况
- **状态统计** - 统计激活、未激活人员数量

## 文件结构

### 输入模型
- `UnitPersonnelQueryInput.cs` - 单位人员查询输入
- `UnitPersonnelPageQueryInput.cs` - 单位人员分页查询输入

### 输出模型
- `UnitPersonnelDetailOutput.cs` - 单位人员详细信息输出
- `UnitPersonnelBriefOutput.cs` - 单位人员简要信息输出
- `PackageDetailInfo.cs` - 套餐详细信息

### 服务层
- `IUnitPhysicalExaminationService.cs` - 单位体检服务接口（已扩展）
- `UnitPhysicalExaminatioService.cs` - 单位体检服务实现（已扩展）

### 控制器
- `Admin/UnitPersonnelController.cs` - 管理端单位人员控制器
- `AppWeb/UnitPersonnelController.cs` - 前端应用单位人员控制器

### 实体类
- `UnitPersonnelList.cs` - 单位人员列表实体（已存在）
- `CodeCluster.cs` - 套餐实体（已存在）

## API 接口

### 管理端接口 (Admin)

#### 1. 查询单位人员详细信息
```
POST /admin/UnitPersonnel/GetUnitPersonnelWithPackageInfo
```

**请求参数**:
```json
{
  "companyCode": "COMP001",
  "batchNumber": 1,
  "idNumber": "身份证号",
  "tel": "13800138000"
}
```

**响应数据**:
```json
{
  "data": {
    "id": "人员ID",
    "companyCode": "COMP001",
    "companyName": "测试公司",
    "department": "技术部",
    "status": 1,
    "statusText": "已激活",
    "employeeName": "张三",
    "sex": 1,
    "sexText": "男",
    "age": 30,
    "tel": "13800138000",
    "idNumber": "身份证号",
    "packageDetail": {
      "clusCode": "CLUS001",
      "clusName": "基础体检套餐",
      "price": 500.00,
      "gender": "0",
      "genderText": "通用",
      "description": "套餐简介",
      "notice": "注意事项"
    }
  }
}
```

#### 2. 分页查询单位人员信息
```
POST /admin/UnitPersonnel/GetUnitPersonnelPageList
```

#### 3. 获取单位人员统计信息
```
GET /admin/UnitPersonnel/GetUnitPersonnelStatistics?companyCode=COMP001&batchNumber=1
```

#### 4. 批量更新人员状态
```
POST /admin/UnitPersonnel/BatchUpdateStatus?status=1
```

#### 5. 导出单位人员信息
```
POST /admin/UnitPersonnel/ExportUnitPersonnel
```

### 前端应用接口 (AppWeb)

#### 1. 身份验证并获取详细信息
```
POST /appweb/UnitPersonnel/AuthenticationAndGetDetail
```

#### 2. 验证单位人员身份
```
POST /appweb/UnitPersonnel/AuthenticationToUnit
```

#### 3. 获取套餐信息
```
POST /appweb/UnitPersonnel/GetPackageInfo
```

#### 4. 检查人员是否有效
```
POST /appweb/UnitPersonnel/CheckPersonnelValid
```

#### 5. 获取人员基本信息
```
POST /appweb/UnitPersonnel/GetPersonnelBasicInfo
```

#### 6. 搜索单位人员
```
GET /appweb/UnitPersonnel/SearchPersonnel?keyword=张三&companyCode=COMP001
```

## 数据库表关联

### 主要表结构

#### UnitPersonnelList（单位人员列表）
- **Id** - 人员ID
- **CompanyCode** - 单位编码
- **CompanyName** - 单位名称
- **BatchNumber** - 批次号
- **EmployeeName** - 员工姓名
- **IdNumber** - 证件号码
- **Tel** - 手机号码
- **PackAgeCode** - 套餐编码
- **PackAgeName** - 套餐名称
- **Status** - 状态（0:未激活 1:已激活）

#### CodeCluster（套餐表）
- **ClusCode** - 套餐编码
- **ClusName** - 套餐名称
- **Price** - 套餐价格
- **Gender** - 性别限制
- **IsEnabled** - 是否启用
- **Description** - 套餐简介
- **Notice** - 注意事项

### 关联查询SQL示例
```sql
SELECT 
    p.*,
    c.ClusCode,
    c.ClusName,
    c.Price,
    c.Gender,
    c.Description,
    c.Notice
FROM UnitPersonnelList p
LEFT JOIN CodeCluster c ON p.PackAgeCode = c.ClusCode
WHERE p.CompanyCode = @CompanyCode
  AND p.BatchNumber = @BatchNumber
  AND p.IdNumber = @IdNumber
  AND p.Tel = @Tel
```

## 业务规则

### 1. 身份验证规则
- 必须同时提供单位编码、批次号、证件号、手机号四个条件
- 所有条件必须完全匹配才能通过验证
- 验证失败时不返回任何人员信息

### 2. 数据权限规则
- 管理端可以查询所有单位的人员信息
- 前端应用只能查询经过身份验证的人员信息
- 敏感信息需要脱敏处理

### 3. 状态检查规则
- 只有状态为"已激活"的人员才能进行体检预约
- 需要检查体检有效期（StartTime 到 EndTime）
- 过期人员需要重新激活

### 4. 套餐关联规则
- 通过 PackAgeCode 关联套餐信息
- 套餐不存在时返回空的套餐信息
- 套餐停用时给出相应提示

## 使用示例

### JavaScript 调用示例

```javascript
// 身份验证并获取详细信息
async function authenticateAndGetDetail() {
    const authData = {
        companyCode: 'COMP001',
        batchNumber: 1,
        idNumber: '身份证号',
        tel: '13800138000'
    };

    const response = await fetch('/appweb/UnitPersonnel/AuthenticationAndGetDetail', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(authData)
    });

    const result = await response.json();
    if (result.data) {
        console.log('人员信息:', result.data);
        console.log('套餐信息:', result.data.packageDetail);
    }
}

// 检查人员是否有效
async function checkPersonnelValid() {
    const checkData = {
        companyCode: 'COMP001',
        batchNumber: 1,
        idNumber: '身份证号',
        tel: '13800138000'
    };

    const response = await fetch('/appweb/UnitPersonnel/CheckPersonnelValid', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(checkData)
    });

    const result = await response.json();
    if (result.data.valid) {
        console.log('人员有效，可以进行体检预约');
    } else {
        console.log('人员无效:', result.data.message);
    }
}

// 搜索单位人员
async function searchPersonnel() {
    const response = await fetch('/appweb/UnitPersonnel/SearchPersonnel?keyword=张三&companyCode=COMP001&pageNum=1&pageSize=10');
    
    const result = await response.json();
    console.log('搜索结果:', result.data);
}

// 分页查询人员信息（管理端）
async function getPersonnelPageList() {
    const queryData = {
        companyCode: 'COMP001',
        batchNumber: 1,
        pageNum: 1,
        pageSize: 20,
        status: 1
    };

    const response = await fetch('/admin/UnitPersonnel/GetUnitPersonnelPageList', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(queryData)
    });

    const result = await response.json();
    console.log('人员列表:', result.data);
}
```

### C# 调用示例

```csharp
// 查询单位人员详细信息
var queryInput = new UnitPersonnelQueryInput
{
    CompanyCode = "COMP001",
    BatchNumber = 1,
    IdNumber = "身份证号",
    Tel = "13800138000"
};

var personnelDetail = await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(queryInput);

if (personnelDetail != null)
{
    Console.WriteLine($"人员姓名: {personnelDetail.EmployeeName}");
    Console.WriteLine($"单位名称: {personnelDetail.CompanyName}");
    Console.WriteLine($"套餐名称: {personnelDetail.PackAgeName}");
    
    if (personnelDetail.PackageDetail != null)
    {
        Console.WriteLine($"套餐价格: {personnelDetail.PackageDetail.Price}");
        Console.WriteLine($"套餐描述: {personnelDetail.PackageDetail.Description}");
    }
}

// 分页查询
var pageQueryInput = new UnitPersonnelPageQueryInput
{
    CompanyCode = "COMP001",
    BatchNumber = 1,
    pageNum = 1,
    pageSize = 20,
    Status = 1
};

var pageResult = await _unitPhysicalExaminationService.GetUnitPersonnelPageList(pageQueryInput);
Console.WriteLine($"总记录数: {pageResult.TotalCount}");
```

## 注意事项

1. **数据安全**: 身份验证接口需要严格的参数验证，防止恶意查询
2. **性能优化**: 大数据量查询时需要合理使用分页和索引
3. **缓存策略**: 套餐信息等相对稳定的数据可以考虑缓存
4. **日志记录**: 重要的查询操作需要记录日志便于审计
5. **异常处理**: 完善的异常处理机制，给用户友好的错误提示
6. **数据一致性**: 确保人员信息与套餐信息的一致性
7. **权限控制**: 不同角色用户的数据访问权限控制
8. **敏感信息**: 证件号、手机号等敏感信息的脱敏处理

## 扩展功能

1. **Excel导入导出**: 支持人员信息的批量导入和导出
2. **数据同步**: 与HR系统的数据同步功能
3. **消息通知**: 人员状态变更时的消息通知
4. **报表统计**: 更丰富的统计报表和图表展示
5. **审计日志**: 详细的操作审计日志
6. **数据备份**: 重要数据的定期备份机制
