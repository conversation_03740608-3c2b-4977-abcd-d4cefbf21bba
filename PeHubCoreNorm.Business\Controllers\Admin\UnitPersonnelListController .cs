﻿namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 单位人员名单
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class UnitPersonnelListController : BaseControllerAuthorize
{
    private readonly IUnitPersonnelListService _unitPersonnelListService;
    private readonly IBasicCodeService _basicCodeService;

    public UnitPersonnelListController(IUnitPersonnelListService unitPersonnelListService, IBasicCodeService basicCodeService)
    {
        _unitPersonnelListService = unitPersonnelListService;
        _basicCodeService = basicCodeService;
    }



    /// <summary>
    /// 创建单位人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("CreateUnitPersons")]
    [ActionPermission(ActionType.Query, "创建单位人员名单")]
    public async Task<UnitPersonnelList> CreateUnitPersons([FromBody] CreateUnitPersonInput input)
    {

        UnitPersonnelList unitPersonnelList = input.Adapt<CreateUnitPersonInput, UnitPersonnelList>();
        return await _unitPersonnelListService.CreateUnitPersons(unitPersonnelList);
    }

    /// <summary>
    /// 批量创建单位人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("BatchCreateUnitPersons")]
    [ActionPermission(ActionType.Query, "批量创建单位人员名单")]
    public async Task<int> BatchCreateUnitPersons([FromBody] List<CreateUnitPersonInput> input)
    {

        List<UnitPersonnelList>  unitPersonnelList = input.Adapt<List<CreateUnitPersonInput>,List<UnitPersonnelList> >();
        return await _unitPersonnelListService.BatchCreateUnitPersons(unitPersonnelList);
    }

    
    /// <summary>
    /// 查询单位人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("QueryUnitPersons")]
    [ActionPermission(ActionType.Query, "查询单位人员名单")]
    public async Task<SqlSugarPagedList<UnitPersonnelList>> QueryUnitPersons(QueryUnitPersonInput input)
    {

        return await _unitPersonnelListService.GetUnitPersons(input);
    }

    /// <summary>
    /// 激活人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("ActivationUnitPersons")]
    [ActionPermission(ActionType.Query, "激活人员名单")]
    public async Task<int> ActivationUnitPersons(ActivationUnitPersonsInput input)
    {

        return await _unitPersonnelListService.ActivationUnitPersons(
         p =>  p.Status == 0 && input.Ids.Contains(p.Id),
         p => new UnitPersonnelList
         {
             StartTime = input.StartTime,
             EndTime = input.EndTime,
             Status = 1, // 更新为激活状态
             ModifyDate = DateTime.Now
         });



    }


    /// <summary>
    /// 取消激活人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("CancelActivationUnitPersons")]
    [ActionPermission(ActionType.Query, "取消激活人员名单")]
    public async Task<int> CancelActivationUnitPersons(ActivationUnitPersonsInput input)
    {

        return await _unitPersonnelListService.ActivationUnitPersons(
         p => p.Status == 1 && input.Ids.Contains(p.Id),
         p => new UnitPersonnelList
         {
             Status = 0, // 更新为激活状态
             ModifyDate = DateTime.Now
         });

    }

    /// <summary>
    /// 删除未激活人员名单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(Company[]), 200)]
    [HttpPost("DelUnitPersons")]
    [ActionPermission(ActionType.Query, "删除未激活人员名单")]
    public async Task<int> DelUnitPersons(DelUnitPersonsInput input)
    {

        return await _unitPersonnelListService.DelUnitPersons( p=> p.Status == 0 && input.Ids.Contains(p.Id));

    }







}
