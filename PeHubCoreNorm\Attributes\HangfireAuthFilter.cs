﻿using Hangfire.Dashboard;

namespace PeHub<PERSON>oreNorm
{
	public class HangfireAuthFilter : IDashboardAuthorizationFilter
	{
		private readonly string _username;
		private readonly string _password;

		public HangfireAuthFilter(string username, string password)
		{
			_username = username;
			_password = password;
		}

		public bool Authorize(DashboardContext context)
		{
			var httpContext = context.GetHttpContext();
			string authHeader = httpContext.Request.Headers["Authorization"];

			if (authHeader != null && authHeader.StartsWith("Basic "))
			{
				var encodedUsernamePassword = authHeader.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)[1]?.Trim();
				var decodedUsernamePassword = Encoding.UTF8.GetString(Convert.FromBase64String(encodedUsernamePassword));
				var username = decodedUsernamePassword.Split(':', 2)[0];
				var password = decodedUsernamePassword.Split(':', 2)[1];

				if (username == _username && password == _password)
				{
					return true;
				}
			}

			// 返回401 Challenge
			httpContext.Response.StatusCode = 401;
			httpContext.Response.Headers["WWW-Authenticate"] = "Basic realm=\"Hangfire Dashboard\"";
			return false;
		}
	}
}
