using Microsoft.AspNetCore.Mvc;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 团检订单控制器（前端应用）
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/appweb/[controller]")]
public class TeamOrderController : BaseControllerAuthorize
{
    private readonly IOrderService _orderService;

    public TeamOrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    /// <summary>
    /// 添加团检订单
    /// </summary>
    /// <param name="input">团检订单输入</param>
    /// <returns></returns>
    [HttpPost("AddTeamOrder")]
    [ActionPermission(ActionType.Button, "添加团检订单", "APP端团检业务")]
    public async Task<bool> AddTeamOrder([FromBody] TeamOrderInput input)
    {
        // 设置当前用户ID
        input.MembersId = UserManager.UserId;
        return await _orderService.AddTeamOrder(input);
    }

    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input">取消团检订单输入</param>
    /// <returns></returns>
    [HttpPost("CancelTeamOrder")]
    [ActionPermission(ActionType.Button, "取消团检订单", "APP端团检业务")]
    public async Task<bool> CancelTeamOrder([FromBody] CancelTeamOrderInput input)
    {
        // 确保只能取消自己的订单
        input.MemberId = UserManager.UserId;
        return await _orderService.CancelTeamOrder(input);
    }

    /// <summary>
    /// 获取我的团检订单列表
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns></returns>
    [HttpPost("GetMyTeamOrders")]
    [ActionPermission(ActionType.Query, "获取我的团检订单", "APP端团检业务")]
    public async Task<SqlSugarPagedList<TeamOrderBriefOutput>> GetMyTeamOrders([FromBody] TeamOrderQueryInput input)
    {
        // 只能查询自己的订单
        input.MembersId = UserManager.UserId;
        return await _orderService.GetTeamOrderPageList(input);
    }

    /// <summary>
    /// 根据ID获取团检订单详情
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    [HttpGet("GetTeamOrderById")]
    [ActionPermission(ActionType.Query, "获取团检订单详情", "APP端团检业务")]
    public async Task<TeamOrderOutput> GetTeamOrderById([FromQuery] string orderId)
    {
        var order = await _orderService.GetTeamOrderById(orderId);
        
        // 确保只能查看自己的订单
        if (order != null && order.MembersId != UserManager.UserId)
        {
            Unify.SetError("无权查看此订单");
            return null;
        }
        
        return order;
    }

    /// <summary>
    /// 获取我的团检订单统计
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetMyTeamOrderStatistics")]
    [ActionPermission(ActionType.Query, "获取我的团检订单统计", "APP端团检业务")]
    public async Task<Dictionary<string, int>> GetMyTeamOrderStatistics()
    {
        var orders = await Context.Queryable<TeamOrder>()
            .Where(x => x.MembersId == UserManager.UserId)
            .ToListAsync();

        return new Dictionary<string, int>
        {
            ["已预约"] = orders.Count(x => x.Status == OrderStatus.已预约),
            ["已支付"] = orders.Count(x => x.Status == OrderStatus.已支付),
            ["已导入"] = orders.Count(x => x.Status == OrderStatus.已导入),
            ["已完成"] = orders.Count(x => x.Status == OrderStatus.已完成),
            ["已取消"] = orders.Count(x => x.Status == OrderStatus.已取消),
            ["已退费"] = orders.Count(x => x.Status == OrderStatus.已退费),
            ["总计"] = orders.Count
        };
    }

    /// <summary>
    /// 检查是否可以预约团检
    /// </summary>
    /// <param name="cardNo">证件号</param>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">单位体检次数</param>
    /// <returns></returns>
    [HttpGet("CheckCanBookTeamOrder")]
    [ActionPermission(ActionType.Query, "检查团检预约资格", "APP端团检业务")]
    public async Task<bool> CheckCanBookTeamOrder([FromQuery] string cardNo, [FromQuery] string companyCode, [FromQuery] int companyTimes)
    {
        // 检查是否已存在相同证件号的未完成订单
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付, OrderStatus.正在缴费, OrderStatus.已导入 };
        var existsOrder = await Context.Queryable<TeamOrder>()
            .Where(x => x.CardNo == cardNo && 
                       x.CompanyCode == companyCode && 
                       x.CompanyTimes == companyTimes &&
                       x.BeginTime.Date > DateTime.Now.Date && 
                       SqlFunc.ContainsArray(statusArray, x.Status))
            .FirstAsync();

        if (existsOrder != null)
        {
            Unify.SetError("该人员在此单位此次体检中已存在预约！");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取可取消的团检订单
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetCancellableTeamOrders")]
    [ActionPermission(ActionType.Query, "获取可取消的团检订单", "APP端团检业务")]
    public async Task<List<TeamOrderBriefOutput>> GetCancellableTeamOrders()
    {
        var statusArray = new OrderStatus[] { OrderStatus.已预约, OrderStatus.已支付 };
        
        return await Context.Queryable<TeamOrder>()
            .Where(x => x.MembersId == UserManager.UserId && 
                       SqlFunc.ContainsArray(statusArray, x.Status) &&
                       x.BeginTime.Date > DateTime.Now.Date)
            .Select<TeamOrderBriefOutput>()
            .ToListAsync();
    }

    /// <summary>
    /// 获取团检订单支付信息
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    [HttpGet("GetTeamOrderPaymentInfo")]
    [ActionPermission(ActionType.Query, "获取团检订单支付信息", "APP端团检业务")]
    public async Task<object> GetTeamOrderPaymentInfo([FromQuery] string orderId)
    {
        var order = await Context.Queryable<TeamOrder>()
            .Where(x => x.Id == orderId && x.MembersId == UserManager.UserId)
            .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在");
            return null;
        }

        if (order.Status != OrderStatus.已预约)
        {
            Unify.SetError("订单状态不正确，无法支付");
            return null;
        }

        return new
        {
            OrderId = order.Id,
            OrderNo = order.Id, // 可以生成更友好的订单号
            Amount = order.TotalPrice,
            Title = $"团检订单-{order.Name}",
            Description = $"{order.CompanyName} 第{order.CompanyTimes}次体检",
            CreateTime = order.CreateTime,
            ExpireTime = order.BeginTime.AddDays(-1) // 体检前一天过期
        };
    }

    /// <summary>
    /// 更新团检订单支付状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="paymentResult">支付结果</param>
    /// <returns></returns>
    [HttpPost("UpdateTeamOrderPaymentStatus")]
    [ActionPermission(ActionType.Button, "更新团检订单支付状态", "APP端团检业务")]
    public async Task<bool> UpdateTeamOrderPaymentStatus([FromQuery] string orderId, [FromBody] object paymentResult)
    {
        try
        {
            var order = await Context.Queryable<TeamOrder>()
                .Where(x => x.Id == orderId && x.MembersId == UserManager.UserId)
                .FirstAsync();

            if (order == null)
            {
                Unify.SetError("订单不存在");
                return false;
            }

            if (order.Status != OrderStatus.已预约)
            {
                Unify.SetError("订单状态不正确");
                return false;
            }

            // TODO: 验证支付结果
            // 这里应该调用支付接口验证支付结果的真实性

            order.Status = OrderStatus.已支付;
            order.EndTime = DateTime.Now;

            await Context.Updateable(order).ExecuteCommandAsync();
            return true;
        }
        catch (Exception ex)
        {
            Unify.SetError("更新支付状态失败：" + ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取团检订单二维码
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    [HttpGet("GetTeamOrderQRCode")]
    [ActionPermission(ActionType.Query, "获取团检订单二维码", "APP端团检业务")]
    public async Task<object> GetTeamOrderQRCode([FromQuery] string orderId)
    {
        var order = await Context.Queryable<TeamOrder>()
            .Where(x => x.Id == orderId && x.MembersId == UserManager.UserId)
            .FirstAsync();

        if (order == null)
        {
            Unify.SetError("订单不存在");
            return null;
        }

        if (order.Status != OrderStatus.已支付 && order.Status != OrderStatus.已导入)
        {
            Unify.SetError("订单状态不正确，无法生成二维码");
            return null;
        }

        // TODO: 生成二维码
        // 这里可以使用QRCoder或其他二维码库生成二维码

        return new
        {
            OrderId = order.Id,
            QRCodeData = order.Id, // 简单示例，实际应该是加密后的数据
            QRCodeUrl = $"/api/qrcode/{order.Id}", // 二维码图片URL
            ValidUntil = order.BeginTime.AddDays(1) // 二维码有效期
        };
    }
}
