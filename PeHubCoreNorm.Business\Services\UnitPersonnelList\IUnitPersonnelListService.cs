﻿using PeHubCoreNorm.Business.Input;
using System.Linq.Expressions;

namespace PeHubCoreNorm.Business;

public interface IUnitPersonnelListService: ITransient
{


    /// <summary>
    /// 新增单位人员名单
    /// </summary>
    /// <param name="unitPerson"></param>
    /// <returns></returns>
    Task<UnitPersonnelList> CreateUnitPersons(UnitPersonnelList unitPerson);


    /// <summary>
    /// 批量新增单位人员名单
    /// </summary>
    /// <param name="unitPersons"></param>
    /// <returns></returns>
    Task<int> BatchCreateUnitPersons(List<UnitPersonnelList> unitPersons);


    /// <summary>
    /// 查询单位人员名单
    /// </summary>
    /// <param name="unitPerson"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<UnitPersonnelList>> GetUnitPersons(QueryUnitPersonInput query);


    /// <summary>
    /// 根据查询条件，批量更新某个字段
    /// </summary>
    /// <param name="whereExpression"></param>
    /// <param name="updateExpression"></param>
    /// <returns></returns>
    Task<int> ActivationUnitPersons(Expression<Func<UnitPersonnelList, bool>> whereExpression, Expression<Func<UnitPersonnelList, UnitPersonnelList>> updateExpression);

    /// <summary>
    /// 删除未激活的人员
    /// </summary>
    /// <param name="whereExpression"></param>
    /// <returns></returns>
    Task<int> DelUnitPersons(Expression<Func<UnitPersonnelList, bool>> whereExpression);


}

