﻿using PeHubCoreNorm.System;
using PeHubCoreNorm.Utils;
using System.Diagnostics;

namespace PeHubCoreNorm.RBAC;

public class AuthService : IAuthService
{
    private readonly ICacheService _cacheService;
    private readonly ICapPublisher _capPublisher;
    private readonly IConfigService _configService;
    private readonly ISysMenuService _sysMenuService;
    private readonly ISysUserService _sysUserService;
	private readonly ISysOrgService _sysOrgService;

	public AuthService(ISysMenuService sysMenuService, ISysUserService sysUserService,
        ICacheService cacheService, IConfigService configService,
        ICapPublisher capPublisher, ISysOrgService sysOrgService)
    {
        _sysUserService = sysUserService;
        _sysMenuService = sysMenuService;
        _cacheService = cacheService;
        _capPublisher = capPublisher;
        _configService = configService;
        _sysOrgService = sysOrgService;

	}

    public async Task<LoginUserOutput> GetLoginUser()
    {
        var user = await _sysUserService.GetSysUserById(UserManager.UserId); //根据账号获取用户信息
        if (user != null)
        {
            var output = user.Adapt<LoginUserOutput>();
            output.ButtonCodeList = await _sysUserService.OwnButtonCodeList();
            output.RoleCodeList = await _sysUserService.OwnRoleCode();
            output.ModuleList = await _sysMenuService.GetOwnMenus(UserManager.UserId);
            return output;
        }

        return null;
    }

    public bool CheckTokenInRedis(string token)
    {
        if (string.IsNullOrWhiteSpace(UserManager.UserId) || string.IsNullOrWhiteSpace(UserManager.Device))
            return false;
        var key = CacheConst.Cache_UserToken + UserManager.UserId + ":" + UserManager.Device;
        var redisToken = _cacheService.Get<string>(key);
        return token == redisToken;
    }

    public async Task<LoginOutPut> Login(LoginInput loginInput)
    {
		await CheckCaptcha(loginInput); //检查验证码
        await CheckWebOpen(loginInput); //检查网站是否开启	 
        //获取登录策略
        var loginPolicy = await _configService.GetListByCategory(CateGoryConst.Config_LOGIN_POLICY);
        BeforeLogin(loginPolicy, loginInput); //登录前校验

        var user = await _sysUserService.GetSysUserByAccount(loginInput.Account);

        if (user == null)
            return Unify.SetError("用户名或密码错误,用户名:{0}", loginInput.Account);

        if (user.status != "Y")
            return Unify.SetError("用户名已经被禁用");

        var password = CryptogramUtil.Sm2Decrypt(loginInput.Password); // 使用特定解密，需要kr补充

        if ((CryptogramUtil.Sm2Decrypt(user.Password) ==password && !string.IsNullOrEmpty(password)))
        {
            //返回结果 上次登录为空,或者距离上次登录已经180天了,则需要修改密码
            var pwdPolicy = await _configService.GetListByCategory(CateGoryConst.Config_PWD_POLICY);


            var remind = pwdPolicy.FirstOrDefault(x => x.ConfigKey == PeHubCoreNormConst.PWD_REMIND)?.ConfigValue
                .ToBoolean(); //是否提醒
            var remindday = pwdPolicy.FirstOrDefault(x => x.ConfigKey == PeHubCoreNormConst.PWD_REMIND_DAY)?.ConfigValue
                .ToInt(180); //提醒时间

            if (remind == null && (user.LastLoginTime == null ||
                                   Math.Abs((DateTime.Now - user.LastLoginTime.Value).TotalDays) >= (remindday ?? 180)))
                return Unify.SetError("上次登录已经{0}天了,需要重新修改密码", remindday);

			var orgList = await _sysOrgService.GetOrgList(user.Id, user.SuperAdmin == PeHubCoreNormConst.STATUS_ENABLE);

			//引入设备概念,每次登陆都重新颁发Token
			var token = JwtUtils.GenerateToken(new Dictionary<string, string>
            {
                [ClaimConst.UserId] = user.Id,
                [ClaimConst.Account] = user.Account,
                [ClaimConst.Name] = user.Name,
                [ClaimConst.SuperAdmin] = user.SuperAdmin,
                [ClaimConst.Device] = loginInput.Device,
                [ClaimConst.OrgList] = string.Join(",", orgList.Select(x=>x.OrgCode))
			});

            //该功能结合 actionfilter 的 缓存使用
            //var key = CacheConst.Cache_UserToken + user.Id + ":" + loginInput.Device;
            //_cacheService.Set(key, token["accessToken"].ToString(), JwtUtils._jwtSettings.ExpirationMinutes * 60);

            _capPublisher.Publish("logined", user.Id);

            App.HttpContext.Response.Headers["access-token"] = token["accessToken"].ToString();
            App.HttpContext.Response.Headers["x-access-token"] = token["accessToken"].ToString();

			App.HttpContext.Response.Headers["Access-Control-Expose-Headers"] = "access-token,x-access-token";

			var modulelist = await _sysMenuService.GetOwnMenus(user.Id);

 

			return new LoginOutPut
            {
                Token = token["accessToken"].ToString(),
                Account = user.Account,
                Name = user.Name,
                DefaultModule = user.DefaultModule,
                ModuleList = modulelist,
				OrgList= orgList
            };
        }

        LoginError(loginPolicy, loginInput); //登录错误操作

        return Unify.SetError("用户名密码错误");
    }

    public Task LoginOut(LoginOutInput input)
    {
        // //该功能结合 actionfilter 的 缓存使用
        var key = CacheConst.Cache_UserToken + UserManager.UserId + ":" + input.Device;
        _cacheService.Remove(key);

        return Task.CompletedTask;
    }

	public async Task<PicValidCodeOutPut> GetCaptchaInfo()
    {
        var config =
            await _configService.GetByConfigKey(CateGoryConst.Config_LOGIN_POLICY,
                PeHubCoreNormConst.LOGIN_CAPTCHA_TYPE);
        var captchaType = (CaptchaType)Enum.Parse(typeof(CaptchaType), config.ConfigValue);
        //生成验证码
        var captchaInfo = CaptchaUtil.CreateCaptcha(captchaType, 4, 100, 38);

        //生成请求号，并将验证码放入缓存
        var reqNo = IDUtils.GetId();
        _cacheService.Set(CacheConst.Cache_Captcha + reqNo, captchaInfo.Code, TimeSpan.FromMinutes(5));
        //返回验证码和请求号
        return new PicValidCodeOutPut { ValidCodeBase64 = captchaInfo.Base64Str, ValidCodeReqNo = reqNo };
    }

    [CapSubscribe("logined")]
    public async Task LoginedAsync(string userId)
    {
        await _sysUserService.SetLogined(userId);
    }


    #region 登录验证方法

    /// <summary>
    ///     检查验证码
    /// </summary>
    /// <param name="input"></param>
    public async Task CheckCaptcha(LoginInput input)
    {
        //判断是否有验证码
        var sysBase =
            await _configService.GetByConfigKey(CateGoryConst.Config_LOGIN_POLICY,
                PeHubCoreNormConst.LOGIN_CAPTCHA_OPEN);
        if (sysBase != null) //如果有这个配置项
            if (sysBase.ConfigValue.ToBoolean()) //如果需要验证码
            {
                //如果没填验证码，提示验证码不能为空
                if (string.IsNullOrEmpty(input.validCode) || string.IsNullOrEmpty(input.ValidCodeReqNo))
                    throw new CustomException("验证码不能为空"); // Unify.SetError("验证码不能为空");

                ValidValidCode(input.validCode, input.ValidCodeReqNo); //校验验证码
            }
    }

    /// <summary>
    ///     检查网站是否开启
    /// </summary>
    /// <param name="input"></param>
    public async Task CheckWebOpen(LoginInput input)
    {
        //判断是否开启web访问
        var webStatus =
            await _configService.GetByConfigKey(CateGoryConst.Config_SYS_BASE, PeHubCoreNormConst.SYS_WEB_STATUS);
        if (webStatus != null && webStatus.ConfigValue == PeHubCoreNormConst.STATUS_DISABLED
                              && input.Account.ToLower() !=
                              PeHubCoreNormConst.SUPER_ADMIN_CODE.ToLower()) //如果禁用了网站并且不是超级管理员
        {
            var closePrompt = await _configService.GetByConfigKey(CateGoryConst.Config_SYS_BASE,
                PeHubCoreNormConst.SYS_WEB_CLOSE_PROMPT);
            throw new CustomException(closePrompt.ConfigValue);
        }
    }

    /// <summary>
    ///     登录之前执行的方法
    /// </summary>
    /// <param name="loginPolicy"></param>
    /// <param name="input"></param>
    public void BeforeLogin(List<DevConfig> loginPolicy, LoginInput input)
    {
        var lockTime = loginPolicy.First(x => x.ConfigKey == PeHubCoreNormConst.LOGIN_ERROR_LOCK).ConfigValue
            .ToInt(); //获取锁定时间
        var errorCount = loginPolicy.First(x => x.ConfigKey == PeHubCoreNormConst.LOGIN_ERROR_COUNT).ConfigValue
            .ToInt(); //获取错误次数
        var key = PeHubCoreNormConst.CACHE_LOGIN_ERROR_COUNT; //获取登录错误次数Key值

        key += input.Account;

        var errorCountCache = _cacheService.Get<int>(key); //获取登录错误次数
        if (errorCountCache >= errorCount)
        {
            _cacheService.SetExpire(key, TimeSpan.FromMinutes(lockTime)); //设置缓存
            throw new CustomException($"密码错误次数过多，请{lockTime}分钟后再试");
        }
    }

    /// <summary>
    ///     登录错误操作
    /// </summary>
    /// <param name="loginPolicy"></param>
    /// <param name="input"></param>
    /// <param name="isTenant"></param>
    public void LoginError(List<DevConfig> loginPolicy, LoginInput input)
    {
        var resetTime = loginPolicy.First(x => x.ConfigKey == PeHubCoreNormConst.LOGIN_ERROR_RESET_TIME).ConfigValue
            .ToInt(); //获取重置时间
        var key = PeHubCoreNormConst.CACHE_LOGIN_ERROR_COUNT; //获取登录错误次数Key值

        key += input.Account;

        _cacheService.Increment(key, 1); // 登录错误次数+1
        _cacheService.SetExpire(key, TimeSpan.FromMinutes(resetTime)); //设置过期时间
    }

    /// <summary>
    ///     校验验证码方法
    /// </summary>
    /// <param name="validCode">验证码</param>
    /// <param name="validCodeReqNo">请求号</param>
    /// <param name="isDelete">是否从Redis删除</param>
    public void ValidValidCode(string validCode, string validCodeReqNo, bool isDelete = true)
    {
        var key = CacheConst.Cache_Captcha + validCodeReqNo; //获取验证码Key值
        var code = _cacheService.Get<string>(key); //从缓存拿数据
        if (isDelete) RemoveValidCodeFromRedis(validCodeReqNo); //如果需要删除验证码
        if (code != null && validCode != null) //如果有
        {
            //验证码如果不匹配直接抛错误，这里忽略大小写
            if (validCode.ToLower() != code.ToLower()) throw new CustomException("验证码错误");
        }
        else
        {
            //抛出验证码不能为空
            throw new CustomException("验证码不能为空");
        }
    }

    /// <summary>
    ///     从缓存中删除验证码
    /// </summary>
    /// <param name="validCodeReqNo"></param>
    public void RemoveValidCodeFromRedis(string validCodeReqNo)
    {
        var key = CacheConst.Cache_Captcha + validCodeReqNo; //获取验证码Key值
        _cacheService.Remove(key); //删除验证码
    }

    #endregion
}