﻿using Masuit.Tools.Models;
using SqlSugar;

namespace PeHubCoreNorm;

/// <summary>
///     Sqlsugar分页拓展类
/// </summary>
public static class SqlSugarPageExtension
{
    /// <summary>
    ///     SqlSugar分页扩展
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <param name="queryable"></param>
    /// <param name="current"></param>
    /// <param name="size"></param>
    /// <returns></returns>
    public static SqlSugarPagedList<TEntity> ToPagedList<TEntity>(this ISugarQueryable<TEntity> queryable, int pageNum,
        int size)
    {
        var total = 0;
        var lists = queryable.ToPageList(pageNum, size, ref total);
        var pages = (int)Math.Ceiling(total / (double)size);
        return new SqlSugarPagedList<TEntity>
        {
            pageNum = pageNum,
            pageSize = size,
            list = lists,
            total = total,
            pages = pages,
            HasNextPages = pageNum < pages,
            HasPrevPages = pageNum - 1 > 0
        };
    }

    /// <summary>
    ///     SqlSugar分页扩展
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <param name="queryable"></param>
    /// <param name="current"></param>
    /// <param name="size"></param>
    /// <returns></returns>
    public static async Task<SqlSugarPagedList<TEntity>> ToPagedListAsync<TEntity>(
        this ISugarQueryable<TEntity> queryable,
        int pageNum, int size)
    {
        RefAsync<int> totalCount = 0;
        var lists = await queryable.ToPageListAsync(pageNum, size, totalCount);
        var totalPages = (int)Math.Ceiling(totalCount / (double)size);
        return new SqlSugarPagedList<TEntity>
        {
            pageNum = pageNum,
            pageSize = size,
            list = lists,
            total = (int)totalCount,
            pages = totalPages,
            HasNextPages = pageNum < totalPages,
            HasPrevPages = pageNum - 1 > 0
        };
    }

    /// <summary>
    ///     SqlSugar分页扩展
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="queryable"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <param name="expression"></param>
    /// <returns></returns>
    public static SqlSugarPagedList<TResult> ToPagedList<TEntity, TResult>(this ISugarQueryable<TEntity> queryable,
        int pageNum,
        int pageSize, Expression<Func<TEntity, TResult>> expression)
    {
        var totalCount = 0;
        var lists = queryable.ToPageList(pageNum, pageSize, ref totalCount, expression);
        var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        return new SqlSugarPagedList<TResult>
        {
            pageNum = pageNum,
            pageSize = pageSize,
            list = lists,
            total = totalCount,
            pages = totalPages,
            HasNextPages = pageNum < totalPages,
            HasPrevPages = pageNum - 1 > 0
        };
    }

    /// <summary>
    ///     SqlSugar分页扩展
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="queryable"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <param name="expression"></param>
    /// <returns></returns>
    public static async Task<SqlSugarPagedList<TResult>> ToPagedListAsync<TEntity, TResult>(
        this ISugarQueryable<TEntity> queryable, int pageNum, int pageSize,
        Expression<Func<TEntity, TResult>> expression)
    {
        RefAsync<int> totalCount = 0;
        var lists = await queryable.ToPageListAsync(pageNum, pageSize, totalCount, expression);
        var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        return new SqlSugarPagedList<TResult>
        {
            pageNum = pageNum,
            pageSize = pageSize,
            list = lists,
            total = totalCount,
            pages = totalPages,
            HasNextPages = pageNum < totalPages,
            HasPrevPages = pageNum - 1 > 0
        };
    }

    /// <summary>
    ///     分页查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list">数据列表</param>
    /// <param name="pageIndex">当前页</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>分页集合</returns>
    public static LinqPagedList<T> LinqPagedList<T>(this List<T> list, int pageIndex, int pageSize)
    {
        var lists = list.ToPagedList(pageIndex, pageSize); //获取分页
        //格式化
        return new LinqPagedList<T>
        {
            pageNum = pageIndex,
            pageSize = pageSize,
            list = lists.Data,
            total = lists.TotalCount,
            pages = lists.TotalPages,
            HasNextPages = pageIndex < lists.TotalPages,
            HasPrevPages = pageIndex - 1 > 0
        };
    }
}

/// <summary>
///     SqlSugar 分页泛型集合
/// </summary>
/// <typeparam name="TEntity"></typeparam>
public class SqlSugarPagedList<TEntity>
{
    /// <summary>
    ///     页码
    /// </summary>
    public int pageNum { get; set; }

    /// <summary>
    ///     数量
    /// </summary>
    public int pageSize { get; set; }

    /// <summary>
    ///     总条数
    /// </summary>
    public int total { get; set; }

    /// <summary>
    ///     总页数
    /// </summary>
    public int pages { get; set; }

    /// <summary>
    ///     当前页集合
    /// </summary>
    public IEnumerable<TEntity> list { get; set; }

    /// <summary>
    ///     是否有上一页
    /// </summary>
    public bool HasPrevPages { get; set; }

    /// <summary>
    ///     是否有下一页
    /// </summary>
    public bool HasNextPages { get; set; }
}

/// <summary>
///     Linq分页泛型集合
/// </summary>
/// <typeparam name="T"></typeparam>
public class LinqPagedList<T> : SqlSugarPagedList<T>
{
}