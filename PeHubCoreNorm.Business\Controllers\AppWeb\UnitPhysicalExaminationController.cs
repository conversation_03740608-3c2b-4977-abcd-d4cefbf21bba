using Microsoft.AspNetCore.Authorization;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using PeHubCoreNorm.Business.Services.UnitPhysicalExamination;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Controllers.AppWeb
{

    [ApiExplorerSettings(GroupName = "AppWeb")]
    [Route("/AppWeb/[controller]")]
    [AllowAnonymous]
    public class UnitPhysicalExaminationController : BaseControllerAuthorize
    {
        private readonly IUnitPersonnelListService _unitPersonnelListService;
        private readonly IUnitTitlePageService _unitTitlePageService;
        private readonly IUnitPhysicalExaminationService _unitPhysicalExaminationService;
        public UnitPhysicalExaminationController(IUnitPersonnelListService unitPersonnelListService, IUnitTitlePageService unitTitlePageService,IUnitPhysicalExaminationService unitPhysicalExaminationService)
        {
            _unitPersonnelListService = unitPersonnelListService;
            _unitTitlePageService = unitTitlePageService;
            _unitPhysicalExaminationService= unitPhysicalExaminationService;
        }

        /// <summary>
        /// 验证单位人员信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("VerifyPersonInfo")]
        [ActionPermission(ActionType.Button, "VerifyPersonInfo", "APP端用户业务")]
        public async Task<List<UnitPersonnelList>> VerifyPersonInfo([FromBody] AuthenticationToUntiInput query)
        {
            return await _unitPersonnelListService.VerifyPersonInfo(query);
        }

        /// <summary>
        /// 根据单位编码和体检次数获取单位封面
        /// </summary>
        /// <param name="companyCode">单位编码</param>
        /// <param name="companyTimes">体检次数</param>
        /// <returns></returns>
        [HttpGet("GetUnitTitlePageByCompany")]
        [ActionPermission(ActionType.Query, "单位封面查询", "单位封面管理")]
        public async Task<UnitTitlePageOutput> GetUnitTitlePageByCompany([FromQuery] string companyCode, [FromQuery] int companyTimes)
        {
            return await _unitTitlePageService.GetUnitTitlePageByCompany(companyCode, companyTimes);
        }


        [HttpPost("GetPackageInfo")]
        [AllowAnonymous]
        public async Task<PackageDetailInfo> GetPackageInfo([FromBody] UnitPersonnelQueryInput input)
        {
            try
            {
                var personnel = await _unitPhysicalExaminationService.GetUnitPersonnelWithPackageInfo(input);
                return personnel?.PackageDetail;
            }
            catch (Exception ex)
            {
                Unify.SetError("获取套餐信息失败：" + ex.Message);
                return null;
            }
        }


    }
}
