namespace PeHubCoreNorm.Business;

/// <summary>
/// 团检订单输出
/// </summary>
public class TeamOrderOutput
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    public string MembersId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public string CardType { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 体检分类代码
    /// </summary>
    public string PeClsCode { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age => DateTime.Now.Year - Birthday.Year;

    /// <summary>
    /// 性别(1:男 2:女)
    /// </summary>
    public string Sex { get; set; }

    /// <summary>
    /// 性别描述
    /// </summary>
    public string SexText => Sex == "1" ? "男" : "女";

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string MarryStatus { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 体检流水号
    /// </summary>
    public string RegNo { get; set; }

    /// <summary>
    /// 订单总价格
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 时间段编码Id
    /// </summary>
    public string TimeId { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 客户体检时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 客户订单撤销时间
    /// </summary>
    public DateTime? CancelTime { get; set; }

    /// <summary>
    /// 客户订单下单时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 客户订单完成时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 是否为Vip
    /// </summary>
    public bool? IsVip { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus Status { get; set; }

    /// <summary>
    /// 订单状态描述
    /// </summary>
    public string StatusText => Status.ToString();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMsg { get; set; }

    /// <summary>
    /// 订单详情
    /// </summary>
    public List<TeamOrderDetailOutput> OrderDetails { get; set; } = new List<TeamOrderDetailOutput>();
}

/// <summary>
/// 团检订单明细输出
/// </summary>
public class TeamOrderDetailOutput
{
    /// <summary>
    /// 明细ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 订单ID
    /// </summary>
    public string OrderId { get; set; }

    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 组合类型(1:套餐 2:加项包)
    /// </summary>
    public int CombType { get; set; }

    /// <summary>
    /// 组合类型描述
    /// </summary>
    public string CombTypeText => CombType == 1 ? "套餐" : "加项包";

    /// <summary>
    /// 订单类型(1:个人 2:团体)
    /// </summary>
    public int OrderType { get; set; }

    /// <summary>
    /// 订单类型描述
    /// </summary>
    public string OrderTypeText => OrderType == 1 ? "个人" : "团体";
}

/// <summary>
/// 团检订单简要输出
/// </summary>
public class TeamOrderBriefOutput
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 订单总价格
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 客户体检时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus Status { get; set; }

    /// <summary>
    /// 订单状态描述
    /// </summary>
    public string StatusText => Status.ToString();

    /// <summary>
    /// 客户订单下单时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 是否为Vip
    /// </summary>
    public bool? IsVip { get; set; }
}

/// <summary>
/// 团检订单统计输出
/// </summary>
public class TeamOrderStatisticsOutput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 总订单数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已预约数量
    /// </summary>
    public int BookedCount { get; set; }

    /// <summary>
    /// 已支付数量
    /// </summary>
    public int PaidCount { get; set; }

    /// <summary>
    /// 已完成数量
    /// </summary>
    public int CompletedCount { get; set; }

    /// <summary>
    /// 已取消数量
    /// </summary>
    public int CancelledCount { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// 完成率
    /// </summary>
    public decimal CompletionRate => TotalCount > 0 ? (decimal)CompletedCount / TotalCount * 100 : 0;

    /// <summary>
    /// 支付率
    /// </summary>
    public decimal PaymentRate => TotalCount > 0 ? (decimal)PaidCount / TotalCount * 100 : 0;
}

/// <summary>
/// 批量操作结果输出
/// </summary>
public class BatchTeamOrderResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 失败详情
    /// </summary>
    public List<BatchFailureDetail> FailureDetails { get; set; } = new List<BatchFailureDetail>();

    /// <summary>
    /// 成功的订单ID列表
    /// </summary>
    public List<string> SuccessOrderIds { get; set; } = new List<string>();
}

/// <summary>
/// 批量操作失败详情
/// </summary>
public class BatchFailureDetail
{
    /// <summary>
    /// 索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string ErrorMessage { get; set; }
}
