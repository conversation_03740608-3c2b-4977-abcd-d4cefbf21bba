﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 重点项目号源信息表
    /// </summary>
    [SugarTable("NS_KeyProjectSource", TableDescription = "重点项目号源信息表")]
    public class NS_KeyProjectSource : NS_BaseEntry
    {
        /// <summary>
        /// 重点项目编码
        /// </summary>
        [SugarColumn(ColumnName = "CodeItemCode", ColumnDescription = "重点项目编码")]
        public virtual string CodeItemCode { get; set; }
    }
}
