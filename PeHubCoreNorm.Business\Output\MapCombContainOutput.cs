﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合包含关系返参
/// </summary>
public class MapCombContainOutput
{
    /// <summary>
    /// 组合代码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 子组合展示
    /// </summary>
    public string ChildCombs { get; set; }
}

/// <summary>
/// 组合包含关系明细返参
/// </summary>
public class CombContainDetailOutput
{
    /// <summary>
    /// 组合代码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 子组合编码
    /// </summary>
    public string ChildCombCode { get; set; }

    /// <summary>
    /// 子组合名称
    /// </summary>
    public string ChildCombName { get; set; }
}