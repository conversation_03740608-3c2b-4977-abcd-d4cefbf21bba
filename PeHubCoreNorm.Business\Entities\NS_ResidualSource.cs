﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 补检号源信息表
    /// </summary>
    [SugarTable("NS_ResidualSource", TableDescription = "补检号源信息表")]
    public class NS_ResidualSource : NS_BaseEntry
    {
        /// <summary>
        /// 是否启用补检号源
        /// </summary>
        [SugarColumn(ColumnName = "IsEnabled", ColumnDescription = "是否启用补检号源")]
        public virtual string IsEnabled { get; set; }

        /// <summary>
        /// 补检号源生效开始日期
        /// </summary>
        [SugarColumn(ColumnName = "StartDate", ColumnDescription = "补检号源生效开始日期")]
        public virtual DateTime StartDate { get; set; }

        /// <summary>
        /// 补检号源生效结束日期
        /// </summary>
        [SugarColumn(ColumnName = "EndDate", ColumnDescription = "补检号源生效结束日期")]
        public virtual DateTime EndDate { get; set; }                
    }
}
