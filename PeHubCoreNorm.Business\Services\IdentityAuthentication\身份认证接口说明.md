# 身份认证接口说明

## 接口概述

身份认证接口用于通过证件号码、电话号码以及可选的短信验证码进行身份验证，验证用户是否为已激活的人员，并返回符合身份的信息列表。

## 核心功能

1. **身份认证** - 通过证件号码和电话号码验证身份
2. **短信验证** - 发送和验证短信验证码
3. **人员查找** - 查找已激活的人员信息
4. **令牌管理** - 生成和验证认证令牌

## 接口详情

### 1. 身份认证接口

**前端接口**: `POST /appweb/IdentityAuthentication/AuthenticateIdentity`  
**管理端接口**: `POST /admin/IdentityAuthentication/AuthenticateIdentity`

**请求参数**: `IdentityAuthenticationInput`

```json
{
  "idNumber": "身份证号码",           // 必填
  "tel": "手机号码",                // 必填，格式：1[3-9]xxxxxxxxx
  "smsCode": "短信验证码",          // 可选，6位数字
  "companyCode": "单位编码",        // 可选，用于精确匹配
  "batchNumber": 1                  // 可选，体检次数
}
```

**返回结果**: `IdentityAuthenticationOutput`

```json
{
  "isAuthenticated": true,          // 认证是否成功
  "message": "身份认证成功",        // 认证消息
  "authToken": "认证令牌",          // 认证令牌
  "personnelList": [               // 符合身份的人员信息列表
    {
      "id": "人员ID",
      "employeeName": "员工姓名",
      "companyCode": "单位编码",
      "companyName": "单位名称",
      "department": "部门",
      "idNumber": "证件号码",
      "idNumberType": 1,
      "tel": "电话号码",
      "sex": 1,
      "age": 30,
      "packAgeCode": "套餐编码",
      "packAgeName": "套餐名称",
      "batchNumber": 1,
      "startTime": "2024-01-01T00:00:00",
      "endTime": "2024-12-31T23:59:59",
      "status": 1,
      "statusText": "已激活",
      "isValid": true
    }
  ]
}
```

### 2. 发送短信验证码接口

**前端接口**: `POST /appweb/IdentityAuthentication/SendSmsCode`  
**管理端接口**: `POST /admin/IdentityAuthentication/SendSmsCode`

**请求参数**: `SendSmsCodeInput`

```json
{
  "tel": "手机号码",               // 必填
  "idNumber": "身份证号码"         // 必填，用于验证身份
}
```

**返回结果**: `SendSmsCodeOutput`

```json
{
  "success": true,                 // 是否发送成功
  "message": "验证码发送成功",     // 消息
  "validMinutes": 5               // 验证码有效期（分钟）
}
```

### 3. 验证短信验证码接口

**前端接口**: `GET /appweb/IdentityAuthentication/VerifySmsCode`  
**管理端接口**: `GET /admin/IdentityAuthentication/VerifySmsCode`

**请求参数**:
- `tel`: 电话号码
- `code`: 验证码

**返回结果**: `bool` - 验证是否成功

### 4. 查找已激活人员接口

**前端接口**: `GET /appweb/IdentityAuthentication/FindActivePersonnel`  
**管理端接口**: `GET /admin/IdentityAuthentication/FindActivePersonnel`

**请求参数**:
- `idNumber`: 证件号码（必填）
- `tel`: 电话号码（必填）
- `companyCode`: 单位编码（可选）
- `batchNumber`: 体检次数（可选）

**返回结果**: `List<PersonnelInfo>` - 人员信息列表

### 5. 验证认证令牌接口

**前端接口**: `GET /appweb/IdentityAuthentication/ValidateAuthToken`  
**管理端接口**: `GET /admin/IdentityAuthentication/ValidateAuthToken`

**请求参数**:
- `token`: 认证令牌

**返回结果**: `PersonnelInfo` - 人员信息

## 业务逻辑

### 1. 身份认证流程

1. **基础验证**: 验证证件号码和电话号码格式
2. **短信验证**: 如果提供短信验证码，先验证验证码
3. **人员查找**: 在已激活人员中查找匹配的记录
4. **结果返回**: 返回认证结果和人员信息列表
5. **令牌生成**: 认证成功后生成认证令牌

### 2. 短信验证码机制

1. **身份预验证**: 发送前验证证件号码和电话是否匹配
2. **频率限制**: 同一号码1分钟内只能发送一次
3. **验证码生成**: 生成6位随机数字验证码
4. **有效期管理**: 验证码5分钟内有效
5. **记录保存**: 验证码记录保存到数据库

### 3. 认证条件

- **必须是已激活人员**: `Status = 1`
- **证件号码匹配**: `IdNumber` 完全匹配
- **电话号码匹配**: `Tel` 完全匹配
- **可选精确匹配**: 可通过单位编码和体检次数进一步筛选

## 使用示例

### JavaScript 调用示例

```javascript
// 1. 发送短信验证码
async function sendSmsCode() {
    const response = await fetch('/appweb/IdentityAuthentication/SendSmsCode', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            tel: '13800138000',
            idNumber: '110101199001011234'
        })
    });
    
    const result = await response.json();
    if (result.data.success) {
        console.log('验证码发送成功');
    }
}

// 2. 身份认证
async function authenticateIdentity() {
    const response = await fetch('/appweb/IdentityAuthentication/AuthenticateIdentity', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            idNumber: '110101199001011234',
            tel: '13800138000',
            smsCode: '123456'
        })
    });
    
    const result = await response.json();
    if (result.data.isAuthenticated) {
        console.log('认证成功', result.data.personnelList);
        // 保存认证令牌
        localStorage.setItem('authToken', result.data.authToken);
    }
}

// 3. 验证认证令牌
async function validateToken() {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`/appweb/IdentityAuthentication/ValidateAuthToken?token=${token}`);
    const result = await response.json();
    
    if (result.data) {
        console.log('令牌有效', result.data);
    } else {
        console.log('令牌无效或已过期');
    }
}
```

### C# 调用示例

```csharp
// 身份认证
var input = new IdentityAuthenticationInput
{
    IdNumber = "110101199001011234",
    Tel = "13800138000",
    SmsCode = "123456"
};

var result = await _identityAuthenticationService.AuthenticateIdentity(input);
if (result.IsAuthenticated)
{
    Console.WriteLine($"认证成功，找到 {result.PersonnelList.Count} 条人员信息");
}
```

## 安全特性

1. **频率限制**: 短信发送有频率限制，防止恶意刷取
2. **验证码过期**: 验证码5分钟自动过期
3. **令牌管理**: 认证令牌2小时有效期
4. **日志记录**: 详细的操作日志记录
5. **异常处理**: 完善的异常处理机制

## 错误码说明

- `短信验证码错误或已过期`
- `未找到符合条件的已激活人员信息`
- `证件号码和电话号码不匹配或不存在相关人员信息`
- `发送过于频繁，请1分钟后再试`
- `验证码发送失败，请稍后重试`
- `身份认证失败，请稍后重试`

## 注意事项

1. **数据安全**: 证件号码等敏感信息需要妥善保护
2. **缓存管理**: 验证码和令牌使用Redis缓存管理
3. **短信成本**: 短信发送有成本，需要合理控制发送频率
4. **权限控制**: 管理端接口需要相应权限，前端接口允许匿名访问
5. **日志监控**: 建议监控认证失败次数，防止恶意攻击
