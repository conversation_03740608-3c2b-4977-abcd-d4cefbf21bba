﻿using AngleSharp.Text;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Utils.IOUtils
{
    public static class FileUploadUtils
    {


        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static async Task<FileUploadResponse> UploadFileAsync(FileUploadRequest request)
        {
            var response = new FileUploadResponse();

            try
            {
                // 1. 基本验证
                if (request.File == null || request.File.Length == 0)
                {
                    response.Success = false;
                    response.Message = "未接收到文件或文件为空";
                    return response;
                }

                // 2. 验证文件大小
                if (request.File.Length > request.MaxFileSize)
                {
                    response.Success = false;
                    response.Message = $"文件大小超过限制（最大允许 {request.MaxFileSize / 1024 / 1024}MB）";
                    return response;
                }

                // 3. 验证文件类型
                var fileExtension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                if (request.AllowedExtensions != null &&
                    request.AllowedExtensions.Length > 0 &&
                    !request.AllowedExtensions.Contains(fileExtension))
                {
                    response.Success = false;
                    response.Message = $"不支持的文件类型，允许的类型: {string.Join(", ", request.AllowedExtensions)}";
                    return response;
                }

                // 4. 准备存储路径
                var uploadsRoot = Path.Combine(request.SaveFilePath, "Uploads");

                // 如果有子目录，添加到路径中
                var storagePath = string.IsNullOrEmpty(request.SubDirectory)
                    ? uploadsRoot
                    : Path.Combine(uploadsRoot, request.SubDirectory);

                // 确保目录存在
                Directory.CreateDirectory(storagePath);

                // 5. 生成唯一文件名
                var fileName = string.IsNullOrEmpty(request.CustomFileName)
                    ? $"{Guid.NewGuid()}{fileExtension}" // 使用GUID防止重名
                    : $"{request.CustomFileName}{fileExtension}";

                var filePath = Path.Combine(storagePath, fileName);

                // 6. 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await request.File.CopyToAsync(stream);
                }

                // 7. 返回成功响应
                response.Success = true;
                response.Message = "文件上传成功";
                response.FilePath = filePath;
                response.FileName = fileName;
                response.FileSize = request.File.Length;
                response.VirtualPath= "/nas/" + (string.IsNullOrEmpty(request.SubDirectory)?"":(request.SubDirectory+"/"))+
					fileName; // 返回虚拟路径，便于前端访问


				return response;
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"文件上传失败: {ex.Message}";
                return response;
            }
        }
    }
}
