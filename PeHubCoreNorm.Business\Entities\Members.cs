﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 用户表Member
/// </summary>
[SugarTable(TableName = "Members", TableDescription = "用户表")]
public class Members : PrimaryKeyEntity
{
    /// <summary>
    /// 证件类型
    /// </summary>
    //[SugarColumn(IsNullable = false, Length = 20)]
    //public string CardType { get; set; }

    /// <summary>
    /// 证件号
    ///// </summary>
    //[SugarColumn(IsNullable = false, Length = 50)]
    //public string IdCard { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Tel { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string CardNo { get; set; }

    /// <summary>
    /// 手机号+证件号加密的 hex码
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string WId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", IsOnlyIgnoreUpdate = true, IsNullable = false)]
    public virtual DateTime? CreateDate { get; set; }

}
