namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位封面输出
/// </summary>
public class UnitTitlePageOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 体检有效期限
    /// </summary>
    public string ValidityPeriod { get; set; }

    /// <summary>
    /// 须知
    /// </summary>
    public string Notes { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 封面图片数据
    /// </summary>
    public byte[] ImgData { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public int FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateDate { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreateUserName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? ModifyDate { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string ModifyUserName { get; set; }
}

/// <summary>
/// 单位封面简要输出（不包含图片数据）
/// </summary>
public class UnitTitlePageBriefOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 体检有效期限
    /// </summary>
    public string ValidityPeriod { get; set; }

    /// <summary>
    /// 须知
    /// </summary>
    public string Notes { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    public int FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateDate { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreateUserName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? ModifyDate { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string ModifyUserName { get; set; }
}