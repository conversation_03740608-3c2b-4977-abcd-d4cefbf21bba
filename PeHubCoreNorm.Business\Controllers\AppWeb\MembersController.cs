﻿using Microsoft.AspNetCore.Authorization;

namespace PeHubCoreNorm.Business.AppWeb;

/// <summary>
/// 客户信息管理控制器
/// </summary>
[ApiExplorerSettings(GroupName = "AppWeb")]
[Route("/AppWeb/[controller]")]
public class MembersController : BaseControllerAuthorize
{
    private readonly IMembersService _membersService;

    public MembersController(IMembersService membersService)
    {
        _membersService = membersService;
    }

	/// <summary>
	///     登陆
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[AllowAnonymous]
	[HttpPost("authlogin")]
	[ActionPermission(ActionType.Button, "authlogin", "APP端用户业务")] //LoginIn用于日志分类
	public async Task<dynamic> authlogin(AuthFormInput input)
	{
		return await _membersService.authlogin(input);
	}


	/// <summary>
	///     获取图片验证码
	/// </summary>
	/// <returns></returns>
	[HttpGet("getAuthCaptcha")]
	[AllowAnonymous]
	[IgnoreLog]
	public async Task<dynamic> getAuthCaptcha()
	{
		return await _membersService.GetCaptchaInfo();
	}

	/// <summary>
	///   获取用户信息
	/// </summary>
	/// <returns></returns>
	[HttpGet("getMemberInfo")]
	[ActionPermission(ActionType.Query, "登录时获取用户信息", "APP端用户业务")]
	public async Task<dynamic> getMemberInfo()
	{
		return null;
		//return await _membersService.getMemberInfo();
	}


	/// <summary>
	///   获取用户信息
	/// </summary>
	/// <returns></returns>
	[HttpGet("getMemberbyWId")]
	[ActionPermission(ActionType.Query, "wid获取用户信息", "APP端用户业务")]
	[AllowAnonymous]
	public async Task<dynamic> getMemberbyWId([FromQuery] MembersWIdInput input)
	{
		return await _membersService.GetMembersByWId(input.wId,input.orgKey);
	}


    /// <summary>
	/// 获取用户id绑定的卡
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("GetCardsById")]
    [ActionPermission(ActionType.Query, "获取用户id绑定的卡")]
    public async Task<dynamic> GetCardsById()
    {
        return (await _membersService.GetCardsById(UserManager.UserId)).Select(card => new {
			card.Id,
			card.CardType,
			card.CardNo,
			card.Name,
			card.Tel,
			card.Sex,
			card.BirthDate,
			card.Marital
		}).ToList();
	}

    /// <summary>
	/// 建卡（默认绑定用户id）
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("CreateCards")]
    [ActionPermission(ActionType.Button, "建卡（默认绑定用户id）")]
    public async Task<dynamic> CreateCards(CardsInput input)
    {
		CardList card = new CardList()
		{
			CardType=input.cardType,
			CardNo=input.cardNo,
			Tel=input.tel,
            Name=input.name,
			BirthDate=input.birthDate,
			Sex=input.sex,
			Marital=input.marital,
        };
		//判断用户是否已超出绑卡次数
		var cards = await _membersService.GetCardsById(UserManager.UserId);
		if (cards.Count > 5)
		{
			throw new Exception("绑定的卡不能超出5张。");
        }
        //判断是否已绑定有该信息的卡
        var item=cards.FirstOrDefault(x => x.CardNo == card.CardNo && x.Name == card.Name && x.Tel == card.Tel);
		if(item != null)
		{
            throw new Exception("请勿重复绑定！");
        }
		//查询该信息的卡是否存在，不存在则新建卡
		var card_id = await _membersService.InserCardsReturnId(card);

		//绑卡
		return _membersService.MembersBingsCards(UserManager.UserId, card_id);
    }

    /// <summary>
	/// 用户id解绑卡
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	[HttpPost("UnbindTheCardById")]
    [ActionPermission(ActionType.Button, "用户id解绑卡")]
    public async Task<dynamic> UnbindTheCardById(UnbindTheCardInput input)
    {
		return await _membersService.UnbindTheCard(UserManager.UserId, input.cardId);
    }
}
