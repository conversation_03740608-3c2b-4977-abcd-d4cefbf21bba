﻿using PeHubCoreNorm;

namespace PerHubCoreNorm.SendSMS;

public interface ISendSmsService : ITransient
{
    Task<SendSmsResult> SendSmsAsync(SendSmsRequest request);

    /// <summary>
    ///     发送短信批量
    /// </summary>
    /// <param name="phoneNumbers"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    Task SendSmsBatchAsync(List<string> phoneNumbers, string message);
}