﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Utils.IOUtils
{
    public class FileUploadRequest
    {
        /// <summary>
        /// 允许的文件扩展名（如：.jpg,.png,.pdf）
        /// </summary>
        public string[] AllowedExtensions { get; set; }

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public long MaxFileSize { get; set; }

        /// <summary>
        /// 上传的文件
        /// </summary>
        public IFormFile File { get; set; }

        /// <summary>
        /// 自定义文件名（可选，不传则使用原始文件名）
        /// </summary>
        public string CustomFileName { get; set; }


        /// <summary>
        /// 保存文件地址
        /// </summary>
        public string SaveFilePath { get; set; }

        /// <summary>
        /// 存储子目录（可选）
        /// </summary>
        public string SubDirectory { get; set; }
    }
}
