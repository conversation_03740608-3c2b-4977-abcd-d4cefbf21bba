﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐对应的组合
/// </summary>
public class ClusterClsCombs
{
    /// <summary>
    /// 分类代码
    /// </summary>
    public string ClsCode { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    public string ClsName { get; set; }

    /// <summary>
    /// 项目分类下的组合
    /// </summary>
    public ClsCombs[] CombData { get; set; }
}

/// <summary>
/// 分类下的组合
/// </summary>
public class ClsCombs
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}