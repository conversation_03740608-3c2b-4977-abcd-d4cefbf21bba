﻿namespace PeHubCoreNorm.RBAC;

/// <summary>
///  组织机构表
/// </summary>
[SugarTable("sys_org", TableDescription = "组织机构表")]
[Tenant(SqlsugarConst.DB_Default)]
public class SysOrg : BaseEntity
{
	/// <summary>
	/// 医院编码
	/// </summary>
	[SugarColumn(ColumnName = "OrgCode", ColumnDescription = "账号", Length = 200, IsNullable = false)]
	public string OrgCode { get; set; }

	/// <summary>
	/// 共建体名称
	/// </summary>
	[SugarColumn(ColumnName = "OrgName", ColumnDescription = "医院名称", Length = 200, IsNullable = false)]
	public string OrgName { get; set; }

	/// <summary>
	/// 院区编码
	/// </summary>
	[SugarColumn(ColumnName = "AreaCode", ColumnDescription = "院区编码", Length = 200, IsNullable = false)]
	public string AreaCode { get; set; }

	/// <summary>
	/// 院区名称
	/// </summary>
	[SugarColumn(ColumnName = "AreaName", ColumnDescription = "院区名称", Length = 200, IsNullable = false)]
	public string AreaName { get; set; }

	/// <summary>
	/// 头像
	/// </summary>
	[SugarColumn(ColumnName = "Avatar", ColumnDescription = "头像", ColumnDataType = StaticConfig.CodeFirst_BigString,
	IsNullable = true)]
	public virtual string Avatar { get; set; }


	/// <summary>
	/// 信息描述
	/// </summary>
	[SugarColumn(ColumnName = "Describe", ColumnDescription = "描述", Length = 1000, IsNullable = false)]
	public string Describe { get; set; }


	/// <summary>
	/// 地址
	/// </summary>
	[SugarColumn(ColumnName = "Address", ColumnDescription = "地址", Length = 500, IsNullable = false)]
	public string Address { get; set; }


	/// <summary>
	/// 电话标签
	/// </summary>
	[SugarColumn(ColumnName = "TelTag", ColumnDescription = "电话标签", Length = 100, IsNullable = false)]
	public string TelTag { get; set; }


	/// <summary>
	///     用户状态
	/// </summary>
	[SugarColumn(ColumnName = "Status", ColumnDescription = "状态", Length = 200, IsNullable = true)]
	public string Status { get; set; }



}