﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 订单缴退费记录表
    /// </summary>
    [SugarTable("PayRecord", TableDescription = "订单缴退费记录表")]
    public class PayRecord : BaseEntity
    {
        /// <summary>
        /// 订单编码
        /// </summary>
        [SugarColumn(ColumnName = "OrderID", ColumnDescription = "订单编码")]
        public virtual string OrderID { get; set; }

        /// <summary>
        /// 微信公众号唯一标识（openId）
        /// </summary>
        [SugarColumn(ColumnName = "OpenId", ColumnDescription = "微信公众号唯一标识（openId）")]
        public virtual string OpenId { get; set; }

        /// <summary>
        /// 第三方订单号
        /// </summary>
        [SugarColumn(ColumnName = "OutTradeNo", ColumnDescription = "第三方订单号")]
        public virtual string OutTradeNo { get; set; }

        /// <summary>
        /// 支付平台订单ID
        /// </summary>
        [SugarColumn(ColumnName = "TransactionId", ColumnDescription = "支付平台订单ID")]
        public virtual string TransactionId { get; set; }

        /// <summary>
        /// 订单价格
        /// </summary>
        [SugarColumn(ColumnName = "Price", ColumnDescription = "订单价格")]
        public virtual decimal Price { get; set; }

        /// <summary>
        /// 体检类型(个人、团体或扫码付)
        /// </summary>
        [SugarColumn(ColumnName = "Type", ColumnDescription = "体检类型(个人、团体或扫码付)")]
        public virtual string Type { get; set; }

        /// <summary>
        /// 缴费类型(支付或退费)
        /// </summary>
        [SugarColumn(ColumnName = "PayType", ColumnDescription = "缴费类型(支付或退费)")]
        public virtual string PayType { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [SugarColumn(ColumnName = "Statu", ColumnDescription = "订单状态", Length = 20)]
        public virtual string Statu { get; set; }
    }
}
