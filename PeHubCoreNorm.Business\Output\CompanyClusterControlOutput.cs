﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位套餐属性返参
/// </summary>
public class CompanyClusterControlOutput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 性别 0:通用 1:男 2:女
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 加项标识
    /// </summary>
    public bool IsAddItem { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// 加项包标识(1:显示加项包 2:不显示加项包)
    /// </summary>
    public bool IsShowAddPackage { get; set; }

    /// <summary>
    /// 加项金额额度
    /// </summary>
    public decimal AddItemPrice { get; set; }

    /// <summary>
    /// 显示所有项目标识(1:显示，2:不显示)
    /// </summary>
    public bool IsShowAllItems { get; set; }

    /// <summary>
    /// 超出自费标识(1:超出部分自费，2:超出就不能选)
    /// </summary>
    public bool IsExtraPay { get; set; }

    /// <summary>
    /// 套餐简介
    /// </summary>
    public string ClusterIntro { get; set; }

    /// <summary>
    /// 体检须知
    /// </summary>
    public string Notice { get; set; }
}