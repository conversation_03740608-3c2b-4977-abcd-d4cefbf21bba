﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 互斥组合
/// </summary>
[SugarTable(TableName = "CodeMutexComb", TableDescription = "互斥组合")]
public class CodeMutexComb : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "MutexCode", ColumnDescription = "互斥编码（同属一个互斥代码中的组合相斥）", Length = 15)]
    public string MutexCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "MutexName", ColumnDescription = "互斥名称", Length = 20)]
    public string MutexName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombName", ColumnDescription = "组合名称", Length = 200)]
    public string CombName { get; set; }
}