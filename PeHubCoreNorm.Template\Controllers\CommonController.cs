﻿using Microsoft.AspNetCore.Http;
using PeHubCoreNorm.Utils.IOUtils;
using System.IO;

namespace PeHubCoreNorm.Template;

/// <summary>
///     学生控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Template")]
[Route("/biz/student")]
[AllowAnonymous]
public class CommonController:BaseController
{



    [HttpPost("upload")]
    [ActionPermission(ActionType.Button, "文件上传")]
    public async Task<IActionResult> UploadFile(IFormFile file)
    {
        var request = new FileUploadRequest
        {
            File = file,
            AllowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx" },
            MaxFileSize = 10 * 1024 * 1024, // 10MB
            CustomFileName = "custom_" + DateTime.Now.ToString("yyyyMMddHHmmss"),
            SubDirectory = "UserDocuments"
        };

        FileUploadResponse result =await FileUploadUtils.UploadFileAsync(request);

        

        if (result.Success)
        {
            return Ok(new
            {
                result.FileName,
                FileSize = $"{result.FileSize / 1024} KB",
                RelativePath = Path.Combine("Uploads", "UserDocuments", result.FileName)
            });
        }

        return BadRequest(result.Message);
    }

   

    
}