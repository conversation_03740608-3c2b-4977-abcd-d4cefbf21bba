﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位套餐信息控制
/// </summary>
[SugarTable(TableName = "CompanyClusterControl", TableDescription = "单位套餐信息控制")]
public class CompanyClusterControl : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 8)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Gender", ColumnDescription = "性别 0:通用 1:男 2:女", Length = 2)]
    public string Gender { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsAddItem", ColumnDescription = "加项标识")]
    public bool IsAddItem { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "MinAge", ColumnDescription = "最小年龄")]
    public int MinAge { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "MaxAge", ColumnDescription = "最大年龄")]
    public int MaxAge { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsShowAddPackage", ColumnDescription = "加项包标识(1:显示加项包 2:不显示加项包)")]
    public bool IsShowAddPackage { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "AddItemPrice", ColumnDescription = "加项金额额度", Length = 8, DecimalDigits = 2)]
    public decimal AddItemPrice { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsShowAllItems", ColumnDescription = "显示所有项目标识(1:显示，2:不显示)")]
    public bool IsShowAllItems { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsExtraPay", ColumnDescription = "超出自费标识(1:超出部分自费，2:超出就不能选)")]
    public bool IsExtraPay { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "ClusterIntro", ColumnDescription = "套餐简介", Length = 255)]
    public string ClusterIntro { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Notice", ColumnDescription = "体检须知", Length = 255)]
    public string Notice { get; set; }
}
