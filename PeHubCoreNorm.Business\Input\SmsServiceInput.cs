namespace PeHubCoreNorm.Business.Input;

/// <summary>
/// 发送短信输入
/// </summary>
public class SendSmsInput
{
    /// <summary>
    /// 手机号码
    /// </summary>
    [Required(ErrorMessage = "手机号码不能为空")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入有效的手机号码")]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 短信内容
    /// </summary>
    [Required(ErrorMessage = "短信内容不能为空")]
    [StringLength(500, ErrorMessage = "短信内容不能超过500个字符")]
    public string Message { get; set; }

    /// <summary>
    /// 短信模板编码
    /// </summary>
    public string TemplateCode { get; set; }

    /// <summary>
    /// 模板参数
    /// </summary>
    public Dictionary<string, string> TemplateParams { get; set; } = new Dictionary<string, string>();

    /// <summary>
    /// 短信签名
    /// </summary>
    public string SignName { get; set; }

    /// <summary>
    /// 短信类型（验证码、通知、营销等）
    /// </summary>
    public SmsType SmsType { get; set; } = SmsType.Verification;

    /// <summary>
    /// 优先级（1-5，1最高）
    /// </summary>
    [Range(1, 5, ErrorMessage = "优先级范围为1-5")]
    public int Priority { get; set; } = 3;

    /// <summary>
    /// 是否立即发送
    /// </summary>
    public bool SendImmediately { get; set; } = true;

    /// <summary>
    /// 定时发送时间
    /// </summary>
    public DateTime? ScheduledTime { get; set; }
}

/// <summary>
/// 批量发送短信输入
/// </summary>
public class SendBatchSmsInput
{
    /// <summary>
    /// 手机号码列表
    /// </summary>
    [Required(ErrorMessage = "手机号码列表不能为空")]
    public List<string> PhoneNumbers { get; set; } = new List<string>();

    /// <summary>
    /// 短信内容
    /// </summary>
    [Required(ErrorMessage = "短信内容不能为空")]
    public string Message { get; set; }

    /// <summary>
    /// 短信模板编码
    /// </summary>
    public string TemplateCode { get; set; }

    /// <summary>
    /// 模板参数（每个手机号对应的参数）
    /// </summary>
    public Dictionary<string, Dictionary<string, string>> TemplateParams { get; set; } = new Dictionary<string, Dictionary<string, string>>();

    /// <summary>
    /// 短信签名
    /// </summary>
    public string SignName { get; set; }

    /// <summary>
    /// 短信类型
    /// </summary>
    public SmsType SmsType { get; set; } = SmsType.Notification;

    /// <summary>
    /// 批次大小（每批发送数量）
    /// </summary>
    [Range(1, 1000, ErrorMessage = "批次大小范围为1-1000")]
    public int BatchSize { get; set; } = 100;
}

/// <summary>
/// 发送验证码输入
/// </summary>
public class SendVerificationCodeInput
{
    /// <summary>
    /// 手机号码
    /// </summary>
    [Required(ErrorMessage = "手机号码不能为空")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入有效的手机号码")]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 验证码类型（登录、注册、找回密码等）
    /// </summary>
    [Required(ErrorMessage = "验证码类型不能为空")]
    public string CodeType { get; set; }

    /// <summary>
    /// 验证码长度
    /// </summary>
    [Range(4, 8, ErrorMessage = "验证码长度范围为4-8位")]
    public int CodeLength { get; set; } = 6;

    /// <summary>
    /// 验证码有效期（分钟）
    /// </summary>
    [Range(1, 60, ErrorMessage = "验证码有效期范围为1-60分钟")]
    public int ValidMinutes { get; set; } = 5;

    /// <summary>
    /// 是否为纯数字验证码
    /// </summary>
    public bool IsNumericOnly { get; set; } = true;
}

/// <summary>
/// 验证验证码输入
/// </summary>
public class VerifyCodeInput
{
    /// <summary>
    /// 手机号码
    /// </summary>
    [Required(ErrorMessage = "手机号码不能为空")]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    [Required(ErrorMessage = "验证码不能为空")]
    public string Code { get; set; }

    /// <summary>
    /// 验证码类型
    /// </summary>
    [Required(ErrorMessage = "验证码类型不能为空")]
    public string CodeType { get; set; }

    /// <summary>
    /// 验证成功后是否删除验证码
    /// </summary>
    public bool DeleteAfterVerify { get; set; } = true;
}

/// <summary>
/// 短信发送记录查询输入
/// </summary>
public class SmsRecordQueryInput : BasePageInput
{
    /// <summary>
    /// 手机号码
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 短信类型
    /// </summary>
    public SmsType? SmsType { get; set; }

    /// <summary>
    /// 发送状态
    /// </summary>
    public SmsStatus? Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 短信内容关键字
    /// </summary>
    public string MessageKeyword { get; set; }
}

/// <summary>
/// 短信类型枚举
/// </summary>
public enum SmsType
{
    /// <summary>
    /// 验证码
    /// </summary>
    Verification = 1,

    /// <summary>
    /// 通知
    /// </summary>
    Notification = 2,

    /// <summary>
    /// 营销
    /// </summary>
    Marketing = 3,

    /// <summary>
    /// 系统消息
    /// </summary>
    System = 4
}

/// <summary>
/// 短信状态枚举
/// </summary>
public enum SmsStatus
{
    /// <summary>
    /// 待发送
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 发送中
    /// </summary>
    Sending = 1,

    /// <summary>
    /// 发送成功
    /// </summary>
    Success = 2,

    /// <summary>
    /// 发送失败
    /// </summary>
    Failed = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4
}
