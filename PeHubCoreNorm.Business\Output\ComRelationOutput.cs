﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 获取组合的相关关系
/// </summary>
public class ComRelationOutput
{
    /// <summary>
    /// 互斥关系
    /// </summary>
    public List<MutexCombDetailOutput> MutexRelation { get; set; }

    /// <summary>
    /// 依赖关系
    /// </summary>
    public List<CombDependenceDetail> DependRelation { get; set; }

    /// <summary>
    /// 包含
    /// </summary>
    public List<CombContainDetailOutput> ContainRelation { get; set; }
}