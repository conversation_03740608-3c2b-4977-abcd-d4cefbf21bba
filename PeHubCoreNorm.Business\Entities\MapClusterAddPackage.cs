﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐对应加项包信息
/// </summary>
[SugarTable("MapClusterAddPackage", TableDescription = "套餐对应加项包信息")]
public class MapClusterAddPackage : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 15)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "AddPackageCode", ColumnDescription = "加项包编码", Length = 20)]
    public string AddPackageCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }
}