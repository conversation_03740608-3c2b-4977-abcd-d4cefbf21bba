﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    /// <summary>
    /// 团检号源信息表
    /// </summary>
    [SugarTable("NS_GroupSource", TableDescription = "团检号源信息表")]
    public class NS_GroupSource : NS_BaseEntry
    {
        /// <summary>
        /// 单位编码
        /// </summary>
        [SugarColumn(ColumnName = "UnitCode", ColumnDescription = "单位编码")]
        public virtual string UnitCode { get; set; }        
    }
}
