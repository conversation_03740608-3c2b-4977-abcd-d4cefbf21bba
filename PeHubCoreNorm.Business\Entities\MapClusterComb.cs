﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检套餐对应的组合
/// </summary>
[SugarTable(TableName = "MapClusterComb", TableDescription = "个检套餐对应的组合")]
public class MapClusterComb : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 15)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }
}