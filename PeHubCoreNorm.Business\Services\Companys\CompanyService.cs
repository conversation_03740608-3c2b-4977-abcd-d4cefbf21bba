﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位业务接口
/// </summary>
public class CompanyService : BizDbRepository<Company>, ICompanyService
{
    private readonly ILogger<CompanyService> _logger;
    public CompanyService(ILogger<CompanyService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 单位信息查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<CompanyOutput>> GetCompanyList(CompanyPageInput input)
    {
        var query = Context.Queryable<Company>()
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), it => it.CompanyCode.Contains(input.SearchKey) || it.CompanyName.Contains(input.SearchKey))//根据关键字查询
            .Select<CompanyOutput>();

        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    /// <summary>
    /// 单位体检次数查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    public async Task<CompanyTimeOutput[]> GetCompanyTimes(string companyCode)
    {
        return await Context.Queryable<CompanyTime>()
            .WhereIF(!string.IsNullOrEmpty(companyCode), it => it.CompanyCode == companyCode)
            .Select<CompanyTimeOutput>()
            .ToArrayAsync();
    }

    /// <summary>
    /// 单位体检套餐查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    public async Task<CompanyClusterOutput[]> GetCompanyCluster(string companyCode, int companyTimes)
    {
        return await Context.Queryable<CompanyCluster>()
          .WhereIF(!string.IsNullOrEmpty(companyCode), x => x.CompanyCode == companyCode && x.CompanyTimes == companyTimes)
          .Select<CompanyClusterOutput>()
          .ToArrayAsync();
    }

    /// <summary>
    /// 单位体检套餐控制查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="clusterCode"></param>
    /// <returns></returns>
    public async Task<CompanyClusterControlOutput> GetCompanyClusterControl(string companyCode, string clusterCode)
    {
        return await Context.Queryable<CompanyClusterControl>()
          .Where(x => x.CompanyCode == companyCode && x.ClusCode == clusterCode)
          .Select<CompanyClusterControlOutput>()
          .FirstAsync();
    }

    /// <summary>
    /// 更新单位体检套餐控制
    /// </summary>
    /// <param name="clusterControl"></param>
    /// <returns></returns>
    public async Task<bool> SaveCompanyClusterControl(CompanyClusterControl clusterControl)
    {
        var companyClusterControl = Context.Queryable<CompanyClusterControl>().First(x => x.CompanyCode == clusterControl.CompanyCode && x.ClusCode == clusterControl.ClusCode);
        if (companyClusterControl == null)
            return await Context.Insertable(companyClusterControl).ExecuteCommandAsync() > 0;
        else
            return await Context.Updateable(companyClusterControl).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 获取单位套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    public async Task<MapCompanyClusterCombOutput[]> GetMapCompanyClusterComb(MapCompanyClusterCombInput clusterInput)
    {
        return await Context.Queryable<MapCompanyClusterComb>()
           .InnerJoin<CompanyCluster>((mapCluster, cluster) => mapCluster.ClusCode == cluster.ClusCode)
           .InnerJoin<CodeItemComb>((mapCluster, cluster, comb) => comb.CombCode == mapCluster.CombCode)
           .Where(mapCluster => mapCluster.ClusCode == clusterInput.ClusCode)
           .Select<MapCompanyClusterCombOutput>()
           .ToArrayAsync();
    }
}
