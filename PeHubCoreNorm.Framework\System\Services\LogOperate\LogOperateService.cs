﻿using PeHubCoreNorm.Middlewares;

namespace PeHubCoreNorm.System;

/// <summary>
///     操作日志
/// </summary>
public class LogOperateService : DbRepository<FullModel>, ILogOperateService
{
    /// <summary>
    ///     分表最多查近多少年的数据
    /// </summary>
    private readonly int _maxTabs = 100;

    /// <summary>
    ///     异常日志中文名称
    /// </summary>
    private readonly string _nameException = "异常日志";

    /// <summary>
    ///     操作日志中文名称
    /// </summary>
    private readonly string _nameLogin = "登录日志";

    /// <summary>
    ///     操作日志中文名称
    /// </summary>
    private readonly string _nameLogout = "注销日志";

    /// <summary>
    ///     操作日志中文名称
    /// </summary>
    private readonly string _nameOperate = "操作日志";

    /// <summary>
    ///     操作日志分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<FullModel>> Page(OperateLogPageInput input)
    {
        var query = Context.Queryable<FullModel>()
            .WhereIF(!string.IsNullOrEmpty(input.Account), it => it.OpAccount == input.Account) //根据账号查询
            .WhereIF(!string.IsNullOrEmpty(input.Category), it => it.Category == input.Category) //根据分类查询
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey),
                it => it.Name.Contains(input.SearchKey) || it.OpIp.Contains(input.SearchKey)) //根据关键字查询
            .IgnoreColumns(it => new { it.ParamJson, it.ResultJson }).SplitTable(tabs => tabs.Take(_maxTabs))
            .OrderByIF(!string.IsNullOrEmpty(input.SortField), $"{input.SortField} {input.SortOrder}") //排序
            .OrderBy(it => it.OpTime, OrderByType.Desc);
        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    /// <summary>
    ///     操作日志周统计
    /// </summary>
    /// <param name="day"></param>
    /// <returns></returns>
    public async Task<List<OperateLogDayStatisticsOutput>> StatisticsByDay(int day)
    {
        //取最近七天
        var dayArray = Enumerable.Range(0, day).Select(it => DateTime.Now.Date.AddDays(it * -1)).ToList();
        //生成时间表
        var queryableLeft = Context.Reportable(dayArray).ToQueryable<DateTime>();
        //ReportableDateType.MonthsInLast1yea 表式近一年月份 并且queryable之后还能在where过滤
        var queryableRight = Context.Queryable<FullModel>().SplitTable(tabs => tabs.Take(_maxTabs)); //声名表
        //报表查询
        var list = await Context
            .Queryable(queryableLeft, queryableRight, JoinType.Left,
                (x1, x2) => x2.OpTime.ToString("yyyy-MM-dd") == x1.ColumnName.ToString("yyyy-MM-dd"))
            .GroupBy((x1, x2) => x1.ColumnName) //根据时间分组
            .OrderBy((x1, x2) => x1.ColumnName) //根据时间升序排序
            .Select((x1, x2) => new
            {
                OperateCount =
                    SqlFunc.AggregateSum(SqlFunc.IIF(x2.Category == CateGoryConst.Log_OPERATE, 1,
                        0)), //null的数据要为0所以不能用count
                ExceptionCount =
                    SqlFunc.AggregateSum(SqlFunc.IIF(x2.Category == CateGoryConst.Log_EXCEPTION, 1,
                        0)), //null的数据要为0所以不能用count
                LoginCount =
                    SqlFunc.AggregateSum(SqlFunc.IIF(x2.Category == CateGoryConst.Log_LOGIN, 1,
                        0)), //null的数据要为0所以不能用count
                LogoutCount =
                    SqlFunc.AggregateSum(SqlFunc.IIF(x2.Category == CateGoryConst.Log_LOGOUT, 1,
                        0)), //null的数据要为0所以不能用count
                Date = x1.ColumnName.ToString("yyyy-MM-dd")
            }).ToListAsync();
        //定义返回结果
        var result = new List<OperateLogDayStatisticsOutput>();
        //遍历结果
        list.ForEach(it =>
        {
            result.Add(new OperateLogDayStatisticsOutput
                { Date = it.Date, Name = _nameLogin, Count = it.LoginCount }); //添加访问日志
            result.Add(new OperateLogDayStatisticsOutput
                { Date = it.Date, Name = _nameLogout, Count = it.LogoutCount }); //添加访问日志
            result.Add(new OperateLogDayStatisticsOutput
                { Date = it.Date, Name = _nameOperate, Count = it.OperateCount }); //添加访问日志
            result.Add(new OperateLogDayStatisticsOutput
                { Date = it.Date, Name = _nameException, Count = it.ExceptionCount }); //添加异常日志
        });
        return result;
    }

    /// <summary>
    ///     总数统计
    /// </summary>
    /// <returns></returns>
    public async Task<List<OperateLogTotalCountOutput>> TotalCount()
    {
        var data = await Context.Queryable<FullModel>().SplitTable(tabs => tabs.Take(_maxTabs))
            .GroupBy(it => it.Category) //根据分类分组
            .Select(it => new
            {
                it.Category, //分类
                Count = SqlFunc.AggregateCount(it.Category) //数量
            }).ToListAsync();
        //定义结果数组
        var operateLogTotalCounts = new List<OperateLogTotalCountOutput>
        {
            //添加操作日志数据
            new()
            {
                Type = _nameLogin,
                Value = data.Where(it => it.Category == CateGoryConst.Log_LOGIN).Select(it => it.Count).FirstOrDefault()
            },
            //添加操作日志数据
            new()
            {
                Type = _nameLogout,
                Value = data.Where(it => it.Category == CateGoryConst.Log_LOGOUT).Select(it => it.Count)
                    .FirstOrDefault()
            },
            //添加操作日志数据
            new()
            {
                Type = _nameOperate,
                Value = data.Where(it => it.Category == CateGoryConst.Log_OPERATE).Select(it => it.Count)
                    .FirstOrDefault()
            },
            //添加异常日志数据
            new()
            {
                Type = _nameException,
                Value = data.Where(it => it.Category == CateGoryConst.Log_EXCEPTION).Select(it => it.Count)
                    .FirstOrDefault()
            }
        };
        return operateLogTotalCounts;
    }

    /// <inheritdoc />
    public async Task Delete(string category)
    {
        await Context.Deleteable<FullModel>().Where(it => it.Category == category)
            .SplitTable(tabs => tabs.Take(_maxTabs))
            .ExecuteCommandAsync(); //删除对应分类日志
    }

    /// <inheritdoc />
    public async Task<FullModel> Detail(BaseIdInput input)
    {
        return await GetFirstSplitTableAsync(it => it.Id == input.Id, tabs => tabs.Take(_maxTabs));
    }
}