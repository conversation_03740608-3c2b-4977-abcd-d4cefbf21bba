﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Output
{
    public class CompanyClusterDetailsOutput
    {
        /// <summary>
        /// 组合代码
        /// </summary>
        public string ComCode { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        public string ComName { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 是否优先
        /// </summary>
        public bool IsPriority { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        public string ClsCode { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 注意事项
        /// </summary>
        public string Attention { get; set; }
    }
}
