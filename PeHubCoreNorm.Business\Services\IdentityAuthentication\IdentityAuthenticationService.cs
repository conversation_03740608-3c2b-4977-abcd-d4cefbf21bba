using Microsoft.Extensions.Logging;
using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using System.Text;

namespace PeHubCoreNorm.Business.Services.IdentityAuthentication;

/// <summary>
/// 身份认证服务实现
/// </summary>
public class IdentityAuthenticationService : BizDbRepository<UnitPersonnelList>, IIdentityAuthenticationService
{
    private readonly ILogger<IdentityAuthenticationService> _logger;
    private readonly ICacheService _cacheService;
    private readonly IMembersService _membersService;

    public IdentityAuthenticationService(
        ILogger<IdentityAuthenticationService> logger,
        ICacheService cacheService,
        IMembersService membersService)
    {
        _logger = logger;
        _cacheService = cacheService;
        _membersService = membersService;
    }

    /// <summary>
    /// 身份认证
    /// </summary>
    /// <param name="input">认证输入参数</param>
    /// <returns>认证结果</returns>
    public async Task<IdentityAuthenticationOutput> AuthenticateIdentity(IdentityAuthenticationInput input)
    {
        try
        {
            var result = new IdentityAuthenticationOutput();

            // 1. 验证短信验证码（如果提供）
            if (!string.IsNullOrEmpty(input.SmsCode))
            {
                var smsValid = await VerifySmsCode(input.Tel, input.SmsCode);
                if (!smsValid)
                {
                    result.IsAuthenticated = false;
                    result.Message = "短信验证码错误或已过期";
                    return result;
                }
            }

            // 2. 查找已激活的人员
            var personnelList = await FindActivePersonnel(input.IdNumber, input.Tel);

            if (personnelList == null || !personnelList.Any())
            {
                result.IsAuthenticated = false;
                result.Message = "未找到符合条件的已激活人员信息";
                _logger.LogWarning($"身份认证失败：证件号码 {input.IdNumber}，电话 {input.Tel} 未找到已激活人员");
                return result;
            }

            // 3. 认证成功
            result.IsAuthenticated = true;
            result.Message = "身份认证成功";
            result.PersonnelList = personnelList;

            // 4. 生成认证令牌（使用第一个人员信息）
            if (personnelList.Any())
            {
                result.AuthToken = await GenerateAuthToken(personnelList.First());
            }

            _logger.LogInformation($"身份认证成功：证件号码 {input.IdNumber}，电话 {input.Tel}，找到 {personnelList.Count} 条人员信息");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"身份认证异常：证件号码 {input.IdNumber}，电话 {input.Tel}");
            return new IdentityAuthenticationOutput
            {
                IsAuthenticated = false,
                Message = "身份认证失败，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 发送短信验证码
    /// </summary>
    /// <param name="input">发送短信输入参数</param>
    /// <returns>发送结果</returns>
    public async Task<SendSmsCodeOutput> SendSmsCode(SendSmsCodeInput input)
    {
        try
        {
            // 1. 验证该证件号码和电话是否存在人员记录
            var personnelExists = await Context.Queryable<UnitPersonnelList>()
                .Where(x => x.IdNumber == input.IdNumber && x.Tel == input.Tel)
                .AnyAsync();

            if (!personnelExists)
            {
                return new SendSmsCodeOutput
                {
                    Success = false,
                    Message = "证件号码和电话号码不匹配或不存在相关人员信息"
                };
            }

            // 2. 检查发送频率限制
            var rateLimitKey = $"SMS_RATE_LIMIT_{input.Tel}";
            var lastSendTime = _cacheService.Get<DateTime?>(rateLimitKey);
            if (lastSendTime.HasValue && (DateTime.Now - lastSendTime.Value).TotalMinutes < 1)
            {
                return new SendSmsCodeOutput
                {
                    Success = false,
                    Message = "发送过于频繁，请1分钟后再试"
                };
            }

            // 3. 生成6位数字验证码
            var code = new Random().Next(100000, 999999).ToString();

            // 4. 发送短信 SendSmsRequest/Template_param
            var smsRequest = new SendSmsInput
            {
                PhoneNumber = input.Tel,
                Message = $"您的验证码是：{code}，5分钟内有效，请勿泄露。",
                SignName = "体检预约系统",
                TemplateCode = "SMS_VERIFICATION",

            };

            //var smsResult = await _smsService.SendSmsAsync(smsRequest);

            //if ( smsResult !=null)
            //{
            //    // 5. 保存验证码到缓存（5分钟有效期）
            //    var cacheKey = $"SMS_CODE_{input.Tel}";
            //    _cacheService.Set(cacheKey, code, TimeSpan.FromMinutes(5));

            //    // 6. 设置发送频率限制
            //    _cacheService.Set(rateLimitKey, DateTime.Now, TimeSpan.FromMinutes(1));



            //    _logger.LogInformation($"短信验证码发送成功：电话 {input.Tel}");
            //    return new SendSmsCodeOutput
            //    {
            //        Success = true,
            //        Message = "验证码发送成功",
            //        ValidMinutes = 5
            //    };
            //}
            //else
            //{
            //    _logger.LogError($"短信验证码发送失败：电话 {input.Tel}，错误信息：{smsResult}");
            //    return new SendSmsCodeOutput
            //    {
            //        Success = false,
            //        Message = "验证码发送失败，请稍后重试"
            //    };
            //}

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"发送短信验证码异常：电话 {input.Tel}");
            return new SendSmsCodeOutput
            {
                Success = false,
                Message = "验证码发送失败，请稍后重试"
            };
        }
    }

    /// <summary>
    /// 验证短信验证码
    /// </summary>
    /// <param name="tel">电话号码</param>
    /// <param name="code">验证码</param>
    /// <returns>验证结果</returns>
    public async Task<bool> VerifySmsCode(string tel, string code)
    {
        try
        {
            var cacheKey = $"SMS_CODE_{tel}";
            var cachedCode = _cacheService.Get<string>(cacheKey);

            if (string.IsNullOrEmpty(cachedCode))
            {
                _logger.LogWarning($"短信验证码已过期或不存在：电话 {tel}");
                return false;
            }

            if (cachedCode != code)
            {
                _logger.LogWarning($"短信验证码错误：电话 {tel}，输入码 {code}");
                return false;
            }

            // 验证成功后删除缓存
            _cacheService.Remove(cacheKey);
            _logger.LogInformation($"短信验证码验证成功：电话 {tel}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"验证短信验证码异常：电话 {tel}");
            return false;
        }
    }

    /// <summary>
    /// 根据证件号码和电话查找已激活的人员
    /// </summary>
    /// <param name="idNumber">证件号码</param>
    /// <param name="tel">电话号码</param>
    /// <param name="companyCode">单位编码（可选）</param>
    /// <param name="batchNumber">体检次数（可选）</param>
    /// <returns>人员信息列表</returns>
    public async Task<List<PersonnelInfo>> FindActivePersonnel(string idNumber, string tel, string companyCode = null, int? batchNumber = null)
    {
        var query = Context.Queryable<UnitPersonnelList>()
            .Where(x => x.IdNumber == idNumber && x.Tel == tel && x.Status == 1) // 只查找已激活的人员
            .WhereIF(!string.IsNullOrEmpty(companyCode), x => x.CompanyCode == companyCode)
            .WhereIF(batchNumber.HasValue, x => x.BatchNumber == batchNumber.Value);

        var personnelList = await query.ToListAsync();

        return personnelList.Select(p => new PersonnelInfo
        {
            Id = p.Id,
            EmployeeName = p.EmployeeName,
            CompanyCode = p.CompanyCode,
            CompanyName = p.CompanyName,
            Department = p.Department,
            IdNumber = p.IdNumber,
            IdNumberType = p.IdNumberType,
            Tel = p.Tel,
            Sex = p.Sex,
            Age = p.Age,
            PackAgeCode = p.PackAgeCode,
            PackAgeName = p.PackAgeName,
            BatchNumber = p.BatchNumber,
            StartTime = p.StartTime,
            EndTime = p.EndTime,
            Status = p.Status
        }).ToList();
    }

    /// <summary>
    /// 生成认证令牌
    /// </summary>
    /// <param name="personnelInfo">人员信息</param>
    /// <returns>认证令牌</returns>
    public async Task<string> GenerateAuthToken(PersonnelInfo personnelInfo)
    {
        try
        {
            var tokenData = new
            {
                Id = personnelInfo.Id,
                IdNumber = personnelInfo.IdNumber,
                Tel = personnelInfo.Tel,
                CompanyCode = personnelInfo.CompanyCode,
                BatchNumber = personnelInfo.BatchNumber,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            var token = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(tokenData)));
            
            // 将令牌存储到缓存中，有效期2小时
            var cacheKey = $"AUTH_TOKEN_{token}";
            _cacheService.Set(cacheKey, personnelInfo, TimeSpan.FromHours(2));

            return token;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"生成认证令牌异常：人员ID {personnelInfo.Id}");
            return null;
        }
    }

    /// <summary>
    /// 验证认证令牌
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <returns>人员信息</returns>
    public async Task<PersonnelInfo> ValidateAuthToken(string token)
    {
        try
        {
            var cacheKey = $"AUTH_TOKEN_{token}";
            var personnelInfo = _cacheService.Get<PersonnelInfo>(cacheKey);

            if (personnelInfo == null)
            {
                _logger.LogWarning($"认证令牌无效或已过期：{token}");
                return null;
            }

            return personnelInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"验证认证令牌异常：{token}");
            return null;
        }
    }
}
