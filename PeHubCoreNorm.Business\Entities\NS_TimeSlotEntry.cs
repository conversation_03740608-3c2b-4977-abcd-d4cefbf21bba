﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    [SugarTable("TimeSlotEntry", TableDescription = "号源时段信息表")]
    public class NS_TimeSlotEntry : BaseEntity
    {
        /// <summary>
        /// 时段全称（08:00-09:00）
        /// </summary>
        [SugarColumn(ColumnName = "TimeSlotName", ColumnDescription = "时段全称")]
        public virtual string TimeSlotName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnName = "StartTime", ColumnDescription = "开始时间")]
        public virtual string StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(ColumnName = "EndTime", ColumnDescription = "结束时间")]
        public virtual string EndTime { get; set; }

        /// <summary>
        /// 上下午标识（上午：AM,下午：PM,全天：ALL）
        /// </summary>
        [SugarColumn(ColumnName = "TimePeriod", ColumnDescription = "上下午标识（上午：AM,下午：PM,全天：ALL）")]
        public virtual string TimePeriod { get; set; }        
    }
}
