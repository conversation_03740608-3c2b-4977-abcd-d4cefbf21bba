﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 项目分类下的组合数据
/// </summary>
public class ItemClsCombOutput
{
    /// <summary>
    /// 项目分类代码
    /// </summary>
    public string ClsCode { get; set; }

    /// <summary>
    /// 项目分类名称
    /// </summary>
    public string ClsName { get; set; }

    /// <summary>
    /// 组合数据
    /// </summary>
    public CombData[] CombData { get; set; }
}

/// <summary>
/// 组合数据
/// </summary>
public class CombData
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
}
