using PeHubCoreNorm.Business.Input;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 短信模板
/// </summary>
[SugarTable("SmsTemplate", TableDescription = "短信模板")]
public class SmsTemplate : BaseEntity
{
    /// <summary>
    /// 模板编码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "TemplateCode", ColumnDescription = "模板编码", Length = 50)]
    public string TemplateCode { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "TemplateName", ColumnDescription = "模板名称", Length = 100)]
    public string TemplateName { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "TemplateContent", ColumnDescription = "模板内容", Length = 500)]
    public string TemplateContent { get; set; }

    /// <summary>
    /// 短信类型
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "SmsType", ColumnDescription = "短信类型")]
    public int SmsType { get; set; }

    /// <summary>
    /// 短信签名
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "SignName", ColumnDescription = "短信签名", Length = 50)]
    public string SignName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "IsEnabled", ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 第三方平台模板ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "PlatformTemplateId", ColumnDescription = "第三方平台模板ID", Length = 100)]
    public string PlatformTemplateId { get; set; }

    /// <summary>
    /// 短信提供商
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "Provider", ColumnDescription = "短信提供商", Length = 50)]
    public string Provider { get; set; }

    /// <summary>
    /// 模板参数说明
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "ParamsDescription", ColumnDescription = "模板参数说明", Length = 1000)]
    public string ParamsDescription { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnName = "Remark", ColumnDescription = "备注", Length = 500)]
    public string Remark { get; set; }
}
