namespace PeHubCoreNorm.Business.Output;

/// <summary>
/// 单位人员详细信息输出（包含套餐信息）
/// </summary>
public class UnitPersonnelDetailOutput
{
    /// <summary>
    /// 人员ID
    /// </summary>
}

/// <summary>
/// 单位人员简要信息输出
/// </summary>
public class UnitPersonnelBriefOutput
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 员工姓名
    /// </summary>
    public string EmployeeName { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IdNumber { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string PackAgeName { get; set; }

    
    /// <summary>
    /// 套餐编码
    /// </summary>
    public string PackAgeCode {  get; set; }

    /// <summary>
    /// 套餐价格
    /// </summary>
    public decimal? PackagePrice { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public int BatchNumber { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText => Status switch
    {
        0 => "未激活",
        1 => "已激活",
        _ => "未知状态"
    };

    /// <summary>
    /// 有效期
    /// </summary>
    public string ValidPeriod { get; set; }
}
