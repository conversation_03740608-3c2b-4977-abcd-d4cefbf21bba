namespace PeHubCoreNorm.Business.Output;

/// <summary>
/// 单位人员详细信息输出（包含套餐信息）
/// </summary>
public class UnitPersonnelDetailOutput
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText => Status switch
    {
        0 => "未激活",
        1 => "已激活",
        _ => "未知状态"
    };

    /// <summary>
    /// 员工姓名
    /// </summary>
    public string EmployeeName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int Sex { get; set; }

    /// <summary>
    /// 性别描述
    /// </summary>
    public string SexText => Sex switch
    {
        1 => "男",
        2 => "女",
        _ => "未知"
    };

    /// <summary>
    /// 民族
    /// </summary>
    public string Ethnic { get; set; }

    /// <summary>
    /// 婚姻状况
    /// </summary>
    public int Married { get; set; }

    /// <summary>
    /// 婚姻状况描述
    /// </summary>
    public string MarriedText => Married switch
    {
        0 => "未婚",
        1 => "已婚",
        2 => "离异",
        3 => "丧偶",
        _ => "未知"
    };

    /// <summary>
    /// 出生日期
    /// </summary>
    public string Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 证件号类型
    /// </summary>
    public int IdNumberType { get; set; }

    /// <summary>
    /// 证件号类型描述
    /// </summary>
    public string IdNumberTypeText => IdNumberType switch
    {
        1 => "身份证",
        2 => "护照",
        3 => "军官证",
        4 => "港澳通行证",
        5 => "台胞证",
        _ => "其他"
    };

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IdNumber { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string PackAgeCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string PackAgeName { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public int BatchNumber { get; set; }

    /// <summary>
    /// 有效起始日期
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 有效终止日期
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateDate { get; set; }

    /// <summary>
    /// 套餐详细信息
    /// </summary>
    public PackageDetailInfo PackageDetail { get; set; }
}

/// <summary>
/// 套餐详细信息
/// </summary>
public class PackageDetailInfo
{
    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string ClusName { get; set; }

    /// <summary>
    /// 套餐价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 性别限制(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 性别限制描述
    /// </summary>
    public string GenderText => Gender switch
    {
        "0" => "通用",
        "1" => "男性",
        "2" => "女性",
        _ => "未知"
    };

    /// <summary>
    /// 是否启用
    /// </summary>
    public string IsEnabled { get; set; }

    /// <summary>
    /// 是否启用描述
    /// </summary>
    public string IsEnabledText => IsEnabled == "Y" ? "启用" : "停用";

    /// <summary>
    /// 是否固定套餐
    /// </summary>
    public string IsFixed { get; set; }

    /// <summary>
    /// 是否固定套餐描述
    /// </summary>
    public string IsFixedText => IsFixed == "Y" ? "固定套餐" : "可调整套餐";

    /// <summary>
    /// 体检分类
    /// </summary>
    public string PeCls { get; set; }

    /// <summary>
    /// 加项规则
    /// </summary>
    public string AddRule { get; set; }

    /// <summary>
    /// 加项规则描述
    /// </summary>
    public string AddRuleText => AddRule switch
    {
        "1" => "允许加项",
        "2" => "允许加指定项目",
        "3" => "不允许加项",
        _ => "未知"
    };

    /// <summary>
    /// 是否限制年龄
    /// </summary>
    public string IsAgeLimit { get; set; }

    /// <summary>
    /// 年龄下限
    /// </summary>
    public int LowerAgeLimit { get; set; }

    /// <summary>
    /// 年龄上限
    /// </summary>
    public int UpperAgeLimit { get; set; }

    /// <summary>
    /// 年龄限制描述
    /// </summary>
    public string AgeLimitText
    {
        get
        {
            if (IsAgeLimit == "Y")
            {
                return $"{LowerAgeLimit}-{UpperAgeLimit}岁";
            }
            return "无年龄限制";
        }
    }

    /// <summary>
    /// 套餐简介
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string Notice { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public string Tag { get; set; }
}

/// <summary>
/// 单位人员简要信息输出
/// </summary>
public class UnitPersonnelBriefOutput
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 员工姓名
    /// </summary>
    public string EmployeeName { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IdNumber { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string PackAgeName { get; set; }

    /// <summary>
    /// 套餐价格
    /// </summary>
    public decimal? PackagePrice { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public int BatchNumber { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText => Status switch
    {
        0 => "未激活",
        1 => "已激活",
        _ => "未知状态"
    };

    /// <summary>
    /// 有效期
    /// </summary>
    public string ValidPeriod { get; set; }
}
