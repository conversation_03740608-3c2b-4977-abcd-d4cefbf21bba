﻿using System.Diagnostics;
using PeHubCoreNorm.Middlewares;

namespace PeHubCoreNorm.Services.CommonService;

public class CommonService : DbRepository<FullModel>, ICommonService
{
    public async Task WriteSeedDataJson(BaseIdsInput input,string config = SqlsugarConst.DB_Default)
    {
        if (!Debugger.IsAttached)
            throw new Exception("请打开你的解决方案,F5启动后再生成JSON,非调试情况下不可以生成数据库种子");

        foreach (var item in input.Ids)
        {
            var datas = await Context.Ado.GetDataTableAsync($"SELECT * FROM {item}");
            SeedDataUtils.WriteSeedDataJson(config, datas, $"{item}.json");
        }
    }
}