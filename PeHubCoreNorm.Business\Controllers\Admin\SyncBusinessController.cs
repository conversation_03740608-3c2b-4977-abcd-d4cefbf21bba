﻿namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 同步业务控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class SyncBusinessController : BaseControllerAuthorize
{
    private readonly ISyncService _syncService;

    public SyncBusinessController(ISyncService syncService)
    {
        _syncService = syncService;
    }

    #region 同步单位
    /// <summary>
    /// 同步单位信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCompany")]
    [ActionPermission(ActionType.Button, "同步单位信息", "同步信息业务")]
    public async Task<bool> SyncCompany()
    {
        return await _syncService.SyncCompany();
    }

    /// <summary>
    /// 同步单位体检次数信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCompanyTimes")]
    [ActionPermission(ActionType.Button, "同步单位体检次数信息", "同步信息业务")]
    public async Task<bool> SyncCompanyTimes()
    {
        return await _syncService.SyncCompanyTimes();
    }

    /// <summary>
    /// 同步单位体检套餐信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCompanyCluster")]
    [ActionPermission(ActionType.Button, "同步单位体检套餐信息", "同步信息业务")]
    public async Task<bool> SyncCompanyCluster()
    {
        return await _syncService.SyncCompanyCluster();
    }

    /// <summary>
    /// 同步单位体检套餐的组合对应信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCompanyClusterComb")]
    [ActionPermission(ActionType.Button, "同步单位体检套餐信息", "同步信息业务")]
    public async Task<bool> SyncCompanyClusterComb()
    {
        return await _syncService.SyncCompanyClusterComb();
    }
    #endregion

    #region 同步个检套餐及对应组合
    /// <summary>
    /// 同步个检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncClusterAndCombs")]
    [ActionPermission(ActionType.Button, "同步个检套餐及对应组合", "同步信息业务")]
    public async Task<bool> SyncClusterAndCombs()
    {
        return await _syncService.SyncClusterAndCombs();
    }
    #endregion

    #region 同步组合
    /// <summary>
    /// 同步组合
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCodeItemComb")]
    [ActionPermission(ActionType.Button, "同步组合信息", "同步信息业务")]
    public async Task<bool> SyncCodeItemComb()
    {
        return await _syncService.SyncCodeItemComb();
    }
    #endregion

    #region 同步项目分类
    /// <summary>
    /// 同步项目分类
    /// </summary>
    /// <returns></returns>
    [HttpPost("SyncCodeItemCls")]
    [ActionPermission(ActionType.Button, "同步项目分类", "同步信息业务")]
    public async Task<bool> SyncCodeItemCls()
    {
        return await _syncService.SyncCodeItemCls();
    }
    #endregion
}