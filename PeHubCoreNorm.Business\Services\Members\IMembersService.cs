﻿

namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位业务接口
/// </summary>
public interface IMembersService : ITransient
{
    /// <summary>
    /// 根据wId获取用户选项
    /// </summary>
    /// <param name="wId"></param>
    /// <returns></returns>
    Task<AuthMember> GetMembersByWId(string wId,string orgkey);

    /// <summary>
    /// 获取用户信息(根据name、wId、tel、cardNo获取）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<Members>> GetMembersList(MembersInput input);
    /// <summary>
    /// 根据id更新名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> UpdateMembersName(MembersNameInput input);

   
    Task<dynamic> GetCaptchaInfo();

    Task ValidValidCode(string validCode, string validCodeReqNo, bool isDelete = true);

    Task<AuthMember> authlogin(AuthFormInput loginInput);
    Task<AuthMember> authloginOther(AuthFormInput loginInput);



    #region 绑卡
    /// <summary>
    /// 插入卡，如果卡存在则更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId，返回id
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> EditCardsReturnId(CardList input);

    /// <summary>
    /// 绑定卡，如果已存在则返回false
    /// </summary>
    /// <param name="membersId"></param>
    /// <param name="cardId"></param>
    /// <returns></returns>
    bool MembersBingsCards(string membersId, string cardId);
    /// <summary>
    /// 获取用户id绑定的卡
    /// </summary>
    /// <param name="membersId"></param>
    /// <returns></returns>
    Task<List<CardList>> GetCardsById(string membersId);
    /// <summary>
    /// 用户id解绑卡
    /// </summary>
    /// <param name="membersId"></param>
    /// <param name="cardId"></param>
    /// <returns></returns>
    Task<bool> UnbindTheCard(string membersId, string cardId);
    /// <summary>
    /// 根据卡id获取卡信息
    /// </summary>
    /// <param name="cardId"></param>
    /// <returns></returns>
    Task<CardList> GetCardById(string cardId);
    /// <summary>
    /// 根据卡id更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId
    /// </summary>
    /// <param name="card"></param>
    /// <returns></returns>
    bool UpdateCard(CardList card);


    #endregion
}