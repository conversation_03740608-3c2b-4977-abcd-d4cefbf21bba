﻿using Masuit.Tools;
using System.Collections.Immutable;

namespace PeHubCoreNorm.RBAC;

public class RoleService : DbRepository<SysRole>, IRoleService
{
    private readonly ICacheService _cacheService;
    private readonly IRelationService _relationService;

    public RoleService(IRelationService relationService,
        ICacheService cacheService)
    {
        _relationService = relationService;
        _cacheService = cacheService;
    }

    public async Task Add(RoleAddInput input)
    {
        var exist = await GetFirstAsync(x => x.Name == input.Name);
        if (exist != null)
        {
            Unify.SetError("角色已经存在");
            return;
        }

        var sysRole = input.Adapt<SysRole>(); //实体转换

        if (sysRole != null && string.IsNullOrEmpty(sysRole.Code)) sysRole.Code = CaptchaUtil.CreateCharCode(10);

        await InsertAsync(sysRole);
    }

    public async Task Delete(BaseIdsInput input)
    {
        var ids = input.Ids.ToList();
        if (ids.Count > 0)
        {
            //删除用户的接口权限缓存
            _cacheService.Remove(CacheConst.Cache_UserRelation);
            _cacheService.Remove(CacheConst.Cache_UserRelationMenu);

            var result = await itenant.UseTranAsync(async () =>
            {
                await DeleteByIdsAsync(ids.Cast<object>().ToArray()); //删除角色
                var relationRep = ChangeRepository<DbRepository<SysRelation>>(); //切换仓储
                //用户有多少角色
                await relationRep.DeleteAsync(it =>
                    ids.Contains(it.TargetId) && it.Category == CateGoryConst.Relation_SYS_USER_HAS_ROLE);
                //角色有多少菜单
                await relationRep.DeleteAsync(it =>
                    ids.Contains(it.ObjectId) && it.Category == CateGoryConst.Relation_SYS_ROLE_HAS_MENU);
            });
            if (result.IsSuccess) //如果成功了
            {
            }
            else
            {
                throw result.ErrorException;
            }
        }
    }

    public async Task Edit(RoleEditInput input)
    {
        var exist = await GetFirstAsync(x => x.Name == input.Name);
        if (exist != null && exist.Id != input.Id)
        {
            Unify.SetError("角色已经存在");
            return;
        }

        var sysRole = input.Adapt<SysRole>(); //实体转换
        await UpdateAsync(sysRole); //更新角色

        if (exist != null&&exist.ExtJson!=sysRole.ExtJson)
        {
            //更新关联的用户
            Task.Factory.StartNew(() =>
            {
                var userlist = Context.Queryable<SysRelation>()
                .Where(it => it.Category == CateGoryConst.Relation_SYS_USER_HAS_ROLE && exist.Id == it.TargetId)
                .Select(it => it.ObjectId).Distinct().ToList();

                    foreach (var item in userlist)
                    {
                        var itemUser = Context.Queryable<SysUser>().First(it => it.Id == item);
                        if (itemUser != null)
                        {
                            var roleOrg = exist.ExtJson.Split(",");
                            itemUser.ExtJson = string.Join(",", itemUser.ExtJson.Split(",").Except(roleOrg).Adapt(sysRole.ExtJson.Split(",")));
                            Context.Updateable<SysUser>(itemUser).ExecuteCommand();
                        }
                    }
                
            });
         }

    }

    public async Task<List<SysRole>> GetRoleListByUserId(string userId)
    {
        var cods = new List<SysRole>(); //角色代码集合
        var roleList =
            await _relationService.GetRelationListByObjectIdAndCategory(userId,
                CateGoryConst.Relation_SYS_USER_HAS_ROLE); //根据用户ID获取角色ID
        var roleIdList = roleList.Select(x => x.TargetId).ToList(); //角色ID列表
        if (roleIdList.Count > 0) cods = await GetListAsync(it => roleIdList.Contains(it.Id));
        return cods;
    }

    public async Task GrantMenu(GrantMenuInput input)
    {
        var relationRoles = new List<SysRelation>(); //要添加的角色资源和授权关系表

        foreach (var item in input.GrantInfoList)
            relationRoles.Add(new SysRelation
            {
                ObjectId = input.Id,
                TargetId = item.MenuId,
                Category = CateGoryConst.Relation_SYS_ROLE_HAS_MENU,
                ExtJson = item.ButtonInfo.ToJson()
            });

        //删除用户的接口权限缓存
        _cacheService.Remove(CacheConst.Cache_UserRelation);
        _cacheService.Remove(CacheConst.Cache_UserRelationMenu);

        //事务
        var result = await itenant.UseTranAsync(async () =>
        {
            var relationRep = ChangeRepository<DbRepository<SysRelation>>(); //切换仓储
            //删除 角色有多少菜单
            await relationRep.DeleteAsync(it =>
                it.ObjectId == input.Id && it.Category == CateGoryConst.Relation_SYS_ROLE_HAS_MENU);
            //添加新的
            await relationRep.InsertRangeAsync(relationRoles);
        });
        if (result.IsSuccess) //如果成功了
        {
        }
        else
        {
            throw result.ErrorException;
        }
    }

    public async Task<RoleOwnResourceOutput> OwnResource(BaseIdInput input)
    {
        var roleOwnResource = new RoleOwnResourceOutput { Id = input.Id };
        var GrantInfoList = new List<RelationRoleMenu>();

        var relations =
            await _relationService.GetRelationListByObjectIdAndCategory(input.Id,
                CateGoryConst.Relation_SYS_ROLE_HAS_MENU);

        foreach (var item in relations)
            GrantInfoList.Add(new RelationRoleMenu
            {
                MenuId = item.TargetId,
                ButtonInfo = item.ExtJson.ToObject<List<string>>()
            });

        roleOwnResource.GrantInfoList = GrantInfoList;
        return roleOwnResource;
    }


    public async Task<SqlSugarPagedList<SysRole>> Page(RolePageInput input)
    {
		var orlist = new List<string>();
		if (!string.IsNullOrEmpty(input.orgChoose))
		{
			orlist = input.orgChoose.Split(",").Distinct().ToList();
		}

		var query = Context.Queryable<SysRole>()
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey), it => it.Name.Contains(input.SearchKey)) //根据关键字查询
            .WhereIF(!string.IsNullOrEmpty(input.status), it => it.status.Equals(input.status)) // 
			.WhereIF(orlist.Count()>0, it => string.IsNullOrWhiteSpace(it.ExtJson)||orlist.All(v => it.ExtJson.Contains(v)))//是否在一个院区
			.OrderByIF(!string.IsNullOrEmpty(input.SortField), $"{input.SortField} {input.SortOrder}")
            .OrderBy(it => it.SortCode); //排序
        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    public async Task<SqlSugarPagedList<RoleSelectorOutPut>> RoleSelector(RolePageInput input)
    {
        var result = await Context.Queryable<SysRole>()
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey),
                it => it.Name.Contains(input.SearchKey) || it.Code.Contains(input.SearchKey)) //根据关键字查询
            .Select<RoleSelectorOutPut>()
            .ToPagedListAsync(input.pageNum, input.pageSize);
        return result;
    }
}