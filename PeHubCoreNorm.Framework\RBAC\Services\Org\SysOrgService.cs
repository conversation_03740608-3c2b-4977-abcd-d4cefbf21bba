﻿using System.Text.RegularExpressions;
using Masuit.Tools;
using PeHubCoreNorm.System;

namespace PeHubCoreNorm.RBAC;

public class SysOrgService : DbRepository<SysOrg>, ISysOrgService
{
    private readonly ICacheService _cacheService;

	public SysOrgService(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

	public async Task<SysOrg> GetSysOrgById(string Id)
	{
		var user = await Context.Queryable<SysOrg>()
			.Where(u => u.Id == Id)
			.Select(u => new SysOrg
			{
				Id = u.Id.SelectAll()
			}).FirstAsync();
		return user;
	}
	 

	public async Task Add(OrgAddInput input)
    {
        var check = await CheckInput(input); //检查参数
        if (check != "OK")
        {
            Unify.SetError(check);
            return;
        }
        var sysOrg = input.Adapt<SysOrg>(); //实体转换
        await InsertAsync(sysOrg); //添加数据
    }

    public async Task Delete(BaseIdsInput input)
    {
        var ids = input.Ids.ToList();
        if (ids.Count > 0)
        {

            var result = await itenant.UseTranAsync(async () =>
            {
                //删除组织机构
                await DeleteByIdsAsync(ids.Cast<object>().ToArray());
            });
            if (result.IsSuccess) //如果成功了
            {
            }
            else
            {
                throw result.ErrorException;
            }
        }
    }

    public async Task Edit(OrgEditInput input)
    {
        await CheckInput(input); //检查参数
        var exist = await GetByIdAsync(input.Id); //获取信息
        if (exist != null)

        { 
            var sysorg = input.Adapt<SysOrg>(); //实体转换
            await Context.Updateable(sysorg).ExecuteCommandAsync(); //修改数据
        }
    }


    public async Task<SqlSugarPagedList<SysOrg>> Page(OrgPageInput input)
    {
        var query = GetQuery(input); //获取查询条件
        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

	/// <summary>
	/// 获取列表
	/// </summary>
	/// <param name="useid"></param>
	/// <param name="issupadmin"></param>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	public async Task<List<OrgOutPut>> GetOrgList(string useid, bool issupadmin)
	{
        if (issupadmin)
        {
			var query2 = Context.Queryable<SysOrg>()
				.Where(u => u.Status == PeHubCoreNormConst.STATUS_ENABLE) //根据状态查询
				.Select(u => new OrgOutPut
				{
					OrgCode = u.OrgCode,
					OrgName = u.OrgName,
					AreaCode = u.AreaCode,
					AreaName = u.AreaName
				});
			return await query2.ToListAsync();
		}
        if (string.IsNullOrEmpty(useid))
        {
            return new List<OrgOutPut>();
        }
 
		var relations = Context.Queryable<SysRelation>().Where(x => x.Category == CateGoryConst.Relation_SYS_USER_HAS_ROLE && x.ObjectId == useid)
            .Select(x => x.TargetId).ToList();
		var roleOrglist = await Context.Queryable<SysRole>()
		   .Where(x => relations.Select(x => x).Contains(x.Id))
		   .Select(it => it.ExtJson)
		   .ToListAsync();

        var codelist = new List<string>();

		foreach (var item in roleOrglist)
        {
			codelist.AddRange(item.Split(',').ToList());
		}
		codelist = codelist.Distinct().ToList();

		var query = Context.Queryable<SysOrg>()
		  
		  .Where(u => u.Status == PeHubCoreNormConst.STATUS_ENABLE) //根据状态查询
          .Where(x=>codelist.Any(cl=>x.OrgCode==cl))
		  .Select(u => new OrgOutPut
		  {
			  OrgCode = u.OrgCode,
              OrgName = u.OrgName,
              AreaCode=u.AreaCode,
              AreaName=u.AreaName,
			  Address = u.Address,
			  Avatar = u.Avatar,
              Describe = u.Describe,
			  TelTag = u.TelTag
		  });

        return await query.ToListAsync();
	}

	#region 私有方法

	private async Task<string> CheckInput(SysOrg org)
    {
        //判断账号重复,直接从redis拿
        var user = await GetFirstAsync(x =>x.AreaCode==org.AreaCode);
        if (user != null && user.Id != org.Id)
            return $"存在重复的编码:{org.AreaCode}";

        return "OK";
    }

    private ISugarQueryable<SysOrg> GetQuery(OrgPageInput input)
    {
        var query = Context.Queryable<SysOrg>()
            .WhereIF(input.Expression != null, input.Expression?.ToExpression()) //动态查询
            .WhereIF(!string.IsNullOrEmpty(input.status), u => u.Status == input.status) //根据状态查询
            .WhereIF(!string.IsNullOrEmpty(input.SearchKey),
                u => u.OrgCode.Contains(input.SearchKey) || u.OrgName.Contains(input.SearchKey)) //根据关键字查询
            .OrderByIF(!string.IsNullOrEmpty(input.SortField), $" {input.SortField} {input.SortOrder}")
            .OrderByDescending(u => u.Id) //排序
            .Select(u => new SysOrg
            {
                Id = u.Id.SelectAll()
            })
            .Mapper(u =>
            { });
        return query;
    }

	public async Task<string> UpdateAvatar(BaseFileInput input)
	{
		var orgInfo = await GetByIdAsync(input.Id);
		var file = input.File;
		using var fileStream = file.OpenReadStream(); //获取文件流
		var bytes = new byte[fileStream.Length];
		fileStream.Read(bytes, 0, bytes.Length);
		fileStream.Close();
		var base64String = Convert.ToBase64String(bytes); //转base64
		var avatar = "data:image/png;base64," + base64String; //转图片
		orgInfo.Avatar = avatar;
		await Context.Updateable(orgInfo).UpdateColumns(it => new { it.Avatar }).ExecuteCommandAsync(); //修改logo
		return avatar;
	}


	public async Task<SysOrg> GetSysOrgByCode(string code,string arrcode="")
	{
		var user = await Context.Queryable<SysOrg>()
			.WhereIF(!string.IsNullOrEmpty(code),u => u.OrgCode == code)
            .WhereIF(!string.IsNullOrEmpty(arrcode),u=>u.AreaCode==arrcode).FirstAsync();
		return user;
	}


    /// <summary>
    /// 获取同医院的分院区
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
	public async Task<List<SysOrg>> GetOrgInfoListByOrgkey(string Id)
	{
		var orglist = await Context.Queryable<SysOrg>()
			.Where(u => u.OrgCode == Id).ToListAsync();
		return orglist;
	}

	#endregion
}