﻿[
  {
    "Id": "1099096271012573184",
    "ParentId": "212725263003728",
    "Title": "组织机构",
    "Name": "sysOrg",
    "Code": null,
    "Apis": "/sys/org/page,/sys/org/detail",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/limit/org",
    "Component": "sys/limit/org/index",
    "Icon": "ep:list",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 37,
    "Description": "组织机构",
    "Status": "Y",
    "CreateDate": "2025-05-14T11:33:19.797",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "1099096271175618560",
    "ParentId": "1099096271012573184",
    "Title": "操作机构",
    "Name": null,
    "Code": "czjg",
    "Apis": "/sys/org/add,/sys/org/edit,/sys/org/delete",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2025-05-14T11:33:59.6",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-14T11:35:12.39",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212725263003721",
    "ParentId": null,
    "Title": "系统管理",
    "Name": null,
    "Code": "system",
    "Apis": null,
    "Category": "MODULE",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": "ep:briefcase",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 1,
    "Description": null,
    "Status": "N",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2025-05-17T11:31:43.62",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212725263003722",
    "ParentId": null,
    "Title": "系统首页",
    "Name": "index",
    "Code": "system",
    "Apis": null,
    "Category": "SPA",
    "Module": null,
    "MenuType": "MENU",
    "Path": "/home/<USER>",
    "Component": "home/index",
    "Icon": "ant-design:home-outlined",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": true,
    "IsKeepAlive": false,
    "IsHome": true,
    "SortCode": 1,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212725263003723",
    "ParentId": null,
    "Title": "个人中心",
    "Name": "userCenter",
    "Code": "system",
    "Apis": null,
    "Category": "SPA",
    "Module": null,
    "MenuType": "MENU",
    "Path": "/userCenter",
    "Component": "userCenter/index",
    "Icon": "ep:user",
    "ActiveMenu": null,
    "IsHide": true,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212725263003726",
    "ParentId": "212725263003728",
    "Title": "用户管理",
    "Name": "sysUser",
    "Code": "system",
    "Apis": "/sys/user/page,/sys/user/detail",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/limit/user",
    "Component": "sys/limit/user/index",
    "Icon": "eva:people-outline",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 6,
    "Description": "用户管理",
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-08-01T09:22:02.52",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212725263003728",
    "ParentId": "0",
    "Title": "权限管理",
    "Name": null,
    "Code": "system",
    "Apis": null,
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "CATALOG",
    "Path": "/sys/limit",
    "Component": null,
    "Icon": "eva:shield-outline",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 8,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212725263003729",
    "ParentId": "212725263003728",
    "Title": "角色管理",
    "Name": "sysRole",
    "Code": "system",
    "Apis": "/sys/role/page,/sys/role/resourcetreeselector,/sys/role/ownresource",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/limit/role",
    "Component": "sys/limit/role/index",
    "Icon": "ep:user",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 9,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212725263003730",
    "ParentId": "212725263003728",
    "Title": "模块管理",
    "Name": "sysModule",
    "Code": "system",
    "Apis": "/sys/module/page",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/limit/module",
    "Component": "sys/limit/module/index",
    "Icon": "eva:grid-outline",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 10,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212725263003731",
    "ParentId": "212725263003728",
    "Title": "菜单管理",
    "Name": "sysMenu",
    "Code": "system",
    "Apis": "/sys/menu/menutreeselector,/sys/menu/menuapilist,/sys/menu/moduleselector,/sys/menu/tree",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/limit/menu",
    "Component": "sys/limit/menu/index",
    "Icon": "ep:menu",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 11,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212745263003735",
    "ParentId": "0",
    "Title": "系统运维",
    "Name": null,
    "Code": "system",
    "Apis": null,
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "CATALOG",
    "Path": "/sys/ops",
    "Component": null,
    "Icon": "zondicons:servers",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 15,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-07-29T11:15:35.787",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212745263003736",
    "ParentId": "212745263003735",
    "Title": "数据字典",
    "Name": "sysDict",
    "Code": "system",
    "Apis": "/sys/dict/tree,/sys/dict/page",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/ops/dict",
    "Component": "sys/ops/dict/index",
    "Icon": "ep:notebook",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 16,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-07-29T11:25:15.017",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212745263003737",
    "ParentId": "212745263003735",
    "Title": "系统配置",
    "Name": "sysConfig",
    "Code": "system",
    "Apis": "/sys/config/list,/sys/config/page",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/ops/config",
    "Component": "sys/ops/config/index",
    "Icon": "eva:settings-2-outline",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 17,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-07-29T11:16:09.8",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "212745263003738",
    "ParentId": "0",
    "Title": "日志审计",
    "Name": null,
    "Code": "system",
    "Apis": null,
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "CATALOG",
    "Path": "/sys/audit",
    "Component": null,
    "Icon": "line-md:clipboard-list",
    "ActiveMenu": "",
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 14,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "212755263003740",
    "ParentId": "212745263003738",
    "Title": "操作日志",
    "Name": "sysOpLog",
    "Code": "system",
    "Apis": "/sys/logoperate/page,/sys/logoperate/columnchartdata,/sys/logoperate/piechartdata,/sys/logoperate/detail",
    "Category": "MENU",
    "Module": "212725263003721",
    "MenuType": "MENU",
    "Path": "/sys/audit/oplog",
    "Component": "sys/audit/oplog/index",
    "Icon": "ant-design:solution-outlined",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 19,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2024-06-26T11:35:53.457",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin",
    "ExtJson": null
  },
  {
    "Id": "***************",
    "ParentId": null,
    "Title": "业务管理",
    "Name": null,
    "Code": "biz",
    "Apis": null,
    "Category": "MODULE",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": "ep:briefcase",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 39,
    "Description": "2323",
    "Status": "Y",
    "CreateDate": "2024-06-26T11:35:53.457",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin",
    "ModifyDate": "2025-04-27T16:42:52.96",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "217669778280448",
    "ParentId": "0",
    "Title": "项目管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/projectManagement",
    "Component": null,
    "Icon": "ant-design:profile-outlined",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 50,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-13T15:44:32.673",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:15:51.477",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "217671714164736",
    "ParentId": "217669778280448",
    "Title": "项目分类",
    "Name": "projectClassification",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/projectManagement/projectClassification",
    "Component": "projectManagement/projectClassification/index",
    "Icon": "ant-design:file-text-outlined",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 1,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-13T15:52:25.303",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:17:04.997",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "218407500480512",
    "ParentId": "217669778280448",
    "Title": "组合管理",
    "Name": "combinationManagement",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/projectManagement/combinationManagement",
    "Component": "projectManagement/combinationManagement/index",
    "Icon": "ant-design:reconciliation-twotone",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 2,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-15T17:46:20.633",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:17:41.54",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "***************",
    "ParentId": "0",
    "Title": "个检管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/individualManagement",
    "Component": null,
    "Icon": "line-md:account",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 31,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-16T18:52:05.317",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:15:04.873",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "***************",
    "ParentId": "***************",
    "Title": "个检套餐",
    "Name": "individualPackage",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/individualManagement/individualPackage",
    "Component": "individualManagement/individualPackage/index",
    "Icon": "ant-design:medicine-box-twotone",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 1,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-17T08:40:14.863",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:15:25.11",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "220136605974528",
    "ParentId": "217669778280448",
    "Title": "组合互斥",
    "Name": "combinationMutuallyExclusive",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/projectManagement/combinationMutuallyExclusive",
    "Component": "projectManagement/combinationMutuallyExclusive/index",
    "Icon": "eva:swap-fill",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 3,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-20T15:02:05.53",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-06T18:22:18.637",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "221196900954112",
    "ParentId": "4301149905173295104",
    "Title": "设置加项包",
    "Name": "addPackage",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/packageManagement/addPackage",
    "Component": "packageManagement/addPackage/index",
    "Icon": "eva:folder-add-fill",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 81,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-23T14:56:26.61",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "226162596966400",
    "ParentId": "4301149668770295808",
    "Title": "全局号源",
    "Name": "globalNumberSource",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/sourceManagement/globalNumberSource",
    "Component": "sourceManagement/globalNumberSource/index",
    "Icon": "ep:chrome-filled",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 73,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-06-06T15:41:54.737",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-06T17:27:53.233",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "226172250247168",
    "ParentId": "217669778280448",
    "Title": "组合包含",
    "Name": "combinationEmbody",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/projectManagement/combinationEmbody",
    "Component": "projectManagement/combinationEmbody/index",
    "Icon": "eva:copy-fill",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 5,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-06-06T16:21:11.493",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-06T16:21:26.3",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "3436467717603430400",
    "ParentId": "0",
    "Title": "客户信息管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/members",
    "Component": null,
    "Icon": "ep:avatar",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 100,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-23T15:16:51.897",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-06T13:50:15.993",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "3436467718807556096",
    "ParentId": "3436467717603430400",
    "Title": "客户信息",
    "Name": "membersCards",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/members/membersCards",
    "Component": "members/membersCards/index",
    "Icon": "ep:user-filled",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-23T15:21:45.873",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-07T15:47:02.913",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "3436472650046640128",
    "ParentId": "0",
    "Title": "管理中心",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/noticeAndImage",
    "Component": null,
    "Icon": "ep:files",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 100,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-06-06T13:47:01.667",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "3436472650573152256",
    "ParentId": "3436472650046640128",
    "Title": "轮播图管理",
    "Name": "carouselImage",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/noticeAndImage/carouselImage",
    "Component": "noticeAndImage/carouselImage/index",
    "Icon": "ep:picture-filled",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-06-06T13:49:10.207",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-07T15:47:24.693",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "3724693170648023040",
    "ParentId": "4301154977807425536",
    "Title": "单位管理",
    "Name": "companyManagement",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/groupManagement/companyManagement",
    "Component": "groupManagement/companyManagement/index",
    "Icon": "ep:office-building",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 1,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-09T17:24:41.45",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-22T11:14:36.763",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "3724701983455854592",
    "ParentId": "217669778280448",
    "Title": "组合依赖",
    "Name": "combinationDependence",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/projectManagement/combinationDependence",
    "Component": "projectManagement/combinationDependence/index",
    "Icon": "zondicons:link",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 4,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-06-03T15:04:05.86",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-06-06T17:28:35.153",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149668770295808",
    "ParentId": "0",
    "Title": "号源管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/sourceManagement",
    "Component": null,
    "Icon": "eva:calendar-fill",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 42,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-27T16:54:23.003",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-17T11:20:02.513",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149669082357760",
    "ParentId": "4301149668770295808",
    "Title": "时段管理",
    "Name": "timeSlotManagement",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/sourceManagement/timeSlotManagement",
    "Component": "sourceManagement/timeSlotManagement/index",
    "Icon": "ep:coin",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 76,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-27T16:55:39.193",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-17T11:20:02.513",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149905173295104",
    "ParentId": "0",
    "Title": "体检套餐管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/packageManagement",
    "Component": null,
    "Icon": "ep:briefcase",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-28T08:56:18.58",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-17T11:19:51.433",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149905472917504",
    "ParentId": "4301149905173295104",
    "Title": "设置体检套餐",
    "Name": "packageSetting",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/packageManagement/packageSetting",
    "Component": "packageManagement/packageSetting/index",
    "Icon": "ep:list",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 63,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-28T08:57:31.73",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-17T11:19:51.433",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149915599781888",
    "ParentId": "4301149905173295104",
    "Title": "设置套餐加项包",
    "Name": "addPackageSetting",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/packageManagement/addPackageSetting",
    "Component": "packageManagement/addPackageSetting/index",
    "Icon": "ep:folder-add",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 70,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-28T09:38:44.11",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-23T14:56:37.697",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301149923582435328",
    "ParentId": "4301149905173295104",
    "Title": "体检套餐加项",
    "Name": "addItemSetting",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/packageManagement/addItemSetting",
    "Component": "packageManagement/addItemSetting/index",
    "Icon": "ep:document-add",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 55,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-04-28T10:11:13",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2025-05-21T16:19:37.033",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301154977807425536",
    "ParentId": "0",
    "Title": "团检管理",
    "Name": null,
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "CATALOG",
    "Path": "/groupManagement",
    "Component": null,
    "Icon": "zondicons:user-group",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 29,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-12T16:56:54.647",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|袁丁淦",
    "ModifyDate": "2025-05-17T11:17:07.513",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301154978510389248",
    "ParentId": "4301154977807425536",
    "Title": "团检名单",
    "Name": "unitList",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/groupManagement/unitList",
    "Component": "groupManagement/unitList/index",
    "Icon": "ep:list",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 75,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-12T16:59:46.27",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|袁丁淦",
    "ModifyDate": "2025-05-21T16:20:33.693",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301154979848957952",
    "ParentId": "4301154977807425536",
    "Title": "团检订单",
    "Name": "unitOrder",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/groupManagement/unitOrder",
    "Component": "groupManagement/unitOrder/index",
    "Icon": "ant-design:file-done-outlined",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 37,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-12T17:05:13.067",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|袁丁淦",
    "ModifyDate": "2025-05-21T16:20:06.37",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4301154980677857280",
    "ParentId": "4301154977807425536",
    "Title": "团检套餐",
    "Name": "unitPackage",
    "Code": null,
    "Apis": null,
    "Category": "MENU",
    "Module": "***************",
    "MenuType": "MENU",
    "Path": "/groupManagement/unitPackage",
    "Component": "groupManagement/unitPackage/index",
    "Icon": "ep:briefcase",
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": true,
    "IsHome": false,
    "SortCode": 49,
    "Description": null,
    "Status": "Y",
    "CreateDate": "2025-05-12T17:08:35.437",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|袁丁淦",
    "ModifyDate": "2025-05-21T16:20:22.51",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4621863844208640",
    "ParentId": "212725263003726",
    "Title": "重置密码",
    "Name": null,
    "Code": "czmm",
    "Apis": "/sys/user/resetpassword",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-05T18:22:16.777",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4621864266264576",
    "ParentId": "212725263003726",
    "Title": "用户授权角色",
    "Name": null,
    "Code": "sqjs",
    "Apis": "/sys/user/grantrole,/sys/role/page,/sys/role/roleselector,/sys/user/edit,/sys/user/ownroleroleselect",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-05T18:23:59.813",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2024-08-05T18:28:42.42",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4621864958152704",
    "ParentId": "212725263003726",
    "Title": "操作用户",
    "Name": null,
    "Code": "czyh",
    "Apis": "/sys/user/delete,/sys/user/add,/sys/user/edit,/sys/user/disableuser,/sys/user/enableuser",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-05T18:26:48.733",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2024-08-05T18:28:23.987",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4622174332063744",
    "ParentId": "212725263003729",
    "Title": "操作角色",
    "Name": null,
    "Code": "czjs",
    "Apis": ",/sys/role/add,/sys/role/edit,/sys/role/delete",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T15:25:39.473",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622174615695360",
    "ParentId": "212725263003729",
    "Title": "授权菜单",
    "Name": null,
    "Code": "sqcd",
    "Apis": "/sys/role/roleselector,/sys/role/resourcetreeselector,/sys/role/grantresource,/sys/role/ownresource",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T15:26:48.72",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622177168527360",
    "ParentId": "212725263003730",
    "Title": "操作模块",
    "Name": null,
    "Code": "czmk",
    "Apis": "/sys/module/add,/sys/module/edit,/sys/module/delete",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T15:37:11.97",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622178993311744",
    "ParentId": "212725263003731",
    "Title": "操作菜单",
    "Name": null,
    "Code": "czcd",
    "Apis": "/sys/menu/add,/sys/menu/edit,/sys/menu/delete,/sys/menu/changemodule,/sys/menu/changeapilist",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T15:44:37.473",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": "2024-08-06T15:46:57.873",
    "ModifyUserId": "21*************",
    "ModifyUserName": "superAdmin|超管",
    "ExtJson": null
  },
  {
    "Id": "4622179392212992",
    "ParentId": "212725263003731",
    "Title": "操作按键",
    "Name": null,
    "Code": "czaj",
    "Apis": "/sys/button/page,/sys/button/add,/sys/button/edit,/sys/button/delete,/sys/menu/menuapilist,/sys/menu/changeapilist",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T15:46:14.863",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622185890172928",
    "ParentId": "212755263003740",
    "Title": "操作日志",
    "Name": null,
    "Code": "czrz",
    "Apis": "/sys/logoperate/delete",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T16:12:41.277",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622187344420864",
    "ParentId": "212745263003736",
    "Title": "操作字典",
    "Name": null,
    "Code": "czzd",
    "Apis": "/sys/dict/add,/sys/dict/edit,/sys/dict/delete",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T16:18:36.32",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  },
  {
    "Id": "4622187528589312",
    "ParentId": "212745263003737",
    "Title": "操作配置",
    "Name": null,
    "Code": "czpz",
    "Apis": "/sys/config/add,/sys/config/edit,/sys/config/delete,/sys/config/editbatch",
    "Category": "BUTTON",
    "Module": null,
    "MenuType": null,
    "Path": null,
    "Component": null,
    "Icon": null,
    "ActiveMenu": null,
    "IsHide": false,
    "IsFull": false,
    "IsAffix": false,
    "IsKeepAlive": false,
    "IsHome": false,
    "SortCode": 99,
    "Description": null,
    "Status": null,
    "CreateDate": "2024-08-06T16:19:21.283",
    "CreateUserId": "21*************",
    "CreateUserName": "superAdmin|超管",
    "ModifyDate": null,
    "ModifyUserId": null,
    "ModifyUserName": null,
    "ExtJson": null
  }
]