﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检套餐对应的组合返参
/// </summary>
public class MapClusterCombOutput
{
    /// <summary>
    /// 套餐编码
    /// </summary>
    public string ClusCode { get; set; }

    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

      /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
}
