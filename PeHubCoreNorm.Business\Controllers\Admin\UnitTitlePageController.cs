namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 单位封面控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class UnitTitlePageController : BaseControllerAuthorize
{
    private readonly IUnitTitlePageService _unitTitlePageService;

    public UnitTitlePageController(IUnitTitlePageService unitTitlePageService)
    {
        _unitTitlePageService = unitTitlePageService;
    }

    /// <summary>
    /// 添加单位封面
    /// </summary>
    /// <param name="input">添加参数</param>
    /// <returns></returns>
    [HttpPost("AddUnitTitlePage")]
    [ActionPermission(ActionType.Button, "添加单位封面", "单位封面管理")]
    public async Task<bool> AddUnitTitlePage([FromBody] UnitTitlePageAddInput input)
    {
        return await _unitTitlePageService.AddUnitTitlePage(input);
    }

    /// <summary>
    /// 检查单位封面是否存在
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <param name="excludeId">排除的ID（编辑时使用）</param>
    /// <returns></returns>
    [HttpGet("CheckUnitTitlePageExists")]
    [ActionPermission(ActionType.Query, "检查单位封面是否存在", "单位封面管理")]
    public async Task<bool> CheckUnitTitlePageExists([FromQuery] string companyCode, [FromQuery] int companyTimes, [FromQuery] string excludeId = null)
    {
        return await _unitTitlePageService.CheckUnitTitlePageExists(companyCode, companyTimes, excludeId);
    }

    /// <summary>
    /// 根据ID获取单位封面详情
    /// </summary>
    /// <param name="id">主键ID</param>
    /// <returns></returns>
    [HttpGet("GetUnitTitlePageById")]
    [ActionPermission(ActionType.Query, "单位封面详情查询", "单位封面管理")]
    public async Task<UnitTitlePageOutput> GetUnitTitlePageById([FromQuery] string id)
    {
        return await _unitTitlePageService.GetUnitTitlePageById(id);
    }

    /// <summary>
    /// 根据单位编码和体检次数获取单位封面
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <returns></returns>
    [HttpGet("GetUnitTitlePageByCompany")]
    [ActionPermission(ActionType.Query, "单位封面查询", "单位封面管理")]
    public async Task<UnitTitlePageOutput> GetUnitTitlePageByCompany([FromQuery] string companyCode, [FromQuery] int companyTimes)
    {
        return await _unitTitlePageService.GetUnitTitlePageByCompany(companyCode, companyTimes);
    }

    /// <summary>
    /// 获取单位封面图片
    /// </summary>
    /// <param name="id">主键ID</param>
    /// <returns></returns>
    [HttpGet("GetUnitTitlePageImage")]
    [ActionPermission(ActionType.Query, "获取单位封面图片", "单位封面管理")]
    public async Task<IActionResult> GetUnitTitlePageImage([FromQuery] string id)
    {
        var unitTitlePage = await _unitTitlePageService.GetUnitTitlePageById(id);
        if (unitTitlePage?.ImgData == null || unitTitlePage.ImgData.Length == 0)
        {
            return NotFound("图片不存在");
        }

        // 根据图片数据判断MIME类型
        string contentType = "image/jpeg"; // 默认为jpeg
        if (unitTitlePage.ImgData.Length > 4)
        {
            // PNG文件头：89 50 4E 47
            if (unitTitlePage.ImgData[0] == 0x89 && unitTitlePage.ImgData[1] == 0x50 && 
                unitTitlePage.ImgData[2] == 0x4E && unitTitlePage.ImgData[3] == 0x47)
                contentType = "image/png";
            // GIF文件头：47 49 46 38
            else if (unitTitlePage.ImgData[0] == 0x47 && unitTitlePage.ImgData[1] == 0x49 && 
                     unitTitlePage.ImgData[2] == 0x46 && unitTitlePage.ImgData[3] == 0x38)
                contentType = "image/gif";
        }

        return File(unitTitlePage.ImgData, contentType);
    }
}
