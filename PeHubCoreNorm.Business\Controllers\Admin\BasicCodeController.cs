﻿namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 套餐控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class BasicCodeController : BaseControllerAuthorize
{
    private readonly IBasicCodeService _basicCodeService;

    public BasicCodeController(IBasicCodeService basicCodeService)
    {
        _basicCodeService = basicCodeService;
    }

    #region 体检分类
    /// <summary>
    /// 体检分类查询
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetCodePeCls")]
    [ActionPermission(ActionType.Query, "体检分类查询", "基础业务")]
    public async Task<PeCls[]> GetCodePeCls()
    {
        return await _basicCodeService.GetCodePeCls();
    }

    /// <summary>
    /// 添加体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    [HttpPost("AddCodePeCls")]
    [ActionPermission(ActionType.Button, "添加体检分类", "基础业务")]
    public async Task<bool> AddCodePeCls([FromBody] PeCls peCls)
    {
        return await _basicCodeService.AddCodePeCls(peCls);
    }

    /// <summary>
    /// 编辑体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    [HttpPost("EditCodePeCls")]
    [ActionPermission(ActionType.Button, "编辑体检分类", "基础业务")]
    public async Task<bool> EditCodePeCls([FromBody] PeCls peCls)
    {
        return await _basicCodeService.EditCodePeCls(peCls);
    }

    /// <summary>
    /// 删除体检分类
    /// </summary>
    /// <param name="peCls"></param>
    /// <returns></returns>
    [HttpPost("DeleteCodePeCls")]
    [ActionPermission(ActionType.Button, "删除体检分类", "基础业务")]
    public async Task<bool> DeleteCodePeCls([FromBody] PeCls peCls)
    {
        return await _basicCodeService.DeleteCodePeCls(peCls);
    }
    #endregion

    #region 加项包
    /// <summary>
    /// 加项包查询
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    [HttpPost("GetAddPackage")]
    [ActionPermission(ActionType.Query, "加项包查询","基础业务")]
    public async Task<AddPackageOutput[]> GetAddPackage([FromBody] AddPackageInput addInput)
    {
        return await _basicCodeService.GetAddPackage(addInput);
    }

    /// <summary>
    /// 保存加项包
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    [HttpPost("SaveAddPackage")]
    [ActionPermission(ActionType.Button, "保存加项包", "基础业务")]
    public async Task<bool> SaveAddPackage([FromBody] SaveAddPackageAndDetail addInput)
    {
        return await _basicCodeService.SaveAddPackage(addInput);
    }

    /// <summary>
    /// 删除加项包
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    [HttpPost("DeleteAddPackage")]
    [ActionPermission(ActionType.Button, "删除加项包", "基础业务")]
    public async Task<bool> DeleteAddPackage([FromBody] BaseIdsInput baseId)
    {
        return await _basicCodeService.DeleteAddPackage(baseId);
    }
    #endregion

    #region 加项包明细
    /// <summary>
    /// 加项包明细查询
    /// </summary>
    /// <param name="addDetail"></param>
    /// <returns></returns>
    [HttpPost("GetAddPackageDetail")]
    [ActionPermission(ActionType.Query, "加项包明细查询", "基础业务")]
    public async Task<AddPackageDetailOutput[]> GetAddPackageDetail([FromBody] AddPackageDetailInput addDetail)
    {
        return await _basicCodeService.GetAddPackageDetail(addDetail);
    }

    /// <summary>
    /// 更新加项包明细
    /// </summary>
    /// <param name="addPackages"></param>
    /// <returns></returns>
    [HttpPost("EditAddPackageDetail")]
    [ActionPermission(ActionType.Button, "更新加项包明细", "基础业务")]
    public async Task<bool> EditAddPackageDetail([FromBody] List<AddPackageDetailEditInput> addPackages)
    {
        return await _basicCodeService.EditAddPackageDetail(addPackages);
    }
    #endregion

    #region 项目分类
    /// <summary>
    /// 获取项目分类
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetCodeItemCls")]
    [ActionPermission(ActionType.Query, "获取项目分类", "基础业务")]
    public async Task<CodeItemClsOutput[]> GetCodeItemCls()
    {
        return await _basicCodeService.GetCodeItemCls();
    }

    /// <summary>
    /// 添加项目分类
    /// </summary>
    /// <param name="itemCls"></param>
    /// <returns></returns>
    [HttpPost("AddCodeItemCls")]
    [ActionPermission(ActionType.Button, "添加项目分类", "基础业务")]
    public async Task<bool> AddCodeItemCls([FromBody] CodeItemClsAddInput itemCls)
    {
        return await _basicCodeService.AddCodeItemCls(itemCls);
    }

    /// <summary>
    /// 编辑项目分类
    /// </summary>
    /// <param name="itemCls"></param>
    /// <returns></returns>
    [HttpPost("EditCodeItemCls")]
    [ActionPermission(ActionType.Button, "编辑项目分类", "基础业务")]
    public async Task<bool> EditCodeItemCls([FromBody] CodeItemClsEditInput itemCls)
    {
        return await _basicCodeService.EditCodeItemCls(itemCls);
    }

    /// <summary>
    /// 删除项目分类
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    [HttpPost("DeleteCodeItemCls")]
    [ActionPermission(ActionType.Button, "删除项目分类", "基础业务")]
    public async Task<bool> DeleteCodeItemCls([FromBody] BaseIdsInput baseId)
    {
        return await _basicCodeService.DeleteCodeItemCls(baseId);
    }
    #endregion

    #region 组合管理
    /// <summary>
    /// 获取组合(分页查询)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns> 
    [HttpPost("GetCodeItemComb")]
    [ActionPermission(ActionType.Query, "获取组合", "基础业务")]
    public async Task<SqlSugarPagedList<CodeItemCombOutput>> GetCodeItemComb([FromBody] CodeItemCombPageInput input)
    {
        return await _basicCodeService.GetCodeItemComb(input);
    }

    /// <summary>
    /// 获取组合(不分页查询)
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetAllCodeItemComb")]
    [ActionPermission(ActionType.Query, "获取所有组合", "基础业务")]
    public async Task<CodeItemCombOutput[]> GetAllCodeItemComb()
    {
        return await _basicCodeService.GetAllCodeItemComb();
    }

    /// <summary>
    /// 添加组合
    /// </summary>
    /// <param name="addInput"></param>
    /// <returns></returns>
    [HttpPost("AddCodeItemComb")]
    [ActionPermission(ActionType.Button, "编辑组合", "基础业务")]
    public async Task<bool> AddCodeItemComb([FromBody] CodeItemCombAddInput addInput)
    {
        return await _basicCodeService.AddCodeItemComb(addInput);
    }

    /// <summary>
    /// 编辑组合
    /// </summary>
    /// <param name="editInput"></param>
    /// <returns></returns>
    [HttpPost("EditCodeItemComb")]
    [ActionPermission(ActionType.Button, "编辑项目", "基础业务")]
    public async Task<bool> EditCodeItemComb([FromBody] CodeItemCombEditInput editInput)
    {
        return await _basicCodeService.EditCodeItemComb(editInput);
    }

    /// <summary>
    /// 删除组合
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    [HttpPost("DeleteCodeItemComb")]
    [ActionPermission(ActionType.Button, "删除组合", "基础业务")]
    public async Task<bool> DeleteCodeItemComb([FromBody] BaseIdsInput baseId)
    {
        return await _basicCodeService.DeleteCodeItemComb(baseId);
    }
    #endregion

    #region 个检套餐

    /// <summary>
    /// 获取个检套餐
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    [HttpPost("GetCodeCluster")]
    [ActionPermission(ActionType.Query, "获取个检套餐", "基础业务")]
    public async Task<CodeClusterOutput[]> GetCodeCluster([FromBody] CodeClusterPageInput clusterInput)
    {
        return await _basicCodeService.GetCodeCluster(clusterInput);
    }

    /// <summary>
    /// 获取个检套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    [HttpPost("GetMapClusterComb")]
    [ActionPermission(ActionType.Query, "获取个检套餐对应的组合", "基础业务")]
    public async Task<MapClusterCombOutput[]> GetMapClusterComb([FromBody] MapClusterCombInput clusterInput)
    {
        return await _basicCodeService.GetMapClusterComb(clusterInput);
    }

    /// <summary>
    /// 保存个检套餐信息及对应组合
    /// </summary>
    /// <param name="saveCluster"></param>
    /// <returns></returns>
    [HttpPost("SaveClusterAndMapCombs")]
    [ActionPermission(ActionType.Button, "保存个检套餐信息及对应组合", "基础业务")]
    public async Task<bool> SaveClusterAndMapCombs([FromBody] SaveClusterAndMapComb saveCluster)
    {
        return await _basicCodeService.SaveClusterAndMapCombs(saveCluster);
    }

    /// <summary>
    /// 删除个检套餐
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    [HttpPost("DeleteCodeCluster")]
    [ActionPermission(ActionType.Button, "删除个检套餐", "基础业务")]
    public async Task<bool> DeleteCodeCluster([FromBody] BaseIdsInput baseId)
    {
        return await _basicCodeService.DeleteCodeCluster(baseId);
    }
    #endregion

    #region 个检套餐外的项目
    /// <summary>
    /// 获取个检套餐外的项目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetMapClusterExtraComb")]
    [ActionPermission(ActionType.Query, "获取个检套餐外的项目", "基础业务")]
    public async Task<MapClusterExtraCombOutput[]> GetMapClusterExtraComb([FromBody] MapClusterExtraCombInput input)
    {
        return await _basicCodeService.GetMapClusterExtraComb(input);
    }

    /// <summary>
    /// 保存个检套餐外的项目
    /// </summary>
    /// <param name="extraCombs"></param>
    /// <returns></returns>
    [HttpPost("SaveMapClusterExtraComb")]
    [ActionPermission(ActionType.Button, "保存个检套餐外的项目", "基础业务")]
    public async Task<bool> SaveMapClusterExtraComb([FromBody] ClusterExtraComb[] extraCombs)
    {
        return await _basicCodeService.SaveMapClusterExtraComb(extraCombs);
    }
    #endregion

    #region 个检套餐加项包
    /// <summary>
    /// 套餐加项包查询
    /// </summary>
    /// <param name="clusterAddPkgInput"></param>
    /// <returns></returns>
    [HttpPost("GetClusterAddPackage")]
    [ActionPermission(ActionType.Query, "套餐加项包查询", "基础业务")]
    public async Task<ClusterAddPackageOutput[]> GetClusterAddPackage([FromBody] ClusterAddPackageInput clusterAddPkgInput)
    {
        return await _basicCodeService.GetClusterAddPackage(clusterAddPkgInput);
    }

    /// <summary>
    /// 保存套餐加项包
    /// </summary>
    /// <param name="packages"></param>
    /// <returns></returns>
    [HttpPost("SaveClusterAddPackage")]
    [ActionPermission(ActionType.Button, "保存套餐加项包", "基础业务")]
    public async Task<bool> SaveClusterAddPackage([FromBody] List<ClusterAddPackage> packages)
    {
        return await _basicCodeService.SaveClusterAddPackage(packages);
    }

    /// <summary>
    /// 删除套餐加项包
    /// </summary>
    /// <param name="baseId"></param>
    /// <returns></returns>
    [HttpPost("DeleteClusterAddPackage")]
    [ActionPermission(ActionType.Button, "删除套餐加项包", "基础业务")]
    public async Task<bool> DeleteClusterAddPackage([FromBody] BaseIdsInput baseId)
    {
        return await _basicCodeService.DeleteClusterAddPackage(baseId);
    }
    #endregion

    #region 互斥组合
    /// <summary>
    /// 互斥组合查询
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetMutexCombs")]
    [ActionPermission(ActionType.Query, "互斥组合查询", "基础业务")]
    public async Task<MutexCombOutput[]> GetMutexCombs()
    {
        return await _basicCodeService.GetMutexCombs();
    }

    /// <summary>
    /// 删除互斥代码
    /// </summary>
    /// <param name="mutexCode">删除互斥代码</param>
    /// <returns></returns>
    [HttpPost("DeleteMutexComb")]
    [ActionPermission(ActionType.Button, "删除互斥代码", "基础业务")]
    public async Task<bool> DeleteMutexComb([FromBody] string[] mutexCode)
    {
        return await _basicCodeService.DeleteMutexComb(mutexCode);
    }

    /// <summary>
    /// 获取互斥组合明细
    /// </summary>
    /// <param name="mutexCode">互斥代码</param>
    /// <returns></returns>
    [HttpGet("GetMutexCombDetail")]
    [ActionPermission(ActionType.Query, "获取互斥组合明细", "基础业务")]
    public async Task<MutexCombDetailOutput[]> GetMutexCombDetail([FromQuery] string mutexCode)
    {
        return await _basicCodeService.GetMutexCombDetail(mutexCode);
    }

    /// <summary>
    /// 保存互斥组合明细
    /// </summary>
    /// <param name="mutexCombs"></param>
    /// <returns></returns>
    [HttpPost("SaveMutexCombDetail")]
    [ActionPermission(ActionType.Button, "保存互斥组合明细", "基础业务")]
    public async Task<bool> SaveMutexCombDetail([FromBody] List<MutexCombDetailInput> mutexCombs)
    {
        return await _basicCodeService.SaveMutexCombDetail(mutexCombs);
    }

    #endregion

    #region 组合包含关系
    /// <summary>
    /// 组合包含关系查询
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetCombContain")]
    [ActionPermission(ActionType.Query, "组合包含关系查询", "基础业务")]
    public async Task<MapCombContainOutput[]> GetCombContain()
    {
        return await _basicCodeService.GetCombContain();
    }

    /// <summary>
    /// 删除组合包含关系
    /// </summary>
    /// <param name="combCode"></param>
    /// <returns></returns>
    [HttpPost("DeleteCombContain")]
    [ActionPermission(ActionType.Button, "删除组合包含关系", "基础业务")]
    public async Task<bool> DeleteCombContain([FromBody] string[] combCode)
    {
        return await _basicCodeService.DeleteCombContain(combCode);
    }

    /// <summary>
    /// 获取组合包含关系明细
    /// </summary>
    /// <param name="combCode">组合代码</param>
    /// <returns></returns>
    [HttpGet("GetCombContainDetail")]
    [ActionPermission(ActionType.Query, "获取组合包含关系明细", "基础业务")]
    public async Task<CombContainDetailOutput[]> GetCombContainDetail([FromQuery] string combCode)
    {
        return await _basicCodeService.GetCombContainDetail(combCode);
    }

    /// <summary>
    /// 保存组合包含关系
    /// </summary>
    /// <param name="containDetail"></param>
    /// <returns></returns>
    [HttpPost("SaveCombContainDetail")]
    [ActionPermission(ActionType.Button, "保存组合包含关系", "基础业务")]
    public async Task<bool> SaveCombContainDetail([FromBody] List<CombContainDetailInput> containDetail)
    {
        return await _basicCodeService.SaveCombContainDetail(containDetail);
    }
    #endregion

    #region 组合依赖关系
    /// <summary>
    /// 组合依赖关系查询
    /// </summary>
    /// <returns></returns>
    [HttpPost("GetMapCombDependence")]
    [ActionPermission(ActionType.Query, "组合依赖关系查询", "基础业务")]
    public async Task<MapCombDependenceOutput[]> GetMapCombDependence()
    {
        return await _basicCodeService.GetMapCombDependence();
    }

    /// <summary>
    /// 删除组合依赖关系
    /// </summary>
    /// <param name="combCodes"></param>
    /// <returns></returns>
    [HttpPost("DeleteCombDependence")]
    [ActionPermission(ActionType.Button, "删除组合依赖关系", "基础业务")]
    public async Task<bool> DeleteCombDependence([FromBody] string[] combCodes)
    {
        return await _basicCodeService.DeleteCombDependence(combCodes);
    }

    /// <summary>
    /// 获取组合依赖关系明细
    /// </summary>
    /// <param name="combCode">组合代码</param>
    /// <returns></returns>
    [HttpGet("GetCombDependenceDetail")]
    [ActionPermission(ActionType.Query, "获取组合依赖关系明细", "基础业务")]
    public async Task<CombDependenceDetail[]> GetCombDependenceDetail([FromQuery] string combCode)
    {
        return await _basicCodeService.GetCombDependenceDetail(combCode);
    }

    /// <summary>
    /// 保存组合依赖关系明细
    /// </summary>
    /// <param name="containDetail"></param>
    /// <returns></returns>
    [HttpPost("SaveCombDependenceDetail")]
    [ActionPermission(ActionType.Button, "保存组合依赖关系明细", "基础业务")]
    public async Task<bool> SaveCombDependenceDetail([FromBody] List<CombDependenceDetailInput> containDetail)
    {
        return await _basicCodeService.SaveCombDependenceDetail(containDetail);
    }
    #endregion

    /// <summary>
    /// 获取按项目分类分组的组合
    /// </summary>
    /// <returns></returns>
    [HttpGet("GetCombsByItemClsGroup")]
    [ActionPermission(ActionType.Query, "获取按项目分类分组的组合", "基础业务")]
    public async Task<ItemClsCombOutput[]> GetCombsByItemClsGroup()
    {
        return await _basicCodeService.GetCombsByItemClsGroup();
    }
}