﻿namespace PeHubCoreNorm.Business;


public class MembersInput : BasePageInput
{
    public string? name { get; set; } = "";

    public string? cardNo { get; set; } = "";

    public string? wId { get; set; } = "";

    public string? tel { get; set; } = "";
    //public Expressionable<Members> Expression { get; set; }
}

public class MembersNameInput
{
    public string? name { get; set; }
    public string? id { get; set; }


    //public Expressionable<Members> Expression { get; set; }
}

public class MembersWIdInput
{
    public string wId { get; set; }
	public string orgKey { get; set; }

}


public class AuthFormInput
{
	public string CardNo { get; set; }
	public string Tel { get; set; }
	public string Name { get; set; }
	public string Device { get; set; }
	public string WId { get; set; }
	public string orgKey { get; set; }

 
	/// <summary>
	///     验证码图片，Base64
	/// </summary>
	public string? validCode { get; set; }

	/// <summary>
	///     验证码请求号
	/// </summary>
	public string? validCodeReqNo { get; set; }
}


public class CardsInput
{
    public string cardType { get; set; }

    public string cardNo { get; set; }

    public string tel { get; set; }
  
    public string name { get; set; }

    public string birthDate { get; set; }

    /// <summary>
    /// 性别 1男  2女  0不限（由字典控制）
    /// </summary>
    public string sex { get; set; }

    /// <summary>
    /// 婚姻状况 0未婚  1已婚
    /// </summary>
    public string marital { get; set; }
}

public class UnbindTheCardInput
{
    public string cardId { get; set; }
}
public class UpdateCardsInfoInput: CardsInput
{
    public string id { get; set; }
}

