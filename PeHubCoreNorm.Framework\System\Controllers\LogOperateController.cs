﻿namespace PeHubCoreNorm.System;

[ApiExplorerSettings(GroupName = "System")]
[Route("sys/[controller]")]
[IgnoreLog]
public class LogOperateController : BaseControllerRoleAuthorize
{
    private readonly ILogOperateService _logOperateService;

    public LogOperateController(ILogOperateService operateLogService)
    {
        _logOperateService = operateLogService;
    }

    /// <summary>
    ///     操作日志分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [ActionPermission(ActionType.Query, " 操作日志分页查询", "操作日志")]
    public async Task<dynamic> Page([FromQuery] OperateLogPageInput input)
    {
        return await _logOperateService.Page(input);
    }

    /// <summary>
    ///     操作日志周统计柱状图图
    /// </summary>
    /// <returns></returns>
    [HttpGet("columnChartData")]
    [ActionPermission(ActionType.Query, "操作日志周统计柱状图图", "操作日志")]
    public async Task<dynamic> BarChartData()
    {
        return await _logOperateService.StatisticsByDay(7);
    }

    /// <summary>
    ///     操作日志数量总览饼图
    /// </summary>
    /// <returns></returns>
    [HttpGet("pieChartData")]
    [ActionPermission(ActionType.Query, "操作日志数量总览饼图", "操作日志")]
    public async Task<dynamic> PieChartData()
    {
        return await _logOperateService.TotalCount();
    }

    /// <summary>
    ///     清空日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    [ActionPermission(ActionType.Button, "清空日志", "操作日志")]
    public async Task Delete([FromBody] OperateLogDeleteInput input)
    {
        await _logOperateService.Delete(input.Category);
    }

    /// <summary>
    ///     日志详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("detail")]
    [ActionPermission(ActionType.Query, "日志详情", "操作日志")]
    public async Task<dynamic> Detail([FromQuery] BaseIdInput input)
    {
        return await _logOperateService.Detail(input);
    }
}