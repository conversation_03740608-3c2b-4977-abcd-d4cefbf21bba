﻿using Hangfire;
using Hangfire.SqlServer;

namespace PeHubCoreNorm;

public static partial class ServicesExtension
{
    public static IServiceCollection AddHangfire(this IServiceCollection services)
    {
		services.AddHangfire(x =>
        {

			x.SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
			   .UseColouredConsoleLogProvider()
			   .UseSimpleAssemblyNameTypeSerializer()
			   .UseRecommendedSerializerSettings()
			   //.UseInMemoryStorage()
			   .UseSqlServerStorage(App.Get<string>("Hangfire:dbname"),
			   new SqlServerStorageOptions
			   {
				   QueuePollInterval = TimeSpan.FromSeconds(15),
				   JobExpirationCheckInterval = TimeSpan.FromHours(1),
				   CountersAggregateInterval = TimeSpan.FromMinutes(5),
				   CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
				   SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
				   UseRecommendedIsolationLevel = true,
				   //DisableGlobalLocks = true
			   });
			   //.UseHangfireHttpJob()
        });

		services.AddHangfireServer();
		return services;
    }
}