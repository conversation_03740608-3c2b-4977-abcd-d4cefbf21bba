﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位体检次数返参
/// </summary>
public class CompanyTimeOutput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? BeginDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 线上结束日期
    /// </summary>
    public DateTime? OnLineEndDate { get; set; }
}
