﻿using System.Globalization;
using System.IO;
using Microsoft.AspNetCore.Mvc;

namespace PeHubCoreNorm;

public static class FileInfoExtensions
{
	/// <summary>
	///     下载文件
	/// </summary>
	/// <typeparam name="TAttribute"></typeparam>
	/// <param name="httpContext"></param>
	/// <returns></returns>
	public static FileStreamResult ToFileStreamResult(this FileInfo fileInfo)
    {
        return new FileStreamResult(new FileStream(fileInfo.FullName, FileMode.Open), "application/octet-stream")
        {
            FileDownloadName = fileInfo.Name
        };
    }

	/// <summary>
	///     下载文件
	/// </summary>
	/// <typeparam name="TAttribute"></typeparam>
	/// <param name="httpContext"></param>
	/// <returns></returns>
	public static FileStreamResult ToFileStreamResult(this FileInfo fileInfo, string filedoenLoadName)
    {
        return new FileStreamResult(new FileStream(fileInfo.FullName, FileMode.Open), "application/octet-stream")
        {
            FileDownloadName = filedoenLoadName
        };
    }

	/// <summary>
	///     下载文件
	/// </summary>
	/// <typeparam name="TAttribute"></typeparam>
	/// <param name="httpContext"></param>
	/// <returns></returns>
	public static FileStreamResult ToFileStreamResult(this byte[] data, string filedoenLoadName)
    {
        return new FileStreamResult(new MemoryStream(data), "application/octet-stream")
        {
            FileDownloadName = filedoenLoadName
        };
    }

    public static string ToStringUnit(this long byteCount)
    {
        var array = new string[7] { "B", "KB", "MB", "GB", "TB", "PB", "EB" };
        if (byteCount == 0) return "0" + array[0];

        var num = Math.Abs(byteCount);
        var num2 = Convert.ToInt32(Math.Floor(Math.Log(num, 1024.0)));
        var num3 = Math.Round(num / Math.Pow(1024.0, num2), 1);
        return (Math.Sign(byteCount) * num3).ToString(CultureInfo.InvariantCulture) + " " + array[num2];
    }
}