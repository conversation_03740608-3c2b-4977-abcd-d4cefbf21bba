﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 体检分类
/// </summary>
[SugarTable(TableName = "CodePeCls", TableDescription = "体检分类表")]
public class CodePeCls : PrimaryKeyEntity
{
    /// <summary>
    /// 体检分类代码
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "ClsCode", ColumnDescription = "体检分类代码", Length = 5)]
    public string ClsCode { get; set; }

    /// <summary>
    /// 体检分类名称
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "ClsName", ColumnDescription = "体检分类名称", Length = 50)]
    public string ClsName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnName = "IsEnabled", ColumnDescription = "是否启用", Length = 2)]
    public string IsEnabled { get; set; }
}