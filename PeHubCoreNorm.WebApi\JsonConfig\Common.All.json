{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "JwtSettings": {
    "Key": "K_r_i_n_f_o_w__x_p_e_h_u_b_n_e_w",
    "Issuer": "PeHubCoreNorm",
    "Audience": "PeHubCoreNormAdmin",
    "ExpirationMinutes": 720
  },
  "SystemConfig": {
    "ShowInternalServerError": true,
    "RequestMiddlewareHttpLog": false,
    "DefaultLanguageCode": "zh-cn"
  },
  "Swagger": {
    "Enabled": true,
    //是否启用Swagger
    "ShowWhenDocNamesNotExist": true,
    //如果DocNames没有是否显示
    "DocNames": [
      {
        "Name": "RBAC",
        "Description": "基本RBAC权限管理"
      },
      {
        "Name": "System",
        "Description": "一些系统相关的设定"
      },
      {
        "Name": "Application",
        "Description": "业务模块"
      },
      {
        "Name": "Admin",
        "Description": "后台管理"
      }
    ]
  },
  "Urls": "http://*:8000",
  "CorsUrls": "http://localhost:8888,http://*:8000",
  "PeUrl": "http://*************:8093",
  "Hangfire": {
    "enabled": true,
    "dbname": "Data Source=*************\\MSSQLSERVER2019,21433;Initial Catalog=PeHubHangfire;User ID=sa;Password=****;TrustServerCertificate=true;",
    "pathurl": "/krhf",
    "user": "krinfo",
    "pwd": "****56"
  },
  "SmEncrypt": {
    "SM2": {
      "PublicKey": "04781E6715FF979D66139BA4C38026738224F66B632E2ED2C03250C4AB9F7D33E0DC3858CEE1F54256464AE3288A5AA0725764FB35B0E124617B42814DAE601A86",
      "PrivateKey": "00D60D4B8F18B63AD9D9B4FE39B584C982EF62BD0FAFB5691EA3734AE26B33A7CF"
    },
    "SM4": {
      "Key": "3256485621236595", //密钥长度必须为16字节。
      "Iv": "el88jhtdCk8ubBo7jV8OnAed"
    }
  },
  "RequestDecrypt": {
    //是否开启测试，使用swagger页面测试时开启
    "isText": "0", //isText="1"时，开启
    //用逗号隔开：/dev/Upload/upload,/xx/xx/xx
    "path": "/dev/Upload/upload,/sys/org/updateAvatar"
  }

}