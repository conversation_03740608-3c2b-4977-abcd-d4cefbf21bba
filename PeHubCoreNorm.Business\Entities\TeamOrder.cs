﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 团检订单主表
/// </summary>
[SugarTable("TeamOrder", TableDescription = "团检订单主表")]
public class TeamOrder : PrimaryKeyEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "MembersId", ColumnDescription = "用户表id", Length = 20)]
    public string MembersId { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Name", ColumnDescription = "姓名", Length = 50)]
    public string Name { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CardNo", ColumnDescription = "证件号", Length = 18)]
    public string CardNo { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CardType", ColumnDescription = "证件类型", Length = 5)]
    public string CardType { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Tel", ColumnDescription = "手机号", Length = 32)]
    public string Tel { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "PeClsCode", ColumnDescription = "体检分类代码", Length = 5)]
    public string PeClsCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Birthday", ColumnDescription = "出生日期")]
    public DateTime Birthday { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Sex", ColumnDescription = "性别", Length = 5)]
    public string Sex { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "MarryStatus", ColumnDescription = "婚姻状态", Length = 5)]
    public string? MarryStatus { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyName", ColumnDescription = "单位名称", Length = 100)]
    public string CompanyName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyTimes", ColumnDescription = "单位体检次数")]
    public int CompanyTimes { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 20)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusName", ColumnDescription = "套餐名称", Length = 200)]
    public string ClusName { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "RegNo", ColumnDescription = "体检流水号", Length = 20)]
    public string? RegNo { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "TotalPrice", ColumnDescription = "订单总价格", Length = 10, DecimalDigits = 2)]
    public decimal TotalPrice { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "TimeId", ColumnDescription = "时间段编码Id")]
    public string TimeId { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "TimeSlotName", ColumnDescription = "时间段名称", Length = 50)]
    public string TimeSlotName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "BeginTime", ColumnDescription = "客户体检时间")]
    public DateTime BeginTime { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "CancelTime", ColumnDescription = "客户订单撤销时间")]
    public DateTime? CancelTime { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CreateTime", ColumnDescription = "客户订单下单时间")]
    public DateTime CreateTime { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "EndTime", ColumnDescription = "客户订单完成时间")]
    public DateTime? EndTime { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "IsVip", ColumnDescription = "是否为Vip")]
    public bool? IsVip { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "订单状态")]
    public OrderStatus Status { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "ErrorMsg", ColumnDescription = "错误信息", Length = 200)]
    public string? ErrorMsg { get; set; }
}
