﻿using PeHubCoreNorm.Utils.IOUtils;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business
{
    public class NoticeAndImageService : BizDbRepository<Notice>, INoticeAndImageService
    {
        private readonly ILogger<NoticeAndImageService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="httpClientHelper"></param>
        public NoticeAndImageService(ILogger<NoticeAndImageService> logger)
        {
            _logger = logger;
        }

        #region 轮播图
        /// <summary>
        /// 获取轮播图
        /// </summary>
        /// <param name="imageType"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        public async Task<SqlSugarPagedList<CarouselImage>> GetCarouselImages(CarouselImageInput image)
        {
             return await Context.Queryable<CarouselImage>()
                .WhereIF(!string.IsNullOrEmpty(image.status), x => x.Status == image.status)
                .WhereIF(!string.IsNullOrEmpty(image.imagePage), x => x.ImagePage.Contains(image.imagePage))
                .ToPagedListAsync(image.pageNum, image.pageSize);
        }


        /// <summary>
        /// 根据页面获取轮播图
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public async Task<Object> GetCarouselImagesByPage(string page)
        {
            return Context.Queryable<CarouselImage>().Where(x => x.ImagePage == page&&x.Status=="Y").Select(x=>new { 
            x.Title,
            x.ImageValue,
            x.Id
            }).ToList();
        }

        /// <summary>
        /// 更新轮播图
        /// </summary>
        /// <param name="carouselImage"></param>50732944
        /// <returns></returns>
        public bool UpdateCarouselImages(CarouselImage carouselImage)
        {
            Context.Updateable<CarouselImage>(carouselImage).ExecuteCommandAsync();
            return true;
        }

        /// <summary>
        /// 更新轮播图状态
        /// </summary>
        /// <param name="carouselImage"></param>
        /// <returns></returns>
        public bool UpdateCarouselImageStatus(CarouselImage carouselImage)
        {
            Context.Updateable<CarouselImage>().SetColumns(it => it.Status == carouselImage.Status).Where(it => it.Id == carouselImage.Id).ExecuteCommand();
            return true;
        }


        /// <summary>
        /// 新增轮播图
        /// </summary>
        /// <param name="carouselImage"></param>
        /// <returns></returns>
        public bool AddCarouselImages(CarouselImage carouselImage)
        {
            Context.Insertable<CarouselImage>(carouselImage).ExecuteCommandAsync();
            return true;
        }

        /// <summary>
        /// 删除轮播图
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public bool DeleteCarouselImage(string[] ids)
        {
            Context.Deleteable<CarouselImage>().In(it => it.Id, ids).ExecuteCommand();
            //Context.Deleteable<CarouselImage>().Where(x => x.Id== carouselImage.Id).ExecuteCommand();
            return true;
        }

        #endregion


        #region 体检须知

        /// <summary>
        /// 获取全部体检须知
        /// </summary>
        /// <returns></returns>
        public List<Notice> GetAllNotices()
        {
            return Context.Queryable<Notice>().ToList();
        }



        /// <summary>
        /// 根据Id获取体检须知
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Notice> GetNoticeById(string id)
        {
            return Context.Queryable<Notice>().Single(it => it.Id == id);
        }

        /// <summary>
        /// 获取体检须知(根据TypeCode，Status）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SqlSugarPagedList<Notice>> GetSqlNotices(NoticeInput input)
        {
            return await Context.Queryable<Notice>()
               .WhereIF(!string.IsNullOrEmpty(input.typeCode), x => x.TypeCode == input.typeCode)
                .WhereIF(!string.IsNullOrEmpty(input.status), x => x.Status.Contains(input.status))
                .ToPagedListAsync(input.pageNum, input.pageSize);
        }

        /// <summary>
        /// 根据Id更新条件须知内容
        /// </summary>
        /// <param name="notice"></param>
        /// <returns></returns>
        public bool UpdateNoticeContentById(UpdateNoticeInput notice)
        {
            Context.Updateable<Notice>().SetColumns(it => it.Content == notice.content).Where(it => it.Id == notice.id).ExecuteCommand();
            return true;         
        }


        /// <summary>
        /// 根据id修改体检须知的状态
        /// </summary>
        /// <param name="notice"></param>
        /// <returns></returns>
        public bool UpdateNoticeStatus(UpdateStatusInput notice)
        {
            Context.Updateable<Notice>().SetColumns(it => it.Status == notice.status).Where(it => it.Id == notice.id).ExecuteCommand();
            return true;
        }

        /// <summary>
        /// 新增体检须知
        /// </summary>
        /// <param name="notice"></param>
        /// <returns></returns>
        public bool AddNotices(Notice notice)
        {
            Context.Insertable<Notice>(notice).ExecuteCommandAsync();
            return true;
        }


        /// <summary>
        /// 根据typeCode获取体检须知
        /// </summary>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        public async Task<Notice> GetNoticesByTypeCode(string typeCode)
        {
            return Context.Queryable<Notice>().First(x => x.TypeCode == typeCode && x.Status == "Y");
        }

        #endregion

        #region 资讯
        /// <summary>
        /// 新增资讯
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public bool AddRealTimeInfos(RealTimeInfo info)
        {
            Context.Insertable<RealTimeInfo>(info).ExecuteCommandAsync();
            return true;
        }

        /// <summary>
        /// 获取资讯（筛选、分页,不返回富文本内容）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SqlSugarPagedList<RealTimeInfo>> GetTimeInfos(RealTimeInfoInput input)
        {
            return await Context.Queryable<RealTimeInfo>()
               .WhereIF(!string.IsNullOrEmpty(input.status), x => x.Status == input.status)
               .WhereIF(!string.IsNullOrEmpty(input.title), x => x.Title.Contains(input.title))
               .WhereIF(!string.IsNullOrEmpty(input.typeCode), x => x.TypeCode.Contains(input.typeCode))
               .IgnoreColumns(x=>x.Content)
               .ToPagedListAsync(input.pageNum, input.pageSize);
        }

        /// <summary>
        /// 根据id修改资讯状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public bool UpdateRealTimeInfoStatus(UpdateStatusInput input)
        {
            Context.Updateable<RealTimeInfo>().SetColumns(it => it.Status == input.status).Where(it => it.Id == input.id).ExecuteCommand();
            return true;
        }

        /// <summary>
        /// 根据id更新资讯内容
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public bool UpdateRealTimeInfoContentById(UpdateContentInput input)
        {
            Context.Updateable<RealTimeInfo>().SetColumns(it => it.Content == input.content).Where(it => it.Id == input.id).ExecuteCommand();
            return true;
        }

        /// <summary>
        /// 更新资讯（Title、Status、DiagramUrl、TypeCode）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public bool UpdateRealTimeInfos(UpdateRealTimeInfosInput input)
        {
            var model= Context.Queryable<RealTimeInfo>().First(x=>x.Id == input.id);
            if(!string.IsNullOrEmpty(model.DiagramUrl)&&model.DiagramUrl.Trim()!= input.diagramUrl)
            {
                DeleteRealTimeInfoFiles(model.DiagramUrl);
            }

            Context.Updateable<RealTimeInfo>().SetColumns(it => 
            new RealTimeInfo() { 
                Title = input.title,
                Status = input.status,
                DiagramUrl=input.diagramUrl,
                TypeCode=input.typeCode
            }).Where(it=>it.Id== input.id).ExecuteCommand();
            return true;
        }

        /// <summary>
        /// 删除资讯
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public bool DeleteRealTimeInfo(string[] ids)
        {
            foreach (var id in ids)
            {
                var model = Context.Queryable<RealTimeInfo>().First(x => x.Id == id);
                if (!string.IsNullOrEmpty(model.DiagramUrl))
                {
                    var r= DeleteRealTimeInfoFiles(model.DiagramUrl);
                    if (r.Success)
                    {
                        Context.Deleteable<RealTimeInfo>(model).ExecuteCommand();
                    }
                    else
                    {
                        if(r.Message!= "文件不存在!")
                        {
                            throw new Exception(r.Message);
                        }
                    }
                }
            }
            //Context.Deleteable<RealTimeInfo>().In(it => it.Id, ids).ExecuteCommand();
            return true;
        }

        /// <summary>
        /// 根据typeCode获取num个最新资讯信息
        /// </summary>
        /// <param name="typeCode"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public async Task<Object> GetNewRealTimeInfos(string typeCode,int num)
        {
            return Context.Queryable<RealTimeInfo>()
                .WhereIF(!string.IsNullOrWhiteSpace(typeCode), x => x.TypeCode == typeCode)
                .Where(x => x.Status == "Y").Take(num).OrderBy(st => SqlFunc.Desc(st.CreateDate)).Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.DiagramUrl,
                    x.TypeCode,
                    //x.Content,
                    CreateDate = x.CreateDate.Value.ToString("yyyy-dd-MM HH:mm:ss")
                }).ToList();
        }

        /// <summary>
        /// 根据id获取资讯内容
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<string> GetNewRealTimeContent(string id)
        {
            var model=await Context.Queryable<RealTimeInfo>().FirstAsync(x => x.Id == id);
            return model.Content;
        }

        #endregion


        #region
        /// <summary>
        /// 删除资讯图片
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public FileUploadResponse DeleteRealTimeInfoFiles(string path)
        {
            string filePath = App.WebHostEnvironment.WebRootPath+ "\\Uploads\\" + path.Replace("/nas/", "").Replace('/', '\\');
            var response = new FileUploadResponse();
            try
            {
                // 检查文件是否存在
                if (File.Exists(filePath))
                {
                    // 删除文件
                    File.Delete(filePath);
                    response.Success = true;
                }
                else
                {
                    response.Success = false;
                    response.Message = "文件不存在!";
                }
            }
            catch (IOException ex)
            {
                response.Success = false;
                response.Message = $"删除文件时出错: {ex.Message}";
            }
            catch (UnauthorizedAccessException ex)
            {
                response.Success = false;
                response.Message = $"无权限访问文件: {ex.Message}";
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"发生错误: {ex.Message}";
            }
            return response;
        }

        #endregion



    }
}
