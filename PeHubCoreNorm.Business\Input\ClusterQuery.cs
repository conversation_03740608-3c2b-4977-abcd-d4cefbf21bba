﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 套餐查询入参
/// </summary>
public class ClusterQuery
{
    /// <summary>
    /// 套餐名
    /// </summary>
    public string? ClusName { get; set; }

    /// <summary>
    /// 体检分类
    /// </summary>
    public string? PeCls { get; set; }

    /// <summary>
    /// 年龄下限
    /// </summary>
    public int? LowerAgeLimit { get; set; }

    /// <summary>
    /// 年龄上限
    /// </summary>
    public int? UpperAgeLimit { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Gender { get; set; }
}