﻿using SqlSugar;

namespace PeHubCoreNorm;

/// <summary>
///     主键实体基类
/// </summary>
public abstract class PrimaryKeyEntity
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Id", IsPrimaryKey = true)]
    public virtual string Id { get; set; }

    /// <summary>
    ///     拓展信息
    /// </summary>
    [SugarColumn(ColumnName = "ExtJson", ColumnDescription = "扩展信息", ColumnDataType = StaticConfig.CodeFirst_BigString,
        IsNullable = true)]
    public virtual string ExtJson { get; set; }
}

/// <summary>
///     框架实体基类
/// </summary>
public class BaseEntity : PrimaryKeyEntity
{
    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", IsOnlyIgnoreUpdate = true, IsNullable = true)]
    public virtual DateTime? CreateDate { get; set; }

    /// <summary>
    ///     创建者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "创建者Id", IsOnlyIgnoreUpdate = true, IsNullable = true)]
    public virtual string CreateUserId { get; set; }

    /// 创建人
    /// </summary>
    [SugarColumn(ColumnDescription = "创建人", IsOnlyIgnoreUpdate = true, IsNullable = true)]
    public virtual string CreateUserName { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    [SugarColumn(ColumnDescription = "更新时间", IsOnlyIgnoreInsert = true, IsNullable = true)]
    public virtual DateTime? ModifyDate { get; set; }

    /// <summary>
    ///     修改者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "修改者Id", IsOnlyIgnoreInsert = true, IsNullable = true)]
    public virtual string ModifyUserId { get; set; }

    /// <summary>
    ///     更新人
    /// </summary>
    [SugarColumn(ColumnDescription = "更新人", IsOnlyIgnoreInsert = true, IsNullable = true)]
    public virtual string ModifyUserName { get; set; }
}