﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 加项包明细信息
/// </summary>
[SugarTable("AddPackageDetail", TableDescription = "加项包明细信息")]
public class AddPackageDetail : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "AddPackageCode", ColumnDescription = "加项包编码", Length = 20)]
    public string AddPackageCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }
}