﻿using System.IO;
using System.Runtime.Loader;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;

namespace PeHubCoreNorm;

public static partial class ServicesExtension
{
    public static IServiceCollection AddControllers(this IServiceCollection services)
    {
        services.AddControllers(options =>
            {
                options.Filters.Add<UnifyResultFilter>();

                var actionFilters = App.EffectiveTypes.Where(u =>
                    !u.IsInterface && !u.IsAbstract && u.IsClass && u.GetInterfaces()
                        .Any(i => i.HasImplementedRawGeneric(typeof(IAsyncActionFilter))));

                foreach (var item in actionFilters)
                    options.Filters.Add(item);
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.Converters.Add(new DateTimeConverter());
                options.JsonSerializerOptions.NumberHandling = JsonNumberHandling.AllowNamedFloatingPointLiterals |
                                                               JsonNumberHandling
                                                                   .AllowReadingFromString; // | JsonNumberHandling.WriteAsString;
                options.JsonSerializerOptions.WriteIndented = true;
            }).ConfigureApplicationPartManager(apm => {

				//// 定义插件目录路径
				//string pluginsPath = Path.Combine(AppContext.BaseDirectory, "Plugins");
				//if (!Directory.Exists(pluginsPath)) Directory.CreateDirectory(pluginsPath);

				// 加载所有插件DLL
				foreach (var dll in Directory.GetFiles(Path.Combine(AppContext.BaseDirectory), "*Plugins.dll"))
				{
					try
					{
						// 加载程序集
						Assembly assembly = AssemblyLoadContext.Default.LoadFromAssemblyPath(dll);
						var assemblyPart = new AssemblyPart(assembly); // 关键步骤
						apm.ApplicationParts.Add(assemblyPart);
					}
					catch (Exception ex)
					{
						Console.WriteLine($"控制器加载插件失败: {dll}, 错误: {ex.Message}");
					}
				}
			});

        services.AddRouting(options => options.LowercaseUrls = true);

        return services;
    }
}