﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合依赖关系返参
/// </summary>
public class MapCombDependenceOutput
{
    /// <summary>
    /// 组合代码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 依赖于哪些组合展示
    /// </summary>
    public string DependOnCombs { get; set; }
}

/// <summary>
/// 组合依赖关系明细
/// </summary>
public class CombDependenceDetail 
{
    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    public string CombName { get; set; }

    /// <summary>
    /// 依赖于组合编码
    /// </summary>
    public string DependOnCombCode { get; set; }

    /// <summary>
    /// 依赖于组合名称
    /// </summary>
    public string DependOnCombName { get; set; }
}