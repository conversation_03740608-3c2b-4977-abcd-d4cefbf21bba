﻿using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.UnitPhysicalExamination
{
    public class UnitPhysicalExaminatioService : BizDbRepository<UnitPersonnelList>, IUnitPhysicalExaminationService
    {
        private readonly ILogger<UnitPhysicalExaminatioService> _logger;

        public UnitPhysicalExaminatioService(ILogger<UnitPhysicalExaminatioService> logger)
        {
            _logger = logger;
        }

        public async Task<UnitPersonnelList> AuthenticationToUnti(AuthenticationToUntiInput input)
        {
            var expable = Expressionable.Create<UnitPersonnelList>();
            expable.And(x=>x.CompanyCode==input.CompanyCode);
            expable.And(x => x.BatchNumber == input.BatchNumber);
            expable.And(x => x.IdNumber == input.IdNumber);
            expable.And(x => x.Tel == input.Tel);

            var exp = expable.ToExpression();

            return await Context.Queryable<UnitPersonnelList>().Where(exp).FirstAsync();
        }

        /// <summary>
        /// 根据条件查询单位人员详细信息（包含套餐信息）
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>单位人员详细信息</returns>
        public async Task<List<UnitPersonnelList>> GetUnitPersonnelWithPackageInfo(UnitPersonnelQueryInput input)
        {
            try
            {
                var expable = Expressionable.Create<UnitPersonnelList>();
                expable.And(x => x.CompanyCode == input.CompanyCode);
                expable.And(x => x.BatchNumber == input.BatchNumber);
                expable.And(x => x.IdNumber == input.IdNumber);
                expable.And(x => x.Tel == input.Tel);
                expable.And(x => x.Status == 1);

                var exp = expable.ToExpression();

                return await Context.Queryable<UnitPersonnelList>().Where(exp).ToListAsync();





            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"查询单位人员详细信息失败：CompanyCode={input.CompanyCode}, BatchNumber={input.BatchNumber}, IdNumber={input.IdNumber}, Tel={input.Tel}");
                return null;
            }
        }

  
    }
}
