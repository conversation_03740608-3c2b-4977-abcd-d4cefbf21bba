﻿using PeHubCoreNorm.Business.Input;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.UnitPhysicalExamination
{
    public class UnitPhysicalExaminatioService : BizDbRepository<UnitPersonnelList>, IUnitPhysicalExaminationService
    {
        private readonly ILogger<UnitPhysicalExaminatioService> _logger;

        public UnitPhysicalExaminatioService(ILogger<UnitPhysicalExaminatioService> logger)
        {
            _logger = logger;
        }

        public async Task<UnitPersonnelList> AuthenticationToUnti(AuthenticationToUntiInput input)
        {
            var expable = Expressionable.Create<UnitPersonnelList>();
            expable.And(x=>x.CompanyCode==input.CompanyCode);
            expable.And(x => x.BatchNumber == input.BatchNumber);
            expable.And(x => x.IdNumber == input.IdNumber);
            expable.And(x => x.Tel == input.Tel);

            var exp = expable.ToExpression();

            return await Context.Queryable<UnitPersonnelList>().Where(exp).FirstAsync();
        }
    }
}
