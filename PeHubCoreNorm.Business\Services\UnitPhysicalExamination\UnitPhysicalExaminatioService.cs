using PeHubCoreNorm.Business.Input;
using PeHubCoreNorm.Business.Output;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Services.UnitPhysicalExamination
{
    public class UnitPhysicalExaminatioService : BizDbRepository<UnitPersonnelList>, IUnitPhysicalExaminationService
    {
        private readonly ILogger<UnitPhysicalExaminatioService> _logger;

        public UnitPhysicalExaminatioService(ILogger<UnitPhysicalExaminatioService> logger)
        {
            _logger = logger;
        }

        public async Task<UnitPersonnelList> AuthenticationToUnti(AuthenticationToUntiInput input)
        {
            var expable = Expressionable.Create<UnitPersonnelList>();
            expable.And(x=>x.CompanyCode==input.CompanyCode);
            expable.And(x => x.BatchNumber == input.BatchNumber);
            expable.And(x => x.IdNumber == input.IdNumber);
            expable.And(x => x.Tel == input.Tel);

            var exp = expable.ToExpression();

            return await Context.Queryable<UnitPersonnelList>().Where(exp).FirstAsync();
        }

        /// <summary>
        /// 根据条件查询单位人员详细信息（包含套餐信息）
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>单位人员详细信息</returns>
        public async Task<UnitPersonnelDetailOutput> GetUnitPersonnelWithPackageInfo(UnitPersonnelQueryInput input)
        {
            try
            {
                var result = await Context.Queryable<UnitPersonnelList>()
                    .LeftJoin<CodeCompanyCluster>((personnel, package) => personnel.PackAgeCode == package.ClusCode)
                    .Where((personnel, package) =>
                        personnel.CompanyCode == input.CompanyCode &&
                        personnel.BatchNumber == input.BatchNumber &&
                        personnel.IdNumber == input.IdNumber &&
                        personnel.Tel == input.Tel)
                    .Select((personnel, package) => new UnitPersonnelDetailOutput
                    {
                        Id = personnel.Id,
                        CompanyCode = personnel.CompanyCode,
                        CompanyName = personnel.CompanyName,
                        Department = personnel.Department,
                        Status = personnel.Status,
                        EmployeeName = personnel.EmployeeName,
                        Sex = personnel.Sex,
                        Ethnic = personnel.Ethnic,
                        Married = personnel.Married,
                        Birthday = personnel.Birthday,
                        Age = personnel.Age,
                        Tel = personnel.Tel,
                        IdNumberType = personnel.IdNumberType,
                        IdNumber = personnel.IdNumber,
                        PackAgeCode = personnel.PackAgeCode,
                        PackAgeName = personnel.PackAgeName,
                        BatchNumber = personnel.BatchNumber,
                        StartTime = personnel.StartTime,
                        EndTime = personnel.EndTime,      
                        PackageDetail = package == null ? null : new PackageDetailInfo
                        {
                            ClusCode = package.ClusCode,
                            ClusName = package.ClusName,
                            Price = package.Price,
                            Gender = package.Gender,
                            IsEnabled = package.IsEnabled,
                            PeCls = package.PeCls,
                            AddRule = package.AddRule,
                            IsAgeLimit = package.IsAgeLimit,
                            LowerAgeLimit = package.LowerAgeLimit,
                            UpperAgeLimit = package.UpperAgeLimit,
                            Description = package.Description,
                            Notice = package.Notice,
                            Tag = package.Tag
                        }
                    })
                    .FirstAsync();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"查询单位人员详细信息失败：CompanyCode={input.CompanyCode}, BatchNumber={input.BatchNumber}, IdNumber={input.IdNumber}, Tel={input.Tel}");
                return null;
            }
        }

  
    }
}
