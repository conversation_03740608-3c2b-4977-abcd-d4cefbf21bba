﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位信息
/// </summary>
[SugarTable("Company", TableDescription = "单位信息")]
public class Company : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码", Length = 8)]
    public string CompanyCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CompanyName", ColumnDescription = "单位名称", Length = 100)]
    public string CompanyName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态", Length = 2)]
    public string Status { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Note", ColumnDescription = "备注", Length = 100)]
    public string Note { get; set; }
}