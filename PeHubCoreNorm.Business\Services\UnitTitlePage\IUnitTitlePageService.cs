namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位封面业务接口
/// </summary>
public interface IUnitTitlePageService : ITransient
{
    /// <summary>
    /// 添加单位封面
    /// </summary>
    /// <param name="input">添加参数</param>
    /// <returns></returns>
    Task<bool> AddUnitTitlePage(UnitTitlePageAddInput input);

    /// <summary>
    /// 检查单位封面是否存在
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <param name="excludeId">排除的ID（编辑时使用）</param>
    /// <returns></returns>
    Task<bool> CheckUnitTitlePageExists(string companyCode, int companyTimes, string excludeId = null);

    /// <summary>
    /// 根据ID获取单位封面详情
    /// </summary>
    /// <param name="id">主键ID</param>
    /// <returns></returns>
    Task<UnitTitlePageOutput> GetUnitTitlePageById(string id);

    /// <summary>
    /// 根据单位编码和体检次数获取单位封面
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">体检次数</param>
    /// <returns></returns>
    Task<UnitTitlePageOutput> GetUnitTitlePageByCompany(string companyCode, int companyTimes);
}
