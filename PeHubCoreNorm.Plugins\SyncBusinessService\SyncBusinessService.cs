﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PeHubCoreNorm.Utils;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Services.SyncBusinessService;

/// <summary>
/// 获取体检业务的服务
/// </summary>
public class SyncBusinessService : ISyncBusinessService
{
    private readonly ILogger<SyncBusinessService> _logger;
    private readonly IHttpClientHelper _httpClientHelper;
    private static string PeUrl = App.Get<string>("PeUrl");//体检接口地址

    public SyncBusinessService(
     ILogger<SyncBusinessService> logger,
     IHttpClientHelper httpClientHelper)
    {
        _logger = logger;
        _httpClientHelper = httpClientHelper;
    }

    #region 获取体检单位业务
    /// <summary>
    /// 获取体检单位信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompany()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllCompany}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            return ErrorResponseMsg("获取单位信息异常:"+ ex.Message);
        }
    }

    /// <summary>
    /// 获取体检单位次数信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyTimes()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllCompanyTimes}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            return ErrorResponseMsg("获取单位体检次数信息:"+ex.Message);
        }
    }

    /// <summary>
    /// 获取体检单位套餐信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyCluster()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllCompanyCluster}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            return ErrorResponseMsg("获取体检单位套餐信息失败:"+ex.Message);
        }
    }

    /// <summary>
    /// 获取体检单位套餐对应的组合信息
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetPeCompanyClusterComb()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllMapCompanyClusterComb}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            return ErrorResponseMsg("获取体检单位套餐的组合对应信息失败:"+ex.Message);
        }
    }
    #endregion

    #region 获取体检套餐及对应组合
    /// <summary>
    /// 获取体检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetClusterAndCombs()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetClusterAndCombs}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError("GetClusterAndCombs:{0}", ex.Message);
            return ErrorResponseMsg("同步获取体检套餐及对应组合异常:"+ ex.Message);
        }
    }
    #endregion

    #region 获取体检组合
    /// <summary>
    /// 获取体检组合
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetCodeItemComb()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllComb}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError("SyncCodeItemComb:{0}", ex.Message);
            return ErrorResponseMsg("同步组合信息异常:"+ex.Message);
        }
    }
    #endregion

    #region 获取体检项目分类
    /// <summary>
    /// 获取体检项目分类
    /// </summary>
    /// <returns></returns>
    public async Task<ResponseResult> GetCodeItemCls()
    {
        try
        {
            var requestUrl = $"{PeUrl}{PeInterfaceName.GetAllItemCls}";// 拼接请求地址
            var respData = await _httpClientHelper.PostAsync(requestUrl, "");
            var result = JsonConvert.DeserializeObject<ResponseResult>(respData);
            return result ?? ErrorResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError("SyncCodeItemCls:{0}", ex.Message);
            return ErrorResponseMsg("同步项目分类信息异常:"+ ex.Message);
        }
    }
    #endregion

    #region 私有方法
    private ResponseResult ErrorResponse()
    {
        return new ResponseResult
        {
            success = false,
            returnMsg = "体检返回数据为null"
        };

    }

	private ResponseResult ErrorResponseMsg(string msg)
	{
		return new ResponseResult
		{
			success = false,
			returnMsg = msg
		};
	}
	#endregion
}
