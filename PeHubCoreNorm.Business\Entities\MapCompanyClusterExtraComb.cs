﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检套餐外的项目对应
/// </summary>
[SugarTable(TableName = "MapCompanyClusterExtraComb", TableDescription = "团体套餐外的项目对应")]
public class MapCompanyClusterExtraComb : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 15)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }
}
