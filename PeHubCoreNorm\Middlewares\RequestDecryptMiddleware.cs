﻿using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using PeHubCoreNorm.Utils;
using System;
using System.IO;
using System.Web;

namespace PeHubCoreNorm.Middlewares
{
	public class RequestDecryptMiddleware
	{
		private readonly RequestDelegate _next;

		public RequestDecryptMiddleware(RequestDelegate next)
		{
			_next = next;
		}

		public async Task InvokeAsync(HttpContext context)
		{
			var actionDes = context.GetEndpoint()?.Metadata?.GetMetadata<ControllerActionDescriptor>();
			if (actionDes == null || actionDes.Parameters.Count == 0)
			{
				await _next(context);
				return;
			}

            //是否开启测试，使用swagger页面测试时开启
            string isText = App.Get<string>("RequestDecrypt:isText");
            //特定的路径不需要解密
            string path = App.Get<string>("RequestDecrypt:path");
            if (path.Contains(context.Request.Path)|| isText=="1")
            {
                await _next(context);
                return;
            }
           
            //备份引用
            var originalQueryString = context.Request.QueryString;
			var originalRequestBody = context.Request.Body;

			//创建读写权限流
			var newRequestBody = new MemoryStream();
			var newResponseBody = new MemoryStream();

			context.Request.Body = newRequestBody;

			try
			{
				//Query解密
				if (originalQueryString.HasValue)
				{
					if (!context.Request.Query.ContainsKey("encStr"))
					{
						context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
						context.Response.ContentType = "text/plain; charset=utf-8";
						await context.Response.WriteAsync("解析请求发生错误：加密不符合约定");
						return;
					}
                    //context.Request.QueryString = new QueryString("?" + CryptogramUtil.SM2AndSM4Decrypt(context.Request.Query["encStr"][0]));
                    var jsonDoc = JsonDocument.Parse(CryptogramUtil.SM2AndSM4Decrypt(context.Request.Query["encStr"][0]));
                    string config_params = string.Join("&", jsonDoc.RootElement.EnumerateObject().Select(kv => $"{kv.Name}={kv.Value}"));
                    context.Request.QueryString = new QueryString("?" + config_params);
				}

				//RequestBody解密
				using (StreamReader sr = new(originalRequestBody, null, true, -1, true))
				{
					var bodyCiphertext = await sr.ReadToEndAsync();

					if (!string.IsNullOrEmpty(bodyCiphertext))
					{
						RequestEncStr EncStrObj = JsonConvert.DeserializeObject<RequestEncStr>(bodyCiphertext);

						using (StreamWriter sw = new(newRequestBody, null, -1, true))
						{
							var deText = CryptogramUtil.SM2AndSM4Decrypt(EncStrObj.encStr);

							await sw.WriteAsync(deText);
							sw.Flush();
							newRequestBody.Position = 0;
							//body同时转query的方式
							//if (context.Request.QueryString.Value.Length < 1)
							//{
							//    BodytoQuery(deText, context.Request);
							//}
						}
					}
				}
			}
			catch (Exception exx)
			{
				context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
				context.Response.ContentType = "text/plain; charset=utf-8";
				await context.Response.WriteAsync("解析请求发生错误：加密不符合约定");
				return;
			}

			await _next(context);

			//还原引用
			context.Request.QueryString = originalQueryString;
			context.Request.Body = originalRequestBody;

			//销毁内部创建读写权限的流
			newRequestBody.Dispose();
			newResponseBody.Dispose();
		}
	}

	/// <summary>
	/// 请求体加密字符串对象
	/// </summary>
	internal class RequestEncStr
	{
		public string encStr { get; set; }
	}
}
