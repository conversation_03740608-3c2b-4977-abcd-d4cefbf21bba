﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 计算组合
/// </summary>
public class CalCulateCombs
{
    /// <summary>
    /// 操作(添加组合/删除组合)
    /// </summary>
    public string Operator { get; set; }

    /// <summary>
    /// 组合数据
    /// </summary>
    public List<ReturnCombs> ReturnComb { get; set; }

    /// <summary>
    /// 操作的组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 操作的组合名称
    /// </summary>
    public string CombName { get; set; }
}

/// <summary>
/// 返回的组合
/// </summary>
public class ReturnCombs
{
    /// <summary>
    /// 操作的组合编码
    /// </summary>
    public string CombCode { get; set; }

    /// <summary>
    /// 由什么组合生成的
    /// </summary>
    public List<string> BeFrom { get; set; }

    /// <summary>
    /// depend:依赖、contain:包含
    /// </summary>
    public string DependOrContain { get; set; }
}
