﻿using System.Text.Json.Serialization;

namespace PeHubCoreNorm;

public class DateTimeConverter : JsonConverter<DateTime>
{
    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
		if (reader.TokenType == JsonTokenType.String)
		{
			if (reader.TryGetDateTime(out DateTime dateTime))
			{
				return dateTime;
			}

			var dateString = reader.GetString();
			return DateTime.TryParse(dateString, out dateTime) ? dateTime : default;
		}

		return default;
	}

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
    }
}