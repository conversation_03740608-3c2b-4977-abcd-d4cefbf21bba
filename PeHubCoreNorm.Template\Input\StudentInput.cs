﻿namespace PeHubCoreNorm.Template;

/// <summary>
///     学生分页查询参数
/// </summary>
public class StudentPageInput : BasePageInput
{
}

/// <summary>
///     添加学生参数
/// </summary>
public class StudentAddInput
{
    /// <summary>
    ///     Name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     Age
    /// </summary>
    public string Age { get; set; }
}

/// <summary>
///     修改学生参数
/// </summary>
public class StudentEditInput : StudentAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public string Id { get; set; }
}