﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 个检套餐表
/// </summary>
[SugarTable(TableName = "CodeCompanyCluster", TableDescription = "单位套餐表")]
public class CodeCompanyCluster : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "ClusCode", ColumnDescription = "套餐编码", Length = 20)]
    public string ClusCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClusName", ColumnDescription = "套餐名称", Length = 10)]
    public string ClusName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Gender", ColumnDescription = "性别(0:通用 1:男 2:女)", Length = 2)]
    public string Gender { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsEnabled", ColumnDescription = "是否启用", Length = 2)]
    public string IsEnabled { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "PeCls", ColumnDescription = "体检分类", Length = 2)]
    public string PeCls { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "AddRule", ColumnDescription = "加项规则", Length = 2)]
    public string AddRule { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsAgeLimit", ColumnDescription = "限制年龄标识", Length = 2)]
    public string IsAgeLimit { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "LowerAgeLimit", ColumnDescription = "年龄下限")]
    public int LowerAgeLimit { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "UpperAgeLimit", ColumnDescription = "年龄上限")]
    public int UpperAgeLimit { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Description", ColumnDescription = "套餐简介", Length = 200)]
    public string Description { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Notice", ColumnDescription = "注意事项", Length = 200)]
    public string Notice { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Tag", ColumnDescription = "标签", Length = 200)]
    public string Tag { get; set; }
}
