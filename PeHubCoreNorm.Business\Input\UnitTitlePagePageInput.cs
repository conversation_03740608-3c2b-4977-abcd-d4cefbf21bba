namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位封面添加输入
/// </summary>
public class UnitTitlePageAddInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    [Required(ErrorMessage = "单位编码不能为空")]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 体检次数
    /// </summary>
    [Required(ErrorMessage = "体检次数不能为空")]
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 体检有效期限
    /// </summary>
    [Required(ErrorMessage = "体检有效期限不能为空")]
    public string ValidityPeriod { get; set; }

    /// <summary>
    /// 须知
    /// </summary>
    [Required(ErrorMessage = "须知不能为空")]
    public string Notes { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    [Required(ErrorMessage = "标题不能为空")]
    public string Title { get; set; }

    /// <summary>
    /// 封面图片数据(Base64字符串)
    /// </summary>
    [Required(ErrorMessage = "封面图片数据不能为空")]
    public string ImgData { get; set; }

    /// <summary>
    /// 文件大小
    /// </summary>
    [Required(ErrorMessage = "文件大小不能为空")]
    public int FileSize { get; set; }
}

/// <summary>
/// 单位封面编辑输入
/// </summary>
public class UnitTitlePageEditInput : UnitTitlePageAddInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public string Id { get; set; }
}

/// <summary>
/// 单位封面查询输入
/// </summary>
public class UnitTitlePageQueryInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 体检次数
    /// </summary>
    public int? CompanyTimes { get; set; }
}