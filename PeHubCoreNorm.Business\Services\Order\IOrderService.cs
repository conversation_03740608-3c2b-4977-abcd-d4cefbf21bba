namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单业务接口
/// </summary>
public interface IOrderService : ITransient
{
    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> AddPersonOrder(PersonOrderInput input);

    /// <summary>
    /// 取消个检未支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CancelNoPayPersonOrder(CancelOrder input);

    /// <summary>
    /// 取消个检支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CancelPayPersonOrder(CancelOrder input);

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderList>> GetOrderList(OrderQuery query);

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query);

    #region 团检订单相关接口

    /// <summary>
    /// 添加团检订单
    /// </summary>
    /// <param name="input">团检订单输入</param>
    /// <returns></returns>
    Task<bool> AddTeamOrder(TeamOrderInput input);

    /// <summary>
    /// 批量添加团检订单
    /// </summary>
    /// <param name="input">批量团检订单输入</param>
    /// <returns></returns>
    Task<BatchTeamOrderResult> AddBatchTeamOrder(BatchTeamOrderInput input);

    /// <summary>
    /// 编辑团检订单
    /// </summary>
    /// <param name="input">团检订单编辑输入</param>
    /// <returns></returns>
    Task<bool> EditTeamOrder(TeamOrderEditInput input);

    /// <summary>
    /// 取消团检订单
    /// </summary>
    /// <param name="input">取消团检订单输入</param>
    /// <returns></returns>
    Task<bool> CancelTeamOrder(CancelTeamOrderInput input);

    /// <summary>
    /// 获取团检订单分页列表
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns></returns>
    Task<SqlSugarPagedList<TeamOrderBriefOutput>> GetTeamOrderPageList(TeamOrderQueryInput input);

    /// <summary>
    /// 根据ID获取团检订单详情
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <returns></returns>
    Task<TeamOrderOutput> GetTeamOrderById(string orderId);

    /// <summary>
    /// 获取团检订单统计信息
    /// </summary>
    /// <param name="companyCode">单位编码</param>
    /// <param name="companyTimes">单位体检次数</param>
    /// <returns></returns>
    Task<TeamOrderStatisticsOutput> GetTeamOrderStatistics(string companyCode, int? companyTimes = null);

    /// <summary>
    /// 批量取消团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <param name="cancelReason">取消原因</param>
    /// <returns></returns>
    Task<BatchTeamOrderResult> BatchCancelTeamOrder(List<string> orderIds, string cancelReason);

    /// <summary>
    /// 导入团检订单
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <returns></returns>
    Task<BatchTeamOrderResult> ImportTeamOrders(List<string> orderIds);

    #endregion
}
