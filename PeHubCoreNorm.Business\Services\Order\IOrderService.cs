﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单业务接口
/// </summary>
public interface IOrderService : ITransient, ITenantDBTransient
{
    /// <summary>
    /// 添加个检订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> AddPersonOrder(PersonOrderInput input);

    /// <summary>
    /// 取消个检未支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CancelNoPayPersonOrder(CancelOrder input);

    /// <summary>
    /// 取消个检支付订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CancelPayPersonOrder(CancelOrder input);

    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderList>> GetOrderList(OrderQuery query);

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query);
}
