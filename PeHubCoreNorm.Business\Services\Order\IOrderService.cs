﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 订单业务接口
/// </summary>
public interface IOrderService : ITransient
{
    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderList>> GetOrderList(OrderQuery query);

    /// <summary>
    /// 获取订单明细
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<List<OrderDetail>> GetOrderDetail(OrderDetailQuery query);
}
