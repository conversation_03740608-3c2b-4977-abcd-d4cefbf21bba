# 团检订单服务系统说明

## 系统概述

团检订单服务系统是体检预约系统的重要组成部分，专门处理企业团体体检订单的管理。系统支持单个和批量团检订单的创建、编辑、取消、查询、统计等功能，为企业团体体检提供完整的订单管理解决方案。

## 核心功能

### 1. 订单管理
- **单个订单创建** - 创建单个团检订单
- **批量订单创建** - 批量创建团检订单，支持Excel导入
- **订单编辑** - 编辑已预约状态的团检订单
- **订单取消** - 取消团检订单，支持退费处理
- **订单查询** - 多条件查询团检订单
- **订单详情** - 查看团检订单详细信息

### 2. 批量操作
- **批量添加** - 批量添加团检订单
- **批量取消** - 批量取消团检订单
- **批量导入** - 将已支付订单导入体检系统
- **批量删除** - 删除已取消的团检订单

### 3. 统计分析
- **订单统计** - 按单位、时间等维度统计订单数据
- **状态统计** - 统计各种订单状态的数量
- **金额统计** - 统计订单总金额和已收金额
- **完成率分析** - 分析订单完成率和支付率

### 4. 权限控制
- **管理端权限** - 管理员可以管理所有团检订单
- **用户端权限** - 用户只能管理自己的团检订单
- **操作权限** - 不同操作需要不同的权限级别

## 文件结构

### 输入模型
- `TeamOrderInput.cs` - 团检订单输入模型
- `TeamOrderEditInput.cs` - 团检订单编辑输入
- `TeamOrderQueryInput.cs` - 团检订单查询输入
- `CancelTeamOrderInput.cs` - 取消团检订单输入
- `BatchTeamOrderInput.cs` - 批量团检订单输入

### 输出模型
- `TeamOrderOutput.cs` - 团检订单输出模型
- `TeamOrderBriefOutput.cs` - 团检订单简要输出
- `TeamOrderStatisticsOutput.cs` - 团检订单统计输出
- `BatchTeamOrderResult.cs` - 批量操作结果输出

### 服务层
- `IOrderService.cs` - 订单服务接口（已扩展团检方法）
- `OrderService.cs` - 订单服务实现（已扩展团检方法）

### 控制器
- `Admin/TeamOrderController.cs` - 管理端团检订单控制器
- `AppWeb/TeamOrderController.cs` - 前端应用团检订单控制器
- `AppWeb/OrderController.cs` - 前端应用订单控制器（已扩展）

### 实体类
- `TeamOrder.cs` - 团检订单实体（已存在）
- `OrderDetail.cs` - 订单详情实体（共用）

## API 接口

### 管理端接口 (Admin)

#### 1. 添加团检订单
```
POST /admin/TeamOrder/AddTeamOrder
```

**请求参数**:
```json
{
  "membersId": "用户ID",
  "name": "张三",
  "cardNo": "身份证号",
  "cardType": "1",
  "tel": "13800138000",
  "peClsCode": "体检分类代码",
  "birthday": "1990-01-01",
  "sex": "1",
  "marryStatus": "1",
  "companyCode": "COMP001",
  "companyName": "测试公司",
  "companyTimes": 1,
  "clusCode": "CLUS001",
  "clusName": "基础体检套餐",
  "totalPrice": 500.00,
  "timeId": "TIME001",
  "timeSlotName": "上午8:00-12:00",
  "beginTime": "2024-02-01T08:00:00",
  "orderDetail": [
    {
      "combCode": "COMB001",
      "combName": "基础检查",
      "price": 300.00,
      "combType": 1,
      "orderType": 2
    }
  ]
}
```

#### 2. 批量添加团检订单
```
POST /admin/TeamOrder/AddBatchTeamOrder
```

#### 3. 获取团检订单分页列表
```
POST /admin/TeamOrder/GetTeamOrderPageList
```

#### 4. 获取团检订单统计
```
GET /admin/TeamOrder/GetTeamOrderStatistics?companyCode=COMP001&companyTimes=1
```

#### 5. 批量取消团检订单
```
POST /admin/TeamOrder/BatchCancelTeamOrder
```

#### 6. 导入团检订单
```
POST /admin/TeamOrder/ImportTeamOrders
```

### 前端应用接口 (AppWeb)

#### 1. 添加团检订单
```
POST /appweb/TeamOrder/AddTeamOrder
```

#### 2. 获取我的团检订单
```
POST /appweb/TeamOrder/GetMyTeamOrders
```

#### 3. 取消团检订单
```
POST /appweb/TeamOrder/CancelTeamOrder
```

#### 4. 检查团检预约资格
```
GET /appweb/TeamOrder/CheckCanBookTeamOrder?cardNo=xxx&companyCode=xxx&companyTimes=1
```

#### 5. 获取团检订单支付信息
```
GET /appweb/TeamOrder/GetTeamOrderPaymentInfo?orderId=xxx
```

## 订单状态流转

```
已预约 → 已支付 → 已导入 → 已完成
   ↓        ↓
已取消    已退费
```

### 状态说明
- **已预约**: 订单创建成功，等待支付
- **已支付**: 订单已支付，等待导入体检系统
- **已导入**: 订单已导入体检系统，可以进行体检
- **已完成**: 体检完成
- **已取消**: 订单被取消（未支付状态）
- **已退费**: 订单被取消并已退费（已支付状态）

## 业务规则

### 1. 订单创建规则
- 同一人员在同一单位同一次体检中只能有一个有效订单
- 订单详情不能为空
- 总价大于3000元的订单自动标记为VIP

### 2. 订单取消规则
- 只能取消体检日期之前的订单
- 已支付订单取消需要退费
- 已完成订单不能取消

### 3. 订单编辑规则
- 只有已预约状态的订单才能编辑
- 编辑后需要重新验证业务规则

### 4. 批量操作规则
- 批量操作支持部分成功，返回详细的成功和失败信息
- 批量导入只能导入已支付的订单
- 批量删除只能删除已取消或已退费的订单

## 使用示例

### JavaScript 调用示例

```javascript
// 添加团检订单
async function addTeamOrder() {
    const orderData = {
        name: '张三',
        cardNo: '身份证号',
        cardType: '1',
        tel: '13800138000',
        peClsCode: 'PE001',
        birthday: '1990-01-01',
        sex: '1',
        companyCode: 'COMP001',
        companyName: '测试公司',
        companyTimes: 1,
        clusCode: 'CLUS001',
        clusName: '基础体检套餐',
        totalPrice: 500.00,
        timeId: 'TIME001',
        timeSlotName: '上午8:00-12:00',
        beginTime: '2024-02-01T08:00:00',
        orderDetail: [
            {
                combCode: 'COMB001',
                combName: '基础检查',
                price: 300.00,
                combType: 1,
                orderType: 2
            }
        ]
    };

    const response = await fetch('/appweb/TeamOrder/AddTeamOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(orderData)
    });

    const result = await response.json();
    if (result.data) {
        console.log('团检订单创建成功');
    }
}

// 获取我的团检订单
async function getMyTeamOrders() {
    const queryData = {
        pageNum: 1,
        pageSize: 10,
        status: 1 // 已预约状态
    };

    const response = await fetch('/appweb/TeamOrder/GetMyTeamOrders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(queryData)
    });

    const result = await response.json();
    console.log('我的团检订单:', result.data);
}

// 批量添加团检订单
async function batchAddTeamOrders() {
    const batchData = {
        companyCode: 'COMP001',
        companyName: '测试公司',
        companyTimes: 1,
        beginTime: '2024-02-01T08:00:00',
        timeId: 'TIME001',
        timeSlotName: '上午8:00-12:00',
        teamOrders: [
            // 订单数组
        ]
    };

    const response = await fetch('/admin/TeamOrder/AddBatchTeamOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify(batchData)
    });

    const result = await response.json();
    console.log('批量添加结果:', result.data);
}
```

### C# 调用示例

```csharp
// 添加团检订单
var teamOrderInput = new TeamOrderInput
{
    MembersId = "用户ID",
    Name = "张三",
    CardNo = "身份证号",
    CardType = "1",
    Tel = "13800138000",
    PeClsCode = "PE001",
    Birthday = new DateTime(1990, 1, 1),
    Sex = "1",
    CompanyCode = "COMP001",
    CompanyName = "测试公司",
    CompanyTimes = 1,
    ClusCode = "CLUS001",
    ClusName = "基础体检套餐",
    TotalPrice = 500.00m,
    TimeId = "TIME001",
    TimeSlotName = "上午8:00-12:00",
    BeginTime = new DateTime(2024, 2, 1, 8, 0, 0),
    OrderDetail = new List<TeamOrderDetailInput>
    {
        new TeamOrderDetailInput
        {
            CombCode = "COMB001",
            CombName = "基础检查",
            Price = 300.00m,
            CombType = 1,
            OrderType = 2
        }
    }
};

var result = await _orderService.AddTeamOrder(teamOrderInput);

// 获取团检订单统计
var statistics = await _orderService.GetTeamOrderStatistics("COMP001", 1);
Console.WriteLine($"总订单数: {statistics.TotalCount}");
Console.WriteLine($"完成率: {statistics.CompletionRate}%");
```

## 注意事项

1. **数据一致性**: 团检订单涉及多个表的操作，需要使用事务确保数据一致性
2. **权限控制**: 前端接口需要严格控制用户只能操作自己的订单
3. **状态验证**: 每个操作都需要验证订单当前状态是否允许该操作
4. **批量操作**: 批量操作需要考虑性能和错误处理
5. **号源管理**: 订单操作需要同步更新号源信息（TODO项）
6. **支付集成**: 需要集成微信支付等第三方支付接口
7. **日志记录**: 重要操作需要记录详细日志便于问题排查
8. **异常处理**: 完善的异常处理机制，给用户友好的错误提示

## 扩展功能

1. **Excel导入导出**: 支持Excel格式的订单批量导入和导出
2. **二维码生成**: 为已支付订单生成体检二维码
3. **消息通知**: 订单状态变更时发送短信或微信通知
4. **报表统计**: 更丰富的统计报表和图表展示
5. **审批流程**: 大额订单或特殊情况的审批流程
6. **自动化处理**: 定时任务处理过期订单等自动化功能
