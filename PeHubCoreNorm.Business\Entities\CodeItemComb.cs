﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 组合表
/// </summary>
[SugarTable(TableName = "CodeItemComb", TableDescription = "项目表")]
public class CodeItemComb : BaseEntity
{
    [SugarColumn(IsNullable = false, ColumnName = "CombCode", ColumnDescription = "组合编码", Length = 10)]
    public string CombCode { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "CombName", ColumnDescription = "组合名称", Length = 200)]
    public string CombName { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Gender", ColumnDescription = "性别(0:通用 1:男 2:女)", Length = 2)]
    public string Gender { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Price", ColumnDescription = "价格", Length = 9, DecimalDigits = 2)]
    public decimal Price { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "Status", ColumnDescription = "状态",Length = 2)]
    public string Status { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "IsPriority", ColumnDescription = "独立放号")]
    public bool IsPriority { get; set; }

    [SugarColumn(IsNullable = false, ColumnName = "ClsCode", ColumnDescription = "项目分类代码", Length = 10)]
    public string ClsCode { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Description", ColumnDescription = "组合描述", Length = 200)]
    public string Description { get; set; }

    [SugarColumn(IsNullable = true, ColumnName = "Attention", ColumnDescription = "注意事项", Length = 200)]
    public string Attention { get; set; }
}
