﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 用户表关联卡列表
/// </summary>
[SugarTable(TableName = "MapMembersIdCardId", TableDescription = "用户表关联卡列表")]
public class MapMembersIdCardId : BaseEntity
{
    /// <summary>
    /// 用户表id
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public string MembersId { get; set; }

    /// <summary>
    /// 卡列表id
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string CardId { get; set; }

}
