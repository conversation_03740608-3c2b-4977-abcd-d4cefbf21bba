﻿namespace PeHubCoreNorm.Business;

public class TimeSlotInput
{
    /// <summary>
    /// 号源类型编码
    /// </summary>
    [Required(ErrorMessage = "号源类型编码不能为空")]
    public string SourceTypeID { get; set; }
}

public class AddTimeSlotEntryInput
{
    /// <summary>
    /// 
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 时段全称（08:00-09:00）
    /// </summary>
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    /// 上下午标识（上午：AM,下午：PM,全天：ALL）
    /// </summary>
    public string TimePeriod { get; set; }
}

public class EditTimeSlotEntryInput : AddTimeSlotEntryInput
{
}

public class DeleteTimeSlotEntryInput
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [MinLength(0)]
    public string[] Ids { get; set; }

}

public class AddTimeSlotByTypeInput
{
    /// <summary>
    /// 
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// 号源类型编码
    /// </summary>
    public string SourceTypeID { get; set; }
    /// <summary>
    ///  时段ID
    /// </summary>
    public string TimeSlotEntryID { get; set; }

}

public class EditTimeSlotByTypeInput
{
    /// <summary>
    /// 号源类型编码
    /// </summary>
    public string SourceTypeID { get; set; }
    /// <summary>
    ///  时段ID
    /// </summary>
    public string[] TimeSlotEntryID { get; set; }
}

public class DeleteTimeSlotByTypeInput
{
    public string Id { get; set; }
}
