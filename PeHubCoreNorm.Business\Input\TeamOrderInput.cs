namespace PeHubCoreNorm.Business;

/// <summary>
/// 团检订单输入
/// </summary>
public class TeamOrderInput
{
    /// <summary>
    /// 用户id
    /// </summary>
    [Required(ErrorMessage = "用户id不能为空")]
    public string MembersId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    [Required(ErrorMessage = "证件号不能为空")]
    [StringLength(18, ErrorMessage = "证件号长度不能超过18个字符")]
    public string CardNo { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [Required(ErrorMessage = "证件类型不能为空")]
    public string CardType { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [Required(ErrorMessage = "手机号不能为空")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入有效的手机号码")]
    public string Tel { get; set; }

    /// <summary>
    /// 体检分类代码
    /// </summary>
    [Required(ErrorMessage = "体检分类代码不能为空")]
    public string PeClsCode { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    [Required(ErrorMessage = "出生日期不能为空")]
    public DateTime Birthday { get; set; }

    /// <summary>
    /// 性别(1:男 2:女)
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public string Sex { get; set; }

    /// <summary>
    /// 婚姻状态
    /// </summary>
    public string MarryStatus { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    [Required(ErrorMessage = "单位编码不能为空")]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [Required(ErrorMessage = "单位名称不能为空")]
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    [Required(ErrorMessage = "单位体检次数不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "单位体检次数必须大于0")]
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 套餐编码
    /// </summary>
    [Required(ErrorMessage = "套餐编码不能为空")]
    public string ClusCode { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    [Required(ErrorMessage = "套餐名称不能为空")]
    public string ClusName { get; set; }

    /// <summary>
    /// 订单总价格
    /// </summary>
    [Required(ErrorMessage = "订单总价格不能为空")]
    [Range(0, double.MaxValue, ErrorMessage = "订单总价格不能为负数")]
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 时间段编码Id
    /// </summary>
    [Required(ErrorMessage = "时间段编码Id不能为空")]
    public string TimeId { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    [Required(ErrorMessage = "时间段名称不能为空")]
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 客户体检时间
    /// </summary>
    [Required(ErrorMessage = "客户体检时间不能为空")]
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 订单详情
    /// </summary>
    public List<TeamOrderDetailInput> OrderDetail { get; set; } = new List<TeamOrderDetailInput>();
}

/// <summary>
/// 团检订单明细输入
/// </summary>
public class TeamOrderDetailInput
{
    /// <summary>
    /// 组合编码
    /// </summary>
    [Required(ErrorMessage = "组合编码不能为空")]
    public string CombCode { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    [Required(ErrorMessage = "组合名称不能为空")]
    public string CombName { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    [Required(ErrorMessage = "价格不能为空")]
    [Range(0, double.MaxValue, ErrorMessage = "价格不能为负数")]
    public decimal Price { get; set; }

    /// <summary>
    /// 组合类型(1:套餐 2:加项包)
    /// </summary>
    [Required(ErrorMessage = "组合类型不能为空")]
    [Range(1, 2, ErrorMessage = "组合类型只能是1或2")]
    public int CombType { get; set; }

    /// <summary>
    /// 订单类型(1:个人 2:团体)
    /// </summary>
    [Required(ErrorMessage = "订单类型不能为空")]
    public int OrderType { get; set; } = 2; // 默认为团体订单
}

/// <summary>
/// 团检订单编辑输入
/// </summary>
public class TeamOrderEditInput : TeamOrderInput
{
    /// <summary>
    /// 订单ID
    /// </summary>
    [Required(ErrorMessage = "订单ID不能为空")]
    public string Id { get; set; }
}

/// <summary>
/// 团检订单查询输入
/// </summary>
public class TeamOrderQueryInput : BasePageInput
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string MembersId { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    public int? CompanyTimes { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus? Status { get; set; }

    /// <summary>
    /// 体检人姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 体检开始日期
    /// </summary>
    public DateTime? BeginTimeStart { get; set; }

    /// <summary>
    /// 体检结束日期
    /// </summary>
    public DateTime? BeginTimeEnd { get; set; }
}

/// <summary>
/// 取消团检订单输入
/// </summary>
public class CancelTeamOrderInput
{
    /// <summary>
    /// 订单Id
    /// </summary>
    [Required(ErrorMessage = "订单Id不能为空")]
    public string OrderId { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    [Required(ErrorMessage = "用户Id不能为空")]
    public string MemberId { get; set; }

    /// <summary>
    /// 取消原因
    /// </summary>
    [StringLength(200, ErrorMessage = "取消原因不能超过200个字符")]
    public string CancelReason { get; set; }
}

/// <summary>
/// 批量团检订单输入
/// </summary>
public class BatchTeamOrderInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    [Required(ErrorMessage = "单位编码不能为空")]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [Required(ErrorMessage = "单位名称不能为空")]
    public string CompanyName { get; set; }

    /// <summary>
    /// 单位体检次数
    /// </summary>
    [Required(ErrorMessage = "单位体检次数不能为空")]
    public int CompanyTimes { get; set; }

    /// <summary>
    /// 体检时间
    /// </summary>
    [Required(ErrorMessage = "体检时间不能为空")]
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 时间段编码Id
    /// </summary>
    [Required(ErrorMessage = "时间段编码Id不能为空")]
    public string TimeId { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    [Required(ErrorMessage = "时间段名称不能为空")]
    public string TimeSlotName { get; set; }

    /// <summary>
    /// 团检订单列表
    /// </summary>
    [Required(ErrorMessage = "团检订单列表不能为空")]
    public List<TeamOrderInput> TeamOrders { get; set; } = new List<TeamOrderInput>();
}
