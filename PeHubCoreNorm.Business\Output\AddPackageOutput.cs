﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 加项包出参
/// </summary>
public class AddPackageOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 加项包编码
    /// </summary>
    public string AddPackageCode { get; set; }

    /// <summary>
    /// 加项包名称
    /// </summary>
    public string AddPackageName { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string Introduce { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 单位标识
    /// </summary>
    public string IsCompany { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string CompanyName { get; set; }

    /// <summary>
    /// 可选项目数量
    /// </summary>
    public int OptionalQuantity { get; set; }

    /// <summary>
    /// 状态(启用/禁用)
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime? CreateDate { get; set; }
}
