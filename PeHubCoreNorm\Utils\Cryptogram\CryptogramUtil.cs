﻿using NewLife;
using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Utilities.Encoders;
using SixLabors.ImageSharp.Drawing;

namespace PeHubCoreNorm.Utils;

/// <summary>
/// 加解密功能
/// </summary>
public class CryptogramUtil
{
    #region SM2

    /// <summary>
    /// SM2解密
    /// </summary>
    /// <param name="str">密文</param>
    /// <returns>明文</returns>
    public static string Sm2Decrypt(string str)
    {
 
        try
        {
			// 解密
			if (!string.IsNullOrWhiteSpace(str))
				return SM2Util.Decrypt(str);
			// // 解密
			// if (!string.IsNullOrWhiteSpace(str))
			//     return SM2Util.Decrypt(str);
		}
        catch
        {
            return "";
        }
        return "";
    }

    /// <summary>
    /// SM2加密
    /// </summary>
    /// <param name="str">明文</param>
    /// <returns>密文</returns>
    public static string Sm2Encrypt(string str)
    {
        try
        {
            // 加密
            if (!string.IsNullOrWhiteSpace(str))
                return SM2Util.Encrypt(str);
        }
        catch
        {
            return "";
        }
        return "";
    }

    #endregion SM2

    #region Sm4

    /// <summary>
    /// SM4解密
    /// </summary>
    /// <param name="str">密文</param>
    /// <returns>明文</returns>
    public static string Sm4Decrypt(string str,bool iscbc=false)
    {
        if (!string.IsNullOrWhiteSpace(str))// 解密
            return SM4Util.Decrypt(new SM4Util(iscbc) { Data = str });
        return "";
    }

    /// <summary>
    /// SM4加密
    /// </summary>
    /// <param name="str">明文</param>
    /// <returns>密文</returns>
    public static string Sm4Encrypt(string str, bool iscbc = false)
    {
        if (!string.IsNullOrWhiteSpace(str))// 加密
            return SM4Util.Encrypt(new SM4Util(iscbc) { Data = str });
        return "";
    }

	#endregion Sm4

	#region SM3
 
	private static byte[] Encrypt(byte[] data)
	{
		SM3Digest digest = new SM3Digest();
		digest.BlockUpdate(data, 0, data.Length);
		byte[] hash = new byte[digest.GetDigestSize()];
		digest.DoFinal(hash, 0);
		return hash;
	}

	public static string Sm3Encrypt(string data)
	{
		byte[] dataBytes = Encoding.UTF8.GetBytes(data);
		byte[] hashResult = Encrypt(dataBytes);
		return Hex.ToHexString(hashResult);
	}

	#endregion

	#region 混合加密解码
    public static string SM2AndSM4Decrypt(string cipherText)
    {
        //SM2 +SM4 解密 私钥解密
        string[] valueArry = cipherText.Split("@");
        string sm4_key = Sm2Decrypt(valueArry[1]);

        string serializeStr = SM4Util.Decrypt(new SM4Util(false, sm4_key,true) { Data = valueArry[0] });

        return serializeStr;
    }

	#endregion
}
