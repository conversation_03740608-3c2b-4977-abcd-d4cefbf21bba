﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 卡列表CardList
/// </summary>
[SugarTable(TableName = "CardList", TableDescription = "卡列表")]
public class CardList : BaseEntity
{
    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string CardType { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string CardNo { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Tel { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 20)]
    public string Name { get; set; }

    /// <summary>
    /// 出生年月日yyyy-MM-dd
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 10)]
    public string BirthDate { get; set; }

    /// <summary>
    /// 性别 1男  2女  0不限（由字典控制）
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 3)]
    public string Sex { get; set; }

    /// <summary>
    /// 婚姻状况 0未婚  1已婚
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 3)]
    public string Marital { get; set; }

    /// <summary>
    /// 门诊卡
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 50)]
    public string PatientId { get; set; }
}
