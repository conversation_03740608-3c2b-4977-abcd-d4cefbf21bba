namespace PeHubCoreNorm.Business.Input;

/// <summary>
/// 身份认证输入
/// </summary>
public class IdentityAuthenticationInput
{
    /// <summary>
    /// 证件号码
    /// </summary>
    [Required(ErrorMessage = "证件号码不能为空")]
    [StringLength(100, ErrorMessage = "证件号码长度不能超过100个字符")]
    public string IdNumber { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    [Required(ErrorMessage = "电话号码不能为空")]
    [StringLength(20, ErrorMessage = "电话号码长度不能超过20个字符")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入有效的手机号码")]
    public string Tel { get; set; }

    /// <summary>
    /// 短信验证码（可选）
    /// </summary>
    [StringLength(10, ErrorMessage = "验证码长度不能超过10个字符")]
    public string SmsCode { get; set; }

    /// <summary>
    /// 单位编码（可选，用于精确匹配）
    /// </summary>
    [StringLength(8, ErrorMessage = "单位编码长度不能超过8个字符")]
    public string CompanyCode { get; set; }

    /// <summary>
    /// 体检次数（可选，用于精确匹配）
    /// </summary>
    public int? BatchNumber { get; set; }
}

/// <summary>
/// 发送短信验证码输入
/// </summary>
public class SendSmsCodeInput
{
    /// <summary>
    /// 电话号码
    /// </summary>
    [Required(ErrorMessage = "电话号码不能为空")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入有效的手机号码")]
    public string Tel { get; set; }

    /// <summary>
    /// 证件号码（用于验证身份）
    /// </summary>
    [Required(ErrorMessage = "证件号码不能为空")]
    public string IdNumber { get; set; }
}
