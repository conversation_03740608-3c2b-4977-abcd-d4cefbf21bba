﻿using PeHubCoreNorm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PeHubCoreNorm.Business.Entities
{
    [SugarTable(TableName = "UnitTitlePage", TableDescription = "单位封面")]
    public class UnitTitlePage: BaseEntity
    {
        [SugarColumn(IsNullable = false, ColumnName = "CompanyCode", ColumnDescription = "单位编码")]
        public string CompanyCode { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "CompanyTimes", ColumnDescription = "体检次数")]
        public int CompanyTimes { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "ValidityPeriod", ColumnDescription = "体检有效期限")]
        public string ValidityPeriod { get; set; }

        [SugarColumn(IsNullable = false, ColumnName = "Notes", ColumnDescription = "须知")]
        public string Notes { get; set; }


        [SugarColumn(IsNullable = false, ColumnName = "Title", ColumnDescription = "标题")]
        public string Title { get; set; }


        [SugarColumn(IsNullable = false, ColumnName = "ImgData", ColumnDescription = "封面图片数据(Base64)")]
        public string ImgData { get; set; }


        [SugarColumn(IsNullable = false, ColumnName = "FileSize", ColumnDescription = "文件大小")]
        public int FileSize { get; set; }
    }
}




