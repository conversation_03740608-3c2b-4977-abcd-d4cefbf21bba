﻿namespace PeHubCoreNorm.Business;

public class NumberSourceInput
{
    /// <summary>
    /// 日期（年：2025）
    /// </summary>
    public string Year { get; set; } = DateTime.Now.Year.ToString();

    /// <summary>
    /// 日期（月：05）
    /// </summary>
    public string Month { get; set; } = DateTime.Now.Month.ToString();

    /// <summary>
    /// 号源类型编码
    /// </summary>
    [Required(ErrorMessage = "号源类型编码不能为空")]
    public string SourceTypeID { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string UnitCode { get; set; } = string.Empty;

    /// <summary>
    /// 重点项目编码
    /// </summary>
    public string CodeItemCode { get; set; } = string.Empty;
}

public class NumberSourceByDayInput : NumberSourceInput
{
    /// <summary>
    /// 日期（天/日：01）
    /// </summary>
    public string Day { get; set; } = DateTime.Now.Day.ToString();
}

public class EditNumberSourceInput
{
    /// <summary>
    /// 号源类型编码
    /// </summary>
    [Required(ErrorMessage = "号源类型编码不能为空")]
    public string SourceTypeID { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    /// 时段编码
    /// </summary>
    public string TimeSlotID { get; set; }

    /// <summary>
    /// 是否休假
    /// </summary>
    public string Statu { get; set; }

    /// <summary>
    /// 号源总量
    /// </summary>
    public int TotalCapacity { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string ComanyCode { get; set; } = string.Empty;

    /// <summary>
    /// 重点项目编码
    /// </summary>
    public string CodeItemCode { get; set; } = string.Empty;


}

public class DeleteNumberSourceInput : EditNumberSourceInput
{

}

public class SetNumberSourceVacationInput
{
    /// <summary>
    /// 号源类型编码
    /// </summary>
    [Required(ErrorMessage = "号源类型编码不能为空")]
    public string SourceTypeID { get; set; }

    /// <summary>
    /// 开始时间（yyyy-MM-dd）
    /// </summary>
    [Required(ErrorMessage = "开始时间不能为空")]
    public string StartTime { get; set; }

    /// <summary>
    /// 结束时间（yyyy-MM-dd）
    /// </summary>
    [Required(ErrorMessage = "结束时间不能为空")]
    public string EndTime { get; set; }

    /// <summary>
    /// 时段编码
    /// </summary>
    [Required(ErrorMessage = "时段编码不能为空")]
    public string TimeSlotID { get; set; }

    /// <summary>
    /// 是否休假(T 是 F 否)
    /// </summary>
    [Required(ErrorMessage = "是否休假不能为空")]
    public string Statu { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string ComanyCode { get; set; } = string.Empty;

    /// <summary>
    /// 重点项目编码
    /// </summary>
    public string CodeItemCode { get; set; } = string.Empty;
}


