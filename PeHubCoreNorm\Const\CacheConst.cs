﻿namespace PeHubCoreNorm;

public class CacheConst
{
    public const string Cache_Prefix_Web = "PeHubCoreNormWebApi:";

    public const string Cache_UserToken = Cache_Prefix_Web + "UserToken:";

    /// <summary>
    ///     字典表缓存Key
    /// </summary>
    public const string Cache_DevDict = Cache_Prefix_Web + "DevDict";

    /// <summary>
    ///     登录验证码缓存Key
    /// </summary>
    public const string Cache_Captcha = Cache_Prefix_Web + "Captcha:";

    /// <summary>
    ///     系统配置表缓存Key
    /// </summary>
    public const string Cache_DevConfig = Cache_Prefix_Web + "DevConfig:";

    /// <summary>
    ///     Hash 用户对应角色->对应API权限 [编辑角色删除整个Key,编辑用户删除单个field]
    /// </summary>
    public const string Cache_UserRelation = Cache_Prefix_Web + "UserRelation";

    /// <summary>
    ///     Hash 用户对应菜单，独立使用
    /// </summary>
    public const string Cache_UserRelationMenu = Cache_Prefix_Web + "UserRelationMenu";


    /// <summary>
    ///     Hash 用户对应菜单AuthCode，独立使用
    /// </summary>
    public const string Cache_AuthCode = Cache_Prefix_Web + "MenuAuthCode";

    /// <summary>
    ///     Hash Field
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public static string Field_UserHasApi(string userId)
    {
        return "API_" + userId;
    }

    /// <summary>
    ///     Hash Field
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public static string Field_UserHasMenu(string userId)
    {
        return "MENU_" + userId;
    }
}