﻿using Microsoft.Extensions.Logging;

namespace PeHubCoreNorm.Template;

/// <inheritdoc />
public class StudentService : BizDbRepository<Student>, IStudentService
{
    private readonly ILogger<StudentService> _logger;

	/// <inheritdoc />
	public StudentService(ILogger<StudentService> logger)
    {
        _logger = logger;
	}

    /// <inheritdoc />
    public async Task<SqlSugarPagedList<StudentOutPut>> Page(StudentPageInput input)
    {
		var query = Context.Queryable<Student>()
            //.WhereIF(!string.IsNullOrEmpty(input.SearchKey), it => it.Name.Contains(input.SearchKey))//根据关键字查询
            .OrderByIF(!string.IsNullOrEmpty(input.SortField), $"{input.SortField} {input.SortOrder}")
            .Select<StudentOutPut>();

        var pageInfo = await query.ToPagedListAsync(input.pageNum, input.pageSize); //分页
        return pageInfo;
    }

    /// <inheritdoc />
    public async Task Add(StudentAddInput input)
    {
        var student = input.Adapt<Student>(); //实体转换
        await CheckInput(student); //检查参数
        await InsertAsync(student); //插入数据
    }

    /// <inheritdoc />
    public async Task Edit(StudentEditInput input)
    {
        var student = input.Adapt<Student>(); //实体转换
        await CheckInput(student); //检查参数
        await UpdateAsync(student); //更新数据
    }

    /// <inheritdoc />
    public async Task Delete(List<BaseIdInput> input)
    {
        //获取所有ID
        var ids = input.Select(it => it.Id).ToList();
        if (ids.Count > 0) await DeleteByIdsAsync(ids.Cast<object>().ToArray()); //删除数据
    }

    /// <inheritdoc />
    public async Task<Student> Detail(BaseIdInput input)
    {
        var student = await GetFirstAsync(it => it.Id == input.Id);
        return student;
    }

    /// <inheritdoc />
    private async Task CheckInput(Student student)
    {
        await Task.CompletedTask;
    }
}