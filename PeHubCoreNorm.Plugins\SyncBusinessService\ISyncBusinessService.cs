﻿namespace PeHubCoreNorm.Services.SyncBusinessService;

/// <summary>
/// 获取体检业务的接口
/// </summary>
public interface ISyncBusinessService : ITransient
{
    #region 获取体检单位业务
    /// <summary>
    /// 获取体检单位信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompany();

    /// <summary>
    /// 获取体检单位次数信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyTimes();

    /// <summary>
    /// 获取体检单位套餐信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyCluster();

    /// <summary>
    /// 获取体检单位套餐对应的组合信息
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetPeCompanyClusterComb();
    #endregion

    #region 获取体检套餐及对应组合
    /// <summary>
    /// 获取体检套餐及对应组合
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetClusterAndCombs();
    #endregion

    #region 获取体检组合
    /// <summary>
    /// 获取体检组合
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetCodeItemComb();
    #endregion

    #region 获取体检项目分类
    /// <summary>
    /// 获取体检项目分类
    /// </summary>
    /// <returns></returns>
    Task<ResponseResult> GetCodeItemCls();
    #endregion
}