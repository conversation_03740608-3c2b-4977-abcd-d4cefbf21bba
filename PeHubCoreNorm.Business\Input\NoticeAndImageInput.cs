﻿
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Business;


public class CarouselImageInput :BasePageInput
{
    public string imagePage { get; set; } = "";
}

public class CarouselImageByPageInput
{
    public string imagePage { get; set; }
}


#region 体检须知

public class NoticeInput : BasePageInput
{
    public string id { get; set; } = "";
    public string typeCode { get; set; } = "";
}
public class UpdateStatusInput
{
    public string id { get; set; } = "";
    public string status { get; set; } = "";
}
public class UpdateNoticeInput: UpdateStatusInput
{
    public string content { get; set; } = "";
    public string typeCode { get; set; } = "";
}
public class NoticesTypeCodeInput
{
    public string typeCode { get; set; }
}
#endregion

#region 资讯
public class RealTimeInfoInput : BasePageInput
{
    public string title { get; set; } = "";
    public string typeCode { get; set; } = "";
}
public class UpdateContentInput
{
    public string id { get; set; } = "";
    public string content { get; set; } = "";
}

public class UpdateRealTimeInfosInput: UpdateStatusInput
{
    public string title { get; set; } 
    public string typeCode { get; set; }
    public string diagramUrl { get; set; }
}

public class RealTimeInfosHomeInput
{
    public string typeCode { get; set; } = "";
    public int num { get; set; } = 5;
}

#endregion

#region 共用Input

public class ShareDataInput
{
    public string code { get; set; } = "";
    public string kw { get; set; } = "";
}



#endregion


public class IndexInfoInput : RealTimeInfosHomeInput
{
    public string imagePage { get; set; }
}



