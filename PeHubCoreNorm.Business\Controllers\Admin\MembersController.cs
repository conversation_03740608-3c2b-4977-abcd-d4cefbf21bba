﻿using Microsoft.AspNetCore.Authorization;
namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 客户信息管理控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class MembersController : BaseControllerAuthorize
{
    private readonly IMembersService _membersService;

    public MembersController(IMembersService membersService)
    {
        _membersService = membersService;
    }


    #region Members表

    /// <summary>
    /// 写入或更新用户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[ProducesResponseType(typeof(bool), 200)]
    //[HttpPost("InputMembers")]
    //[ActionPermission(ActionType.Button, "写入或更新用户信息")]
    //public async Task<dynamic> InputMembers([FromBody] Members input)
    //{
    //   return await _membersService.InsertOrUpdateMembers(input);
    //}


    /// <summary>
    /// 条件筛选用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetMembersList")]
    [ActionPermission(ActionType.Query, "条件筛选用户")]
    public async Task<dynamic> GetMembersList([FromBody] MembersInput input)
    {
        return await _membersService.GetMembersList(input);
    }

    [ProducesResponseType(typeof(bool), 200)]
    [HttpPost("UpdateMembersName")]
    [ActionPermission(ActionType.Button, "更新用户名称")]
    public async Task<dynamic> UpdateMembersName([FromBody] MembersNameInput input)
    {
        return await _membersService.UpdateMembersName(input);
    }

    //[HttpPost("CreateCards")]
    //[ActionPermission(ActionType.Button, "建卡")]
    //public async Task<dynamic> CreateCards([FromBody] Members input)
    //{
    //    return await _membersService.GetMembersByWId(input.wId);
    //}

    /// <summary>
    /// 新建用户，并关联openid (检查用户、绑定关系是否已存在)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost("CardBingsOpenid")]
    //[ActionPermission(ActionType.Button, "新建用户和关联openid")]
    //public async Task<dynamic> CardBingsOpenid([FromBody] MembersCardsInput input)
    //{
    //    return await _membersService.AssociatedUsers(input);
    //}

    #endregion

    #region 短信


    #endregion





}
