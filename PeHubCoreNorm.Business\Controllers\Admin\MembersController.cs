﻿using Microsoft.AspNetCore.Authorization;
namespace PeHubCoreNorm.Business.Admin;

/// <summary>
/// 客户信息管理控制器
/// </summary>
[ApiExplorerSettings(GroupName = "Admin")]
[Route("/admin/[controller]")]
public class MembersController : BaseControllerAuthorize
{
    private readonly IMembersService _membersService;

    public MembersController(IMembersService membersService)
    {
        _membersService = membersService;
    }


    #region Members表

    /// <summary>
    /// 条件筛选用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetMembersList")]
    [ActionPermission(ActionType.Query, "条件筛选用户")]
    public async Task<dynamic> GetMembersList([FromBody] MembersInput input)
    {
        return await _membersService.GetMembersList(input);
    }

    /// <summary>
    /// 更新用户名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ProducesResponseType(typeof(bool), 200)]
    [HttpPost("UpdateMembersName")]
    [ActionPermission(ActionType.Button, "更新用户名称")]
    public async Task<dynamic> UpdateMembersName([FromBody] MembersNameInput input)
    {
        return await _membersService.UpdateMembersName(input);
    }

    #endregion

    #region 健康卡
    /// <summary>
    /// 获取卡列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("GetCardListPage")]
    [ActionPermission(ActionType.Query, "获取卡列表")]
    public async Task<dynamic> GetCardListPage([FromBody] CardListInput input)
    {
        return await _membersService.GetCardList(input);
    }

    /// <summary>
	/// 修改卡的信息（Name、Tel、BirthDate、Sex、Marital、PatientId、CardType、CardNo）
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	[HttpPost("UpdateCardsInfo")]
    [ActionPermission(ActionType.Button, "修改卡的信息")]
    public async Task<dynamic> UpdateCardsInfo(UpdateCardsInfoInput input)
    {
        CardList card = new CardList()
        {
            CardType = input.cardType,
            CardNo = input.cardNo,
            Tel = input.tel,
            Name = input.name,
            BirthDate = input.birthDate,
            Sex = input.sex,
            Marital = input.marital,
            PatientId= input.patientId,
            Id = input.id,
        };

        //根据卡id获取卡信息
        var item = await _membersService.GetCardById(card.Id);
        if (item == null)
        {
            return Unify.SetError("更新失败:该卡不存在！");
        }
        //更新
        return _membersService.UpdateCard(card);
    }

    [HttpPost("DeleteCardList")]
    [ActionPermission(ActionType.Button, "删除健康卡")]
    public async Task<bool> DeleteCardList([FromBody] BaseIdsInput input)
    {
        return await Task.Factory.StartNew(() =>
        {
            return _membersService.DeleteCardList(input.Ids);
        });
    }

    #endregion




}
