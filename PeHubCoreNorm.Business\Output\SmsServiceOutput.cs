using PeHubCoreNorm.Business.Input;

namespace PeHubCoreNorm.Business.Output;

/// <summary>
/// 短信发送结果输出
/// </summary>
public class SmsResult
{
    /// <summary>
    /// 是否发送成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 短信记录ID
    /// </summary>
    public string SmsRecordId { get; set; }

    /// <summary>
    /// 第三方平台返回的请求ID
    /// </summary>
    public string RequestId { get; set; }

    /// <summary>
    /// 第三方平台返回的消息ID
    /// </summary>
    public string MessageId { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SendTime { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    public string ErrorCode { get; set; }

    /// <summary>
    /// 错误详情
    /// </summary>
    public string ErrorDetail { get; set; }

    /// <summary>
    /// 消耗的短信条数
    /// </summary>
    public int SmsCount { get; set; } = 1;
}

/// <summary>
/// 批量短信发送结果输出
/// </summary>
public class BatchSmsResult
{
    /// <summary>
    /// 总发送数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public decimal SuccessRate => TotalCount > 0 ? (decimal)SuccessCount / TotalCount * 100 : 0;

    /// <summary>
    /// 批次ID
    /// </summary>
    public string BatchId { get; set; }

    /// <summary>
    /// 详细结果列表
    /// </summary>
    public List<SmsResult> Results { get; set; } = new List<SmsResult>();

    /// <summary>
    /// 失败的手机号码列表
    /// </summary>
    public List<string> FailedPhoneNumbers { get; set; } = new List<string>();

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds => (long)(EndTime - StartTime).TotalMilliseconds;
}

/// <summary>
/// 验证码发送结果输出
/// </summary>
public class VerificationCodeResult
{
    /// <summary>
    /// 是否发送成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 验证码（仅测试环境返回）
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 验证码有效期（分钟）
    /// </summary>
    public int ValidMinutes { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// 短信记录ID
    /// </summary>
    public string SmsRecordId { get; set; }
}

/// <summary>
/// 验证码验证结果输出
/// </summary>
public class CodeVerifyResult
{
    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 剩余尝试次数
    /// </summary>
    public int RemainingAttempts { get; set; }

    /// <summary>
    /// 是否已锁定
    /// </summary>
    public bool IsLocked { get; set; }

    /// <summary>
    /// 锁定到期时间
    /// </summary>
    public DateTime? LockExpireTime { get; set; }
}

/// <summary>
/// 短信发送记录输出
/// </summary>
public class SmsRecordOutput
{
    /// <summary>
    /// 记录ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 短信内容
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 短信类型
    /// </summary>
    public SmsType SmsType { get; set; }

    /// <summary>
    /// 短信类型描述
    /// </summary>
    public string SmsTypeText => SmsType.ToString();

    /// <summary>
    /// 发送状态
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText => Status switch
    {
        SmsStatus.Pending => "待发送",
        SmsStatus.Sending => "发送中",
        SmsStatus.Success => "发送成功",
        SmsStatus.Failed => "发送失败",
        SmsStatus.Cancelled => "已取消",
        _ => "未知"
    };

    /// <summary>
    /// 模板编码
    /// </summary>
    public string TemplateCode { get; set; }

    /// <summary>
    /// 短信签名
    /// </summary>
    public string SignName { get; set; }

    /// <summary>
    /// 第三方平台返回的请求ID
    /// </summary>
    public string RequestId { get; set; }

    /// <summary>
    /// 第三方平台返回的消息ID
    /// </summary>
    public string MessageId { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    public string ErrorCode { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; }

    /// <summary>
    /// 短信条数
    /// </summary>
    public int SmsCount { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime? SendTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 批次ID
    /// </summary>
    public string BatchId { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; }
}

/// <summary>
/// 短信统计输出
/// </summary>
public class SmsStatisticsOutput
{
    /// <summary>
    /// 统计日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 总发送数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public decimal SuccessRate => TotalCount > 0 ? (decimal)SuccessCount / TotalCount * 100 : 0;

    /// <summary>
    /// 验证码短信数量
    /// </summary>
    public int VerificationCount { get; set; }

    /// <summary>
    /// 通知短信数量
    /// </summary>
    public int NotificationCount { get; set; }

    /// <summary>
    /// 营销短信数量
    /// </summary>
    public int MarketingCount { get; set; }

    /// <summary>
    /// 系统短信数量
    /// </summary>
    public int SystemCount { get; set; }

    /// <summary>
    /// 总消耗短信条数
    /// </summary>
    public int TotalSmsCount { get; set; }
}
