﻿using DotNetCore.CAP;
using PeHubCoreNorm.RBAC;
using static Npgsql.Replication.PgOutput.Messages.RelationMessage;
using System.Linq.Expressions;
using System;

namespace PeHubCoreNorm.Business;

/// <summary>
/// 客户接口
/// </summary>
public class MembersService : BizDbRepository<Members>, IMembersService
{
	private readonly ICacheService _cacheService;
	private readonly ICapPublisher _capPublisher;
	private readonly ISysOrgService _sysOrgService;
	 


	public MembersService(ICacheService cacheService,ICapPublisher capPublisher, ISysOrgService sysOrgService)
	{
		_cacheService = cacheService;
		_capPublisher = capPublisher;
        _sysOrgService = sysOrgService;
	}



	#region Members表

	/// <summary>
	/// 获取用户信息(根据name、wId、tel、cardNo获取）
	/// </summary>
	/// <param name="input"></param>
	/// <returns></returns>
	public async Task<SqlSugarPagedList<Members>> GetMembersList(MembersInput input)
    {
        return await Context.Queryable<Members>()
            .WhereIF(!string.IsNullOrEmpty(input.name), x => x.Name.Contains(input.name))
            .WhereIF(!string.IsNullOrEmpty(input.wId), x => x.WId.Contains(input.wId))
             .WhereIF(!string.IsNullOrEmpty(input.tel), x => x.Tel.Contains(input.tel))
              .WhereIF(!string.IsNullOrEmpty(input.cardNo), x => x.CardNo.Contains(input.cardNo))
             .ToPagedListAsync(input.pageNum, input.pageSize);
    }

    /// <summary>
    /// 根据id更新名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> UpdateMembersName(MembersNameInput input)
    {
        Context.Updateable<Members>().SetColumns(it => it.Name == input.name).Where(it => it.Id == input.id ).ExecuteCommand();
        return true;
    }
   
    #endregion

    #region 短信验证码

    /// <summary>
    /// 根据电话获取短信记录
    /// </summary>
    /// <param name="tel"></param>
    /// <returns></returns>
    public VerificationMessage GetVerificationMessageByTel(string tel)
    {
        return Context.Queryable<VerificationMessage>().First(x => x.Tel == tel);
    }

    /// <summary>
    /// 写入验证码短信
    /// </summary>
    /// <param name="verificationMessage"></param>
    /// <returns></returns>
    public bool InsertVerificationMessage(VerificationMessage verificationMessage)
    {
        Context.Insertable<VerificationMessage>(verificationMessage).ExecuteCommandAsync();
        return true;
    }

    /// <summary>
    /// 更新验证码短信
    /// </summary>
    /// <param name="verificationMessage"></param>
    /// <returns></returns>
    public bool UpdateVerificationMessage(VerificationMessage verificationMessage)
    {
        Context.Updateable<VerificationMessage>(verificationMessage).ExecuteCommand();
        return true;
    }

    #endregion

    #region 短信接口响应

    /// <summary>
    /// 根据时间获取短信接口响应记录
    /// </summary>
    /// <param name="startTime"></param>
    /// <param name="endTime"></param>
    /// <returns></returns>
    public List<SmsRecord> GetSmsRecordByDateTime(DateTime startTime, DateTime endTime)
    {
        var start_time = startTime.Date;
        var end_time = endTime.AddDays(1).Date;
        return Context.Queryable<SmsRecord>().Where(x => x.CreateTime >= start_time && x.CreateTime < end_time).ToList();
    }

    /// <summary>
    /// 写入短信发送响应
    /// </summary>
    /// <param name="smsRecord"></param>
    /// <returns></returns>
    public bool InsertSmsRecord(SmsRecord smsRecord)
    {
        Context.Insertable<SmsRecord>(smsRecord).ExecuteCommand();
        return true;
    }



	#endregion


	#region 登录验证码和验证
    /// <summary>
    /// 获取验证码
    /// </summary>
    /// <returns></returns>
	public async Task<dynamic> GetCaptchaInfo()
	{
		//生成验证码
		var captchaInfo = CaptchaUtil.CreateCaptcha(CaptchaType.ARITH, 4, 100, 38);

		//生成请求号，并将验证码放入缓存
		var reqNo = IDUtils.GetId();
		_cacheService.Set("AppWeb" + CacheConst.Cache_Captcha + reqNo, captchaInfo.Code, TimeSpan.FromMinutes(5));
		//返回验证码和请求号
		return new  { ValidCodeBase64 = captchaInfo.Base64Str, ValidCodeReqNo = reqNo };
	}


	/// <summary>
	///     校验验证码方法
	/// </summary>
	/// <param name="validCode">验证码</param>
	/// <param name="validCodeReqNo">请求号</param>
	/// <param name="isDelete">是否从Redis删除</param>
	public async Task ValidValidCode(string validCode, string validCodeReqNo, bool isDelete = true)
	{
		var key = "AppWeb" + CacheConst.Cache_Captcha + validCodeReqNo; //获取验证码Key值
		var code = _cacheService.Get<string>(key); //从缓存拿数据
        if (isDelete)
        {
            _cacheService.Remove(key); //删除验证码
        }

		if (code != null && validCode != null) //如果有
		{
			//验证码如果不匹配直接抛错误，这里忽略大小写
			if (validCode.ToLower() != code.ToLower()) throw new CustomException("验证码错误");
		}
		else
		{
			//抛出验证码不能为空
			throw new CustomException("验证码不能为空");
		}
	}

	#endregion

	#region appweb登录授权

	/// <summary>
	/// 根据wId获取用户选项
	/// </summary>
	/// <param name="wId"></param>
	/// <param name="orgkey"></param>
	/// <returns></returns>
	public async Task<AuthMember> GetMembersByWId(string wId,string orgkey)
	{
		//orgkey
		UserManager.setOrgCodeValue(orgkey);

        var user= await Context.Queryable<Members>().FirstAsync(x => x.WId == wId);

        if (user == null)
        {
			return Unify.SetError("已过期请重新登陆!");
		}

		//引入设备概念,每次登陆都重新颁发Token
		var token = JwtUtils.GenerateToken(new Dictionary<string, string>
		{
			[ClaimConst.UserId] = user.Id,
			[ClaimConst.Account] = user.CardNo,
			[ClaimConst.Tel] = user.Tel,
			[ClaimConst.Name] = user.Name,
			[ClaimConst.SuperAdmin] = "N",
			[ClaimConst.Device] ="appweb",
			[ClaimConst.OrgList] = orgkey
		});

		App.HttpContext.Response.Headers["access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["x-access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["Access-Control-Expose-Headers"] = "access-token,x-access-token";

        var orgInfo =await _sysOrgService.GetSysOrgByCode(orgkey);

        return new AuthMember
        {
            token = token["accessToken"].ToString(),
            CardNo = user.CardNo,
            Name = user.Name,
            orgKey = orgkey,
            Tel = user.Tel,
            WId = user.WId,
            Id = user.Id,
            orgInfo = orgInfo
        };
	}


	public async Task<AuthMember> authlogin(AuthFormInput loginInput)
	{
		await ValidValidCode(loginInput.validCode,loginInput.validCodeReqNo); //检查验证码

        if (string.IsNullOrEmpty(loginInput.orgKey))
        {
			return Unify.SetError("授权失败!医院编码为空");
		}
        UserManager.setOrgCodeValue(loginInput.orgKey);

        var user = await GetFirstAsync(x => x.CardNo == loginInput.CardNo && x.Tel == loginInput.Tel);

        if (user == null)
        {
            user = loginInput.Adapt<Members>();

            user.WId = PwdUtils.GetMD5(user.CardNo + PeHubCoreNormConst.WID + user.Tel);
            if (!await InsertAsync(user))
            {
				return Unify.SetError("授权失败!");
			}
        }
 

		//引入设备概念,每次登陆都重新颁发Token
		var token = JwtUtils.GenerateToken(new Dictionary<string, string>
		{
			[ClaimConst.UserId] = user.Id,
			[ClaimConst.Account] = user.CardNo,
			[ClaimConst.Tel] = user.Tel,
			[ClaimConst.Name] = user.Name,
			[ClaimConst.SuperAdmin] = "N",
			[ClaimConst.Device] = loginInput.Device,
			[ClaimConst.OrgList] = loginInput.orgKey
		});

		App.HttpContext.Response.Headers["access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["x-access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["Access-Control-Expose-Headers"] = "access-token,x-access-token";

		var orgInfo = await _sysOrgService.GetSysOrgByCode(loginInput.orgKey);

		return new AuthMember
		{
			token = token["accessToken"].ToString(),
			CardNo = user.CardNo,
			Name = user.Name,
			orgKey = loginInput.orgKey,
			Tel=user.Tel,
            WId=user.WId,
            Id=user.Id,
            orgInfo = orgInfo
		};
	}


	public async Task<AuthMember> authloginOther(AuthFormInput loginInput)
	{
 

		if (string.IsNullOrEmpty(loginInput.orgKey))
		{
			return Unify.SetError("授权失败!医院编码为空");
		}
		UserManager.setOrgCodeValue(loginInput.orgKey);

		var user = await GetFirstAsync(x => x.CardNo == loginInput.CardNo && x.Tel == loginInput.Tel);

		if (user == null)
		{
			user = loginInput.Adapt<Members>();
			if (!await InsertAsync(user))
			{
				return Unify.SetError("授权失败!");
			}
		}
		else
		{
			if (!await UpdateAsync(it => new Members
			{
				WId = user.WId,
				Name = user.Name,
				CardNo = user.CardNo,
				Tel = user.Tel,
			}, it => it.Id == user.Id))
			{
				return Unify.SetError("更新授权失败!");
			}
		}


		//引入设备概念,每次登陆都重新颁发Token
		var token = JwtUtils.GenerateToken(new Dictionary<string, string>
		{
			[ClaimConst.UserId] = user.Id,
			[ClaimConst.Account] = user.CardNo,
			[ClaimConst.Tel] = user.Tel,
			[ClaimConst.Name] = user.Name,
			[ClaimConst.SuperAdmin] = "N",
			[ClaimConst.Device] = loginInput.Device,
			[ClaimConst.OrgList] = loginInput.orgKey
		});

		App.HttpContext.Response.Headers["access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["x-access-token"] = token["accessToken"].ToString();
		App.HttpContext.Response.Headers["Access-Control-Expose-Headers"] = "access-token,x-access-token";

		var orgInfo = await _sysOrgService.GetSysOrgByCode(loginInput.orgKey);

		return new AuthMember
		{
			token = token["accessToken"].ToString(),
			CardNo = user.CardNo,
			Name = user.Name,
			orgKey = loginInput.orgKey,
			Tel = user.Tel,
			WId = user.WId,
			Id = user.Id,
			orgInfo = orgInfo
		};
	}
	#endregion


	#region 卡

	/// <summary>
	/// 获取用户id绑定的卡
	/// </summary>
	/// <param name="membersId"></param>
	/// <returns></returns>
	public async Task<List<CardList>> GetCardsById(string membersId)
    {
        return Context.Queryable<MapMembersIdCardId>()
             .LeftJoin<CardList>((mapMem, card) => mapMem.MembersId == membersId && mapMem.CardId == card.Id)
             .Select((mapMem, card) => card).ToList();
    }

    /// <summary>
    /// 根据卡id获取卡信息
    /// </summary>
    /// <param name="cardId"></param>
    /// <returns></returns>
    public async Task<CardList> GetCardById(string cardId)
    {
        return Context.Queryable<CardList>().First(x=>x.Id == cardId);
    }

    /// <summary>
    /// 根据卡id更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId
    /// </summary>
    /// <param name="card"></param>
    /// <returns></returns>
    public bool UpdateCard(CardList card)
    {
        var db= Context.Updateable<CardList>().SetColumns(it =>
            new CardList()
            {
                Name = card.Name,
                Tel = card.Tel,
                BirthDate = card.BirthDate,
                Sex = card.Sex,
                Marital = card.Marital,
                PatientId = card.PatientId
            }).Where(it => it.Id == card.Id).ExecuteCommand();
        return db==1;
    }

    /// <summary>
    /// 获取用户id已绑定多少张卡的数量
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public int GetBingsCardsCount(string userId)
    {
        return Context.Queryable<MapMembersIdCardId>().Where(x=>x.MembersId == userId).Count();
    }


    /// <summary>
    /// 插入卡，如果卡存在则更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId，返回id
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<string> EditCardsReturnId(CardList input)
    {
        var card = await Context.Queryable<CardList>().FirstAsync(x => x.CardNo == input.CardNo && x.Name == input.Name && x.CardType == input.CardType);
        if (card == null)
        {
            card = Context.Insertable<CardList>(input).ExecuteReturnEntity();
        }
        else
        {
            //更新卡的Name、Tel、BirthDate、Sex、Marital、PatientId
            card.Name = input.Name;
            card.Tel = input.Tel;
            card.BirthDate = input.BirthDate;
            card.Sex = input.Sex;
            card.Marital = input.Marital;
            card.PatientId = input.PatientId;
            Context.Updateable<CardList>(input).ExecuteCommand();
        }
        return card.Id;
    }

    /// <summary>
    /// 绑定卡，如果已存在则返回false
    /// </summary>
    /// <param name="membersId">用户id</param>
    /// <param name="cardId">卡id</param>
    /// <returns></returns>
    public bool MembersBingsCards(string membersId, string cardId)
    {
        var x = Context.Storageable<MapMembersIdCardId>(new MapMembersIdCardId()
        {
            MembersId = membersId,
            CardId = cardId
        }).WhereColumns(it => it.MembersId == membersId && it.CardId == cardId).ToStorage();
        return x.AsInsertable.ExecuteCommand() == 1;
    }


    /// <summary>
    /// 用户id解绑卡
    /// </summary>
    /// <param name="membersId"></param>
    /// <param name="cardId"></param>
    /// <returns></returns>
    public async Task<bool> UnbindTheCard(string membersId, string cardId)
    {
        await Context.Deleteable<MapMembersIdCardId>().Where(x => x.MembersId == membersId && x.CardId == cardId).ExecuteCommandAsync();
        return true;
    }




    #endregion




}
