﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PeHubCoreNorm.Business;

namespace PeHubCoreNorm.Job;

public class CompanySyncJob : BackgroundService, ITransient
{
    private readonly ILogger<CombSyncJob> _logger;
    public CompanySyncJob(ILogger<CombSyncJob> logger)
    {
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
			if (!App.Get<bool>("Hangfire:enabled"))
			{
				return;
			}
			UserManager.setOrgCodeValue("H0002");
			var _syncService = App.ServiceProvider.GetService<ISyncService>();

			RecurringJob.AddOrUpdate("companySyncJob_SD003",
	         () => _syncService.SyncCompany("H0002"), "0 0 0,13 * * ?");
            //UserManager.setOrgCodeValue("SD004");
            //RecurringJob.AddOrUpdate("combSyncJob_SD004", () => _syncService.SyncCodeItemComb(), "*/50 * * * * *");

            // 保持服务运行(关键!)
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "combSyncJob作业报错了");
        }
    }
}
