﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 加项包入参
/// </summary>
public class AddPackageInput
{
    /// <summary>
    /// 关键字
    /// </summary>
    public string SearchKey { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 团体标识
    /// </summary>
    public string IsCompany { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }
}

/// <summary>
/// 保存加项包及对应
/// </summary>
public class  SaveAddPackageAndDetail
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 加项包编码
    /// </summary>
    public string AddPackageCode { get; set; }

    /// <summary>
    /// 加项包名称
    /// </summary>
    public string AddPackageName { get; set; }

    /// <summary>
    /// 性别(0:通用 1:男 2:女)
    /// </summary>
    public string Gender { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string Introduce { get; set; }

    /// <summary>
    /// 团体标识
    /// </summary>
    public string IsCompany { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    public string CompanyCode { get; set; }

    /// <summary>
    /// 可选项目数量
    /// </summary>
    public int OptionalQuantity { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 状态(启用/禁用)
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 加项包明细数据
    /// </summary>
    public List<AddPkgDetailData> DetailCombs { get; set; }
}

/// <summary>
/// 更新加项包明细
/// </summary>
public class AddPkgDetailData
{
    /// <summary>
    /// 加项包编码
    /// </summary>
    public string AddPackageCode { get; set; }

    /// <summary>
    /// 组合编码
    /// </summary>
    public string CombCode { get; set; }
}