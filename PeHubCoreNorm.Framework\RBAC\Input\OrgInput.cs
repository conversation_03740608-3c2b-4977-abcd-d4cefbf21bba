﻿namespace PeHubCoreNorm.RBAC;

/// <summary>
///    分页查询参数
/// </summary>
public class OrgPageInput : BasePageInput
{
	/// <summary>
	///     动态查询条件
	/// </summary>
	public Expressionable<SysOrg> Expression { get; set; }
}

/// <summary>
///     添加参数
/// </summary>
public class OrgAddInput : SysOrg
{
}

 

/// <summary>
///  编辑用户参数
/// </summary>
public class OrgEditInput : OrgAddInput
{
	/// <summary>
	///     Id
	/// </summary>
	[Required(ErrorMessage = "Id不能为空")]
    public override string Id { get; set; }
}