﻿namespace PeHubCoreNorm.Business;

/// <summary>
/// 单位业务接口
/// </summary>
public interface ICompanyService : ITransient, ITenantDBTransient
{
    /// <summary>
    /// 单位信息查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<CompanyOutput>> GetCompanyList(CompanyPageInput input);

    /// <summary>
    /// 单位体检次数查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <returns></returns>
    Task<CompanyTimeOutput[]> GetCompanyTimes(string companyCode);

    /// <summary>
    /// 单位体检套餐查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="companyTimes"></param>
    /// <returns></returns>
    Task<CompanyClusterOutput[]> GetCompanyCluster(string companyCode, int companyTimes);

    /// <summary>
    /// 单位体检套餐控制查询
    /// </summary>
    /// <param name="companyCode"></param>
    /// <param name="clusterCode"></param>
    /// <returns></returns>
    Task<CompanyClusterControlOutput> GetCompanyClusterControl(string companyCode, string clusterCode);

    /// <summary>
    /// 保存单位体检套餐控制
    /// </summary>
    /// <param name="clusterControl"></param>
    /// <returns></returns>
    Task<bool> SaveCompanyClusterControl(CompanyClusterControl clusterControl);

    /// <summary>
    /// 获取单位套餐对应的组合
    /// </summary>
    /// <param name="clusterInput"></param>
    /// <returns></returns>
    Task<MapCompanyClusterCombOutput[]> GetMapCompanyClusterComb(MapCompanyClusterCombInput clusterInput);
}